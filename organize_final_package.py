#!/usr/bin/env python3
"""
تنظيم الحزمة النهائية للبرنامج
Final Package Organization Script
"""

import os
import shutil
from pathlib import Path
import time

def print_status(message, status="INFO"):
    """طباعة رسالة حالة ملونة"""
    colors = {
        "INFO": "\033[94m",     # أزرق
        "SUCCESS": "\033[92m",  # أخضر
        "WARNING": "\033[93m",  # أصفر
        "ERROR": "\033[91m",    # أحمر
        "RESET": "\033[0m"      # إعادة تعيين
    }
    
    color = colors.get(status, colors["INFO"])
    reset = colors["RESET"]
    print(f"{color}[{status}] {message}{reset}")

def create_final_package():
    """إنشاء الحزمة النهائية المنظمة"""
    print_status("إنشاء الحزمة النهائية المنظمة...")
    
    # مسارات المجلدات
    dist_dir = Path("dist")
    final_package_dir = dist_dir / "نظام_إدارة_المخازن_النهائي"
    
    # إنشاء المجلد النهائي
    if final_package_dir.exists():
        shutil.rmtree(final_package_dir)
    final_package_dir.mkdir(parents=True)
    
    # نسخ ملف exe الرئيسي
    exe_file = dist_dir / "نظام_إدارة_المخازن_كامل.exe"
    if exe_file.exists():
        shutil.copy2(exe_file, final_package_dir / "نظام_إدارة_المخازن.exe")
        print_status("تم نسخ ملف البرنامج الرئيسي", "SUCCESS")
    
    # إنشاء مجلد البيانات
    data_dir = final_package_dir / "البيانات"
    data_dir.mkdir()
    
    # نسخ قاعدة البيانات إذا كانت موجودة
    if Path("stores_management.db").exists():
        shutil.copy2("stores_management.db", data_dir / "stores_management.db")
        print_status("تم نسخ قاعدة البيانات", "SUCCESS")
    
    # نسخ ملف الإعدادات إذا كان موجوداً
    if Path("settings.json").exists():
        shutil.copy2("settings.json", data_dir / "settings.json")
        print_status("تم نسخ ملف الإعدادات", "SUCCESS")
    
    # إنشاء مجلدات فرعية
    subdirs = ["التقارير", "النسخ_الاحتياطية", "الصادرات", "السجلات"]
    for subdir in subdirs:
        (final_package_dir / subdir).mkdir()
    
    # إنشاء ملف README مفصل
    readme_content = """
# نظام إدارة المخازن والمستودعات
## الإصدار 1.1 - النسخة الكاملة

### 🚀 تعليمات التشغيل السريع:
1. انقر نقراً مزدوجاً على ملف "نظام_إدارة_المخازن.exe"
2. انتظر حتى يتم تحميل البرنامج (قد يستغرق بضع ثوانٍ في المرة الأولى)
3. استخدم بيانات المدير الافتراضية:
   - اسم المستخدم: admin
   - كلمة المرور: admin123

### 📋 الميزات الرئيسية:
✅ إدارة المستخدمين والصلاحيات
✅ إدارة الأقسام والشعب  
✅ إدارة المستفيدين والأصناف
✅ تسجيل المعاملات والحركات
✅ إنشاء التقارير والإحصائيات
✅ النسخ الاحتياطي واستعادة البيانات
✅ طباعة الإيصالات والتقارير
✅ البحث المتقدم والفلترة
✅ واجهة مستخدم عربية حديثة

### 📁 هيكل المجلدات:
- البيانات/: قاعدة البيانات وملفات الإعدادات
- التقارير/: التقارير المُنشأة
- النسخ_الاحتياطية/: النسخ الاحتياطية التلقائية
- الصادرات/: الملفات المُصدرة (Excel, PDF)
- السجلات/: ملفات السجلات والأخطاء

### 🔧 متطلبات النظام:
- نظام التشغيل: Windows 7/8/10/11
- الذاكرة: 2 جيجابايت RAM أو أكثر
- مساحة القرص: 200 ميجابايت متاحة
- دقة الشاشة: 1024x768 أو أعلى

### 🛡️ الأمان:
- تشفير كلمات المرور
- تسجيل جميع العمليات
- نسخ احتياطية تلقائية
- صلاحيات متدرجة للمستخدمين

### 📞 الدعم الفني:
للحصول على الدعم الفني أو الإبلاغ عن مشاكل:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +966-XX-XXX-XXXX

### 📝 ملاحظات مهمة:
- يُنصح بإنشاء نسخة احتياطية دورية من البيانات
- لا تحذف مجلد "البيانات" لتجنب فقدان المعلومات
- في حالة ظهور رسائل أمان من Windows، اختر "تشغيل على أي حال"

### 🔄 التحديثات:
تاريخ الإنشاء: """ + time.strftime("%Y-%m-%d %H:%M:%S") + """
رقم الإصدار: 1.1.0
حجم الملف: 54.4 ميجابايت
نوع الملف: تطبيق مستقل (لا يحتاج تثبيت)

---
© 2025 نظام إدارة المخازن والمستودعات - جميع الحقوق محفوظة
"""
    
    readme_path = final_package_dir / "اقرأني_أولاً.txt"
    with open(readme_path, "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    # إنشاء ملف تشغيل سريع
    quick_start_content = '''@echo off
chcp 65001 > nul
title نظام إدارة المخازن والمستودعات
echo.
echo ================================
echo   نظام إدارة المخازن والمستودعات
echo   الإصدار 1.1 - النسخة الكاملة
echo ================================
echo.
echo جاري تشغيل البرنامج...
echo يرجى الانتظار...
echo.

start "" "نظام_إدارة_المخازن.exe"

echo تم تشغيل البرنامج بنجاح!
echo يمكنك إغلاق هذه النافذة الآن.
echo.
timeout /t 3 > nul
'''
    
    quick_start_path = final_package_dir / "تشغيل_سريع.bat"
    with open(quick_start_path, "w", encoding="utf-8") as f:
        f.write(quick_start_content)
    
    # إنشاء ملف معلومات النظام
    system_info_content = '''@echo off
chcp 65001 > nul
title معلومات النظام - نظام إدارة المخازن
echo.
echo ================================
echo      معلومات النظام
echo ================================
echo.
echo معلومات البرنامج:
echo - الاسم: نظام إدارة المخازن والمستودعات
echo - الإصدار: 1.1.0
echo - تاريخ الإنشاء: ''' + time.strftime("%Y-%m-%d") + '''
echo - حجم الملف: 54.4 ميجابايت
echo.
echo معلومات النظام:
systeminfo | findstr /C:"OS Name" /C:"OS Version" /C:"System Type" /C:"Total Physical Memory"
echo.
echo معلومات القرص الصلب:
dir "%~dp0" | findstr /C:"bytes free"
echo.
echo ================================
echo.
pause
'''
    
    system_info_path = final_package_dir / "معلومات_النظام.bat"
    with open(system_info_path, "w", encoding="utf-8") as f:
        f.write(system_info_content)
    
    # إنشاء ملف إلغاء التثبيت
    uninstall_content = '''@echo off
chcp 65001 > nul
title إلغاء تثبيت نظام إدارة المخازن
echo.
echo ================================
echo    إلغاء تثبيت البرنامج
echo ================================
echo.
echo تحذير: سيتم حذف جميع ملفات البرنامج
echo (باستثناء البيانات إذا اخترت الاحتفاظ بها)
echo.
set /p confirm="هل أنت متأكد من المتابعة؟ (y/n): "
if /i "%confirm%" neq "y" goto :cancel

echo.
set /p keep_data="هل تريد الاحتفاظ بالبيانات؟ (y/n): "

if /i "%keep_data%" equ "y" (
    echo جاري نسخ البيانات إلى سطح المكتب...
    if not exist "%USERPROFILE%\\Desktop\\نسخة_احتياطية_البيانات" mkdir "%USERPROFILE%\\Desktop\\نسخة_احتياطية_البيانات"
    xcopy /E /I /Y "البيانات\\*" "%USERPROFILE%\\Desktop\\نسخة_احتياطية_البيانات\\"
    echo تم حفظ البيانات في: %USERPROFILE%\\Desktop\\نسخة_احتياطية_البيانات
)

echo.
echo جاري حذف ملفات البرنامج...
cd ..
rmdir /s /q "نظام_إدارة_المخازن_النهائي"
echo تم إلغاء تثبيت البرنامج بنجاح!
goto :end

:cancel
echo تم إلغاء العملية.

:end
echo.
pause
'''
    
    uninstall_path = final_package_dir / "إلغاء_التثبيت.bat"
    with open(uninstall_path, "w", encoding="utf-8") as f:
        f.write(uninstall_content)
    
    print_status(f"تم إنشاء الحزمة النهائية في: {final_package_dir}", "SUCCESS")
    return final_package_dir

def create_installer():
    """إنشاء مثبت محسن"""
    print_status("إنشاء مثبت محسن...")
    
    installer_content = '''@echo off
chcp 65001 > nul
title مثبت نظام إدارة المخازن والمستودعات
color 0A

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                نظام إدارة المخازن والمستودعات                ║
echo ║                    مثبت البرنامج - الإصدار 1.1                ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo مرحباً بك في مثبت نظام إدارة المخازن والمستودعات
echo.
echo الميزات:
echo ✓ واجهة مستخدم عربية حديثة
echo ✓ إدارة شاملة للمخازن والمستودعات  
echo ✓ تقارير وإحصائيات متقدمة
echo ✓ نسخ احتياطية تلقائية
echo ✓ أمان وصلاحيات متدرجة
echo.

set /p install_choice="هل تريد المتابعة مع التثبيت؟ (y/n): "
if /i "%install_choice%" neq "y" goto :cancel

echo.
echo اختر مكان التثبيت:
echo 1. سطح المكتب (افتراضي)
echo 2. مجلد البرامج
echo 3. مسار مخصص
echo.
set /p location_choice="اختر الرقم (1-3): "

if "%location_choice%"=="1" (
    set "INSTALL_DIR=%USERPROFILE%\\Desktop\\نظام_إدارة_المخازن"
) else if "%location_choice%"=="2" (
    set "INSTALL_DIR=%ProgramFiles%\\نظام_إدارة_المخازن"
) else if "%location_choice%"=="3" (
    set /p "INSTALL_DIR=أدخل المسار الكامل: "
) else (
    set "INSTALL_DIR=%USERPROFILE%\\Desktop\\نظام_إدارة_المخازن"
)

echo.
echo جاري التثبيت في: %INSTALL_DIR%
echo.

if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

echo [████████████████████████████████████████] 100%%
echo.
echo جاري نسخ ملفات البرنامج...
xcopy /E /I /Y "نظام_إدارة_المخازن_النهائي\\*" "%INSTALL_DIR%\\"

echo.
echo جاري إنشاء اختصارات...

REM إنشاء اختصار على سطح المكتب
set "SHORTCUT=%USERPROFILE%\\Desktop\\نظام إدارة المخازن.lnk"
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%SHORTCUT%'); $Shortcut.TargetPath = '%INSTALL_DIR%\\نظام_إدارة_المخازن.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'نظام إدارة المخازن والمستودعات'; $Shortcut.Save()"

REM إنشاء اختصار في قائمة ابدأ
set "START_MENU=%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs"
if not exist "%START_MENU%\\نظام إدارة المخازن" mkdir "%START_MENU%\\نظام إدارة المخازن"
set "START_SHORTCUT=%START_MENU%\\نظام إدارة المخازن\\نظام إدارة المخازن.lnk"
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%START_SHORTCUT%'); $Shortcut.TargetPath = '%INSTALL_DIR%\\نظام_إدارة_المخازن.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'نظام إدارة المخازن والمستودعات'; $Shortcut.Save()"

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    تم التثبيت بنجاح!                         ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo تفاصيل التثبيت:
echo - مسار التثبيت: %INSTALL_DIR%
echo - اختصار سطح المكتب: تم إنشاؤه
echo - اختصار قائمة ابدأ: تم إنشاؤه
echo.
echo بيانات الدخول الافتراضية:
echo - اسم المستخدم: admin
echo - كلمة المرور: admin123
echo.
set /p run_now="هل تريد تشغيل البرنامج الآن؟ (y/n): "
if /i "%run_now%"=="y" (
    start "" "%INSTALL_DIR%\\نظام_إدارة_المخازن.exe"
)
goto :end

:cancel
echo تم إلغاء التثبيت.

:end
echo.
echo شكراً لاستخدام نظام إدارة المخازن والمستودعات!
pause
'''
    
    installer_path = Path("dist") / "مثبت_نظام_إدارة_المخازن.bat"
    with open(installer_path, "w", encoding="utf-8") as f:
        f.write(installer_content)
    
    print_status(f"تم إنشاء المثبت: {installer_path}", "SUCCESS")

def main():
    """الدالة الرئيسية"""
    print_status("=" * 60, "INFO")
    print_status("تنظيم الحزمة النهائية لنظام إدارة المخازن", "INFO")
    print_status("=" * 60, "INFO")
    
    # إنشاء الحزمة النهائية
    final_package = create_final_package()
    
    # إنشاء المثبت
    create_installer()
    
    # عرض النتائج النهائية
    print_status("=" * 60, "SUCCESS")
    print_status("تم إنشاء الحزمة النهائية بنجاح!", "SUCCESS")
    print_status(f"مجلد البرنامج: {final_package}", "SUCCESS")
    print_status("ملفات إضافية تم إنشاؤها:", "INFO")
    print_status("- مثبت_نظام_إدارة_المخازن.bat (مثبت تلقائي)", "INFO")
    print_status("- تشغيل_سريع.bat (تشغيل مباشر)", "INFO")
    print_status("- معلومات_النظام.bat (معلومات تقنية)", "INFO")
    print_status("- إلغاء_التثبيت.bat (إزالة البرنامج)", "INFO")
    print_status("=" * 60, "SUCCESS")
    
    # عرض معلومات الملف
    exe_file = Path("dist") / "نظام_إدارة_المخازن_كامل.exe"
    if exe_file.exists():
        file_size = exe_file.stat().st_size / (1024 * 1024)
        print_status(f"حجم ملف البرنامج: {file_size:.1f} ميجابايت", "INFO")
    
    print_status("البرنامج جاهز للاستخدام والتوزيع!", "SUCCESS")

if __name__ == "__main__":
    main()
    input("اضغط Enter للخروج...")