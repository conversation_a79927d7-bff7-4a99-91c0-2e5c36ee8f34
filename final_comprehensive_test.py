#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لجميع الشاشات - التحقق النهائي
Final Comprehensive Test for All Windows
"""

import sys
import os
import traceback

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def comprehensive_test():
    """اختبار شامل لجميع الشاشات"""
    print("🧪 بدء الاختبار الشامل لجميع الشاشات...")
    print("=" * 70)
    
    # قائمة جميع الشاشات
    all_windows = [
        # الشاشات الرئيسية
        ("ui.transactions_window", "TransactionsWindow", "عمليات الصرف"),
        ("ui.reports_window", "ReportsWindow", "التقارير"),
        ("ui.inventory_window", "InventoryWindow", "الأصناف"),
        
        # الشاشات الإدارية
        ("ui.users_management_window", "UsersManagementWindow", "المستخدمين"),
        ("ui.departments_window", "DepartmentsWindow", "الإدارات"),
        ("ui.organizational_chart_window", "OrganizationalChartWindow", "الجدول التنظيمي"),
        ("ui.beneficiaries_window", "BeneficiariesWindow", "المستفيدين"),
        
        # شاشات إضافية
        ("ui.main_window", "MainWindow", "النافذة الرئيسية"),
        ("ui.login_window", "LoginWindow", "تسجيل الدخول"),
        ("ui.units_window", "UnitsWindow", "الوحدات"),
    ]
    
    results = []
    success_count = 0
    
    for module_name, class_name, display_name in all_windows:
        try:
            print(f"🔄 اختبار {display_name}...")
            
            # محاولة استيراد الوحدة
            module = __import__(module_name, fromlist=[class_name])
            
            # محاولة الحصول على الكلاس
            window_class = getattr(module, class_name)
            
            print(f"✅ {display_name} - يعمل بشكل صحيح")
            results.append((display_name, True, None))
            success_count += 1
            
        except Exception as e:
            print(f"❌ {display_name} - خطأ: {e}")
            results.append((display_name, False, str(e)))
    
    # عرض النتائج النهائية
    print("\n" + "=" * 70)
    print("📋 النتائج النهائية:")
    print("=" * 70)
    
    # الشاشات الناجحة
    successful_windows = [name for name, success, _ in results if success]
    failed_windows = [name for name, success, _ in results if not success]
    
    if successful_windows:
        print("✅ الشاشات التي تعمل بشكل صحيح:")
        for window in successful_windows:
            print(f"   ✓ {window}")
    
    if failed_windows:
        print("\n❌ الشاشات التي تحتاج إلى إصلاح:")
        for window in failed_windows:
            print(f"   ✗ {window}")
    
    print(f"\n📊 النتيجة النهائية: {success_count}/{len(all_windows)} شاشات تعمل")
    
    # تقييم النتيجة
    success_percentage = (success_count / len(all_windows)) * 100
    
    if success_percentage == 100:
        print("🎉 ممتاز! جميع الشاشات تعمل بشكل صحيح!")
        print("✅ المشكلة تم حلها بالكامل!")
    elif success_percentage >= 90:
        print("🟢 جيد جداً! معظم الشاشات تعمل بشكل صحيح")
        print(f"⚠️ يجب إصلاح {len(failed_windows)} شاشات فقط")
    elif success_percentage >= 70:
        print("🟡 جيد! أغلب الشاشات تعمل")
        print(f"⚠️ يجب إصلاح {len(failed_windows)} شاشات")
    else:
        print("🔴 يحتاج إلى مزيد من العمل")
        print(f"❌ يجب إصلاح {len(failed_windows)} شاشات")
    
    return failed_windows

def check_main_window_methods():
    """اختبار طرق النافذة الرئيسية"""
    print("\n" + "=" * 70)
    print("🔧 اختبار طرق النافذة الرئيسية...")
    print("=" * 70)
    
    try:
        from ui.main_window import MainWindow
        
        # فحص وجود الطرق المطلوبة
        required_methods = [
            'new_transaction',
            'show_transactions', 
            'show_reports',
            'show_inventory_dashboard',
            'manage_users',
            'manage_departments',
            'show_beneficiaries'
        ]
        
        missing_methods = []
        existing_methods = []
        
        for method_name in required_methods:
            if hasattr(MainWindow, method_name):
                existing_methods.append(method_name)
                print(f"✅ {method_name} - موجود")
            else:
                missing_methods.append(method_name)
                print(f"❌ {method_name} - مفقود")
        
        print(f"\n📊 النتيجة: {len(existing_methods)}/{len(required_methods)} طرق موجودة")
        
        if not missing_methods:
            print("🎉 جميع الطرق المطلوبة موجودة!")
        else:
            print(f"⚠️ الطرق المفقودة: {missing_methods}")
            
    except Exception as e:
        print(f"❌ خطأ في فحص النافذة الرئيسية: {e}")

if __name__ == "__main__":
    try:
        # الاختبار الشامل
        failed_windows = comprehensive_test()
        
        # اختبار طرق النافذة الرئيسية
        check_main_window_methods()
        
        print("\n" + "=" * 70)
        print("🏁 انتهى الاختبار الشامل")
        print("=" * 70)
        
        if not failed_windows:
            print("🎉 جميع الشاشات تعمل بشكل مثالي!")
            print("✅ يمكن للمستخدم الآن فتح جميع الشاشات بدون مشاكل")
        else:
            print(f"⚠️ يجب إصلاح {len(failed_windows)} شاشات")
        
    except Exception as e:
        print(f"❌ خطأ عام في الاختبار: {e}")
        traceback.print_exc()
