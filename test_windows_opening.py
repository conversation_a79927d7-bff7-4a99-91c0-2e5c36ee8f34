#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار فتح الشاشات - عملية الصرف والتقارير والأصناف
Test Windows Opening - Transactions, Reports, and Items
"""

import tkinter as tk
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
import sys
import os
import traceback

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_windows():
    """اختبار فتح الشاشات"""
    print("🧪 بدء اختبار فتح الشاشات...")
    
    try:
        # إنشاء نافذة رئيسية
        root = ttk_bs.Window(themename="flatly")
        root.title("🧪 اختبار فتح الشاشات")
        root.geometry("600x500")
        
        # إنشاء كائن وهمي للمستخدم
        class MockUser:
            def __init__(self):
                self.username = "test_user"
                self.full_name = "مستخدم تجريبي"
                self.is_admin = True
                self.user_type = "admin"
        
        # إنشاء كائن وهمي للنافذة الرئيسية
        class MockMainWindow:
            def __init__(self):
                self.main_frame = ttk_bs.Frame(root)
                self.main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
                self.status_var = tk.StringVar()
                self.status_var.set("جاهز")
                
            def clear_main_content(self):
                for widget in self.main_frame.winfo_children():
                    widget.destroy()
                    
            def refresh_data(self):
                print("✅ تم تحديث البيانات")
        
        mock_user = MockUser()
        mock_main_window = MockMainWindow()
        
        # دوال اختبار الشاشات
        def test_transactions():
            try:
                print("🔄 اختبار فتح شاشة عمليات الصرف...")
                from ui.transactions_window import TransactionsWindow
                window = TransactionsWindow(root, mock_main_window, embedded=True)
                print("✅ تم فتح شاشة عمليات الصرف بنجاح")
                return True
            except Exception as e:
                print(f"❌ خطأ في فتح شاشة عمليات الصرف: {e}")
                traceback.print_exc()
                return False
        
        def test_reports():
            try:
                print("🔄 اختبار فتح شاشة التقارير...")
                from ui.reports_window import ReportsWindow
                window = ReportsWindow(root, mock_main_window)
                print("✅ تم فتح شاشة التقارير بنجاح")
                return True
            except Exception as e:
                print(f"❌ خطأ في فتح شاشة التقارير: {e}")
                traceback.print_exc()
                return False
        
        def test_inventory():
            try:
                print("🔄 اختبار فتح شاشة الأصناف...")
                from ui.inventory_window import InventoryWindow
                window = InventoryWindow(root, mock_main_window)
                print("✅ تم فتح شاشة الأصناف بنجاح")
                return True
            except Exception as e:
                print(f"❌ خطأ في فتح شاشة الأصناف: {e}")
                traceback.print_exc()
                return False
        
        # إنشاء واجهة الاختبار
        title_label = ttk_bs.Label(
            root,
            text="🧪 اختبار فتح الشاشات",
            font=("Arial", 16, "bold")
        )
        title_label.pack(pady=20)
        
        info_label = ttk_bs.Label(
            root,
            text="اختبار فتح شاشات عملية الصرف والتقارير والأصناف",
            font=("Arial", 12)
        )
        info_label.pack(pady=10)
        
        # إطار الأزرار
        buttons_frame = ttk_bs.LabelFrame(
            root,
            text="🎮 أزرار الاختبار",
            padding=20
        )
        buttons_frame.pack(pady=20, padx=20, fill="x")
        
        # أزرار الاختبار
        test_buttons = [
            ("🔄 اختبار عمليات الصرف", test_transactions, "primary"),
            ("📊 اختبار التقارير", test_reports, "info"),
            ("📦 اختبار الأصناف", test_inventory, "success")
        ]
        
        for text, command, style in test_buttons:
            btn = ttk_bs.Button(
                buttons_frame,
                text=text,
                command=command,
                bootstyle=style,
                width=25
            )
            btn.pack(pady=5)
        
        # دالة اختبار جميع الشاشات
        def test_all():
            print("\n" + "="*50)
            print("🚀 بدء اختبار جميع الشاشات...")
            print("="*50)
            
            results = []
            
            # اختبار عمليات الصرف
            results.append(("عمليات الصرف", test_transactions()))
            
            # اختبار التقارير
            results.append(("التقارير", test_reports()))
            
            # اختبار الأصناف
            results.append(("الأصناف", test_inventory()))
            
            # عرض النتائج
            print("\n" + "="*50)
            print("📋 نتائج الاختبار:")
            print("="*50)
            
            success_count = 0
            for name, success in results:
                status = "✅ نجح" if success else "❌ فشل"
                print(f"{status} - {name}")
                if success:
                    success_count += 1
            
            print(f"\n📊 النتيجة النهائية: {success_count}/{len(results)} شاشات تعمل بنجاح")
            
            if success_count == len(results):
                print("🎉 جميع الشاشات تعمل بشكل صحيح!")
            else:
                print("⚠️ بعض الشاشات تحتاج إلى إصلاح")
        
        # زر اختبار الكل
        test_all_btn = ttk_bs.Button(
            buttons_frame,
            text="🚀 اختبار جميع الشاشات",
            command=test_all,
            bootstyle="warning",
            width=25
        )
        test_all_btn.pack(pady=15)
        
        # إطار النتائج
        results_frame = ttk_bs.LabelFrame(
            root,
            text="📋 النتائج",
            padding=10
        )
        results_frame.pack(pady=10, padx=20, fill="both", expand=True)
        
        results_text = tk.Text(
            results_frame,
            height=8,
            font=("Arial", 10),
            wrap=tk.WORD
        )
        results_text.pack(fill="both", expand=True)
        
        # إعادة توجيه المخرجات إلى النص
        import sys
        from io import StringIO
        
        class TextRedirector:
            def __init__(self, widget):
                self.widget = widget
                
            def write(self, str):
                self.widget.insert(tk.END, str)
                self.widget.see(tk.END)
                self.widget.update()
                
            def flush(self):
                pass
        
        # توجيه المخرجات
        sys.stdout = TextRedirector(results_text)
        
        print("✅ تم إنشاء نافذة الاختبار")
        print("💡 اضغط على الأزرار لاختبار فتح الشاشات")
        
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    test_windows()
