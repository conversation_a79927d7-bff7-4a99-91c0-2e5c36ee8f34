#!/usr/bin/env python3
"""
حذف جميع الوحدات من قاعدة البيانات مباشرة
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from database import db_manager

def delete_all_units_direct():
    """حذف جميع الوحدات وجميع البيانات المرتبطة بها مباشرة"""
    try:
        print("🗑️ بدء عملية حذف جميع الوحدات...")
        
        # التحقق من الوحدات الموجودة أولاً
        units = db_manager.fetch_all("SELECT * FROM units")
        if not units:
            print("✅ لا توجد وحدات للحذف")
            return
        
        print(f"📋 سيتم حذف {len(units)} وحدة وجميع البيانات المرتبطة بها")
        
        # حذف البيانات المرتبطة أولاً (تسلسل الحذف مهم)
        
        # 1. حذف الأقسام المرتبطة بالإدارات التابعة للوحدات
        print("🔄 حذف الأقسام...")
        db_manager.execute_query("""
            DELETE FROM sections 
            WHERE department_id IN (
                SELECT id FROM departments WHERE unit_id IN (SELECT id FROM units)
            )
        """)
        print("✅ تم حذف الأقسام")
        
        # 2. حذف الإدارات المرتبطة بالوحدات
        print("🔄 حذف الإدارات...")
        db_manager.execute_query("""
            DELETE FROM departments WHERE unit_id IN (SELECT id FROM units)
        """)
        print("✅ تم حذف الإدارات")
        
        # 3. حذف المستفيدين المرتبطين بالوحدات
        print("🔄 حذف المستفيدين...")
        db_manager.execute_query("""
            DELETE FROM beneficiaries WHERE unit_id IN (SELECT id FROM units)
        """)
        print("✅ تم حذف المستفيدين")
        
        # 4. حذف جميع الوحدات نهائياً
        print("🔄 حذف جميع الوحدات...")
        db_manager.execute_query("DELETE FROM units")
        print("✅ تم حذف جميع الوحدات")
        
        # التحقق من النتيجة
        remaining_units = db_manager.fetch_all("SELECT * FROM units")
        if not remaining_units:
            print("\n🎉 تم حذف جميع الوحدات بنجاح!")
            print("📊 ملخص العملية:")
            print(f"   • الوحدات المحذوفة: {len(units)}")
            print("   • الأقسام المحذوفة: تم")
            print("   • الإدارات المحذوفة: تم") 
            print("   • المستفيدين المحذوفين: تم")
        else:
            print(f"⚠️ تحذير: لا تزال هناك {len(remaining_units)} وحدة موجودة")
            
    except Exception as e:
        print(f"❌ خطأ في حذف الوحدات: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    delete_all_units_direct()