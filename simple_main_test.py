#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط للنافذة الرئيسية
Simple Main Window Test
"""

import sys
import os
import traceback

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# استيراد المكتبات في المستوى الأعلى
import tkinter as tk
import ttkbootstrap as ttk_bs

def test_main_window_simple():
    """اختبار بسيط للنافذة الرئيسية"""
    print("🧪 اختبار بسيط للنافذة الرئيسية...")
    print("=" * 50)
    
    try:
        # إنشاء نافذة رئيسية
        root = ttk_bs.Window(themename="flatly")
        root.title("🧪 اختبار النافذة الرئيسية")
        root.geometry("400x300")
        
        # إنشاء كائن وهمي للمستخدم
        class MockUser:
            def __init__(self):
                self.username = "test_user"
                self.full_name = "مستخدم تجريبي"
                self.is_admin = True
                self.user_type = "admin"
        
        print("✅ تم إنشاء النافذة الأساسية")
        
        # استيراد النافذة الرئيسية
        from ui.main_window import MainWindow
        print("✅ تم استيراد MainWindow")
        
        # إنشاء كائن وهمي للتطبيق
        class MockApp:
            def __init__(self):
                self.current_user = None

        # إنشاء النافذة الرئيسية
        mock_user = MockUser()
        mock_app = MockApp()
        main_window = MainWindow(root, mock_user, mock_app)
        print("✅ تم إنشاء النافذة الرئيسية بنجاح")
        
        # اختبار الطرق الثلاث
        print("\n🔄 اختبار طريقة new_transaction()...")
        try:
            main_window.new_transaction()
            print("✅ نجح تشغيل new_transaction()")
        except Exception as e:
            print(f"❌ خطأ في new_transaction(): {e}")
            traceback.print_exc()
        
        print("\n🔄 اختبار طريقة show_reports()...")
        try:
            main_window.show_reports()
            print("✅ نجح تشغيل show_reports()")
        except Exception as e:
            print(f"❌ خطأ في show_reports(): {e}")
            traceback.print_exc()
        
        print("\n🔄 اختبار طريقة show_inventory_dashboard()...")
        try:
            main_window.show_inventory_dashboard()
            print("✅ نجح تشغيل show_inventory_dashboard()")
        except Exception as e:
            print(f"❌ خطأ في show_inventory_dashboard(): {e}")
            traceback.print_exc()
        
        print("\n🎉 انتهى الاختبار!")
        print("💡 إذا لم تظهر أخطاء، فالمشكلة تم حلها!")
        
        # إغلاق النافذة بعد ثانيتين
        root.after(2000, root.destroy)
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ عام في الاختبار: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    test_main_window_simple()
