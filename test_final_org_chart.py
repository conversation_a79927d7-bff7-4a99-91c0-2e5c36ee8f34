#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نهائي لرسالة النجاح في الجدول التنظيمي
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import tkinter as tk
from models import OrganizationalChart
from ui.organizational_chart_edit_window import OrganizationalChartEditWindow
from database import db_manager

def test_organizational_chart_edit():
    """اختبار تعديل عنصر في الجدول التنظيمي"""
    print("🧪 اختبار تعديل الجدول التنظيمي...")
    
    try:
        # إنشاء عنصر تجريبي
        test_item = OrganizationalChart()
        test_item.sequence_number = 999
        test_item.item_code = "TEST001"
        test_item.item_name = "صنف تجريبي للاختبار"
        test_item.unit = "قطعة"
        
        if test_item.save():
            print(f"✅ تم إنشاء العنصر التجريبي بمعرف: {test_item.id}")
            
            # إنشاء نافذة رئيسية
            root = tk.Tk()
            root.title("اختبار الجدول التنظيمي")
            root.geometry("500x400")
            
            # إنشاء زر لفتح نافذة التعديل
            def open_edit_window():
                edit_window = OrganizationalChartEditWindow(
                    parent=root,
                    main_window=None,
                    item_id=test_item.id
                )
            
            edit_btn = tk.Button(
                root,
                text="فتح نافذة التعديل",
                command=open_edit_window,
                font=("Arial", 14),
                bg="#007bff",
                fg="white",
                padx=20,
                pady=10
            )
            edit_btn.pack(pady=50)
            
            # تعليمات للمستخدم
            instructions = tk.Label(
                root,
                text="اضغط على الزر لفتح نافذة التعديل\nثم قم بتعديل البيانات واحفظ\nلاختبار رسالة النجاح",
                font=("Arial", 12),
                justify="center"
            )
            instructions.pack(pady=20)
            
            # زر لحذف العنصر التجريبي
            def cleanup():
                try:
                    test_item.delete()
                    print("🗑️ تم حذف العنصر التجريبي")
                except:
                    pass
                root.quit()
            
            cleanup_btn = tk.Button(
                root,
                text="إنهاء الاختبار وحذف البيانات التجريبية",
                command=cleanup,
                font=("Arial", 10),
                bg="#dc3545",
                fg="white",
                padx=10,
                pady=5
            )
            cleanup_btn.pack(pady=20)
            
            print("📋 تم فتح نافذة الاختبار. اضغط على الزر لاختبار التعديل.")
            root.mainloop()
            
        else:
            print("❌ فشل في إنشاء العنصر التجريبي")
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")

def test_success_message_directly():
    """اختبار رسالة النجاح مباشرة"""
    print("\n🔧 اختبار رسالة النجاح مباشرة...")
    
    from ui.success_message import AutoSuccessMessage
    
    root = tk.Tk()
    root.title("اختبار رسالة النجاح")
    root.geometry("400x300")
    root.withdraw()  # إخفاء النافذة
    
    # محاكاة رسالة الجدول التنظيمي
    test_message = "تم تحديث الصنف بنجاح\n\nاسم الصنف: مواد غذائية\nرقم الصنف: 101"
    
    try:
        AutoSuccessMessage.show(root, test_message, duration=4000)
        print("✅ تم عرض رسالة النجاح")
        
        # انتظار 5 ثوانٍ ثم إغلاق
        root.after(5000, root.quit)
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في عرض رسالة النجاح: {e}")
    
    root.destroy()

def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار نهائي للجدول التنظيمي")
    print("=" * 50)
    
    # اختبار رسالة النجاح مباشرة
    test_success_message_directly()
    
    # اختبار التعديل الكامل
    test_organizational_chart_edit()
    
    print("\n✅ انتهى الاختبار")

if __name__ == "__main__":
    main()
