#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق مفاتيح الاختصار على جميع نوافذ البرنامج
Apply Shortcuts to All Windows
"""

import os
import re
from pathlib import Path

def add_shortcuts_import(file_path):
    """إضافة استيراد مفاتيح الاختصار للملف"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # التحقق من وجود الاستيراد بالفعل
        if 'from ui.global_shortcuts import' in content:
            print(f"✅ الاستيراد موجود بالفعل في {file_path}")
            return True
        
        # البحث عن آخر استيراد
        import_pattern = r'(from\s+[\w.]+\s+import\s+[^\n]+\n)'
        imports = re.findall(import_pattern, content)
        
        if imports:
            last_import = imports[-1]
            new_import = last_import + "from ui.global_shortcuts import GlobalShortcuts, ContextHandler\n"
            content = content.replace(last_import, new_import)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ تم إضافة الاستيراد إلى {file_path}")
            return True
        else:
            print(f"❌ لم يتم العثور على استيرادات في {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في معالجة {file_path}: {e}")
        return False

def add_shortcuts_setup(file_path, class_name):
    """إضافة إعداد مفاتيح الاختصار للكلاس"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # التحقق من وجود setup_shortcuts بالفعل
        if 'def setup_shortcuts(' in content:
            print(f"✅ setup_shortcuts موجود بالفعل في {file_path}")
            return True
        
        # البحث عن نهاية الكلاس
        class_pattern = rf'class\s+{class_name}.*?(?=\n\nclass|\n\n#|\n\nif\s+__name__|\Z)'
        class_match = re.search(class_pattern, content, re.DOTALL)
        
        if class_match:
            class_content = class_match.group(0)
            
            # إضافة دالة setup_shortcuts
            shortcuts_method = f'''
    def setup_shortcuts(self):
        """إعداد مفاتيح الاختصار العامة"""
        try:
            # إنشاء معالج السياق
            self.context_handler = ContextHandler()
            
            # تعيين دوال العمليات (يمكن تخصيصها حسب النافذة)
            self.context_handler.set_save_callback(self.shortcut_save)
            self.context_handler.set_delete_callback(self.shortcut_delete)
            self.context_handler.set_copy_callback(self.shortcut_copy)
            self.context_handler.set_paste_callback(self.shortcut_paste)
            
            # تفعيل مفاتيح الاختصار
            window = getattr(self, 'window', None) or getattr(self, 'parent', None)
            if window:
                self.global_shortcuts = GlobalShortcuts(window, self.context_handler)
        except Exception as e:
            print(f"خطأ في إعداد مفاتيح الاختصار: {{e}}")
    
    def shortcut_save(self):
        """عملية الحفظ (F1)"""
        try:
            if hasattr(self, 'save_data'):
                self.save_data()
            elif hasattr(self, 'save_changes'):
                self.save_changes()
            elif hasattr(self, 'save'):
                self.save()
            else:
                print("عملية الحفظ غير متاحة")
        except Exception as e:
            print(f"خطأ في عملية الحفظ: {{e}}")
    
    def shortcut_delete(self):
        """عملية الحذف (F2)"""
        try:
            if hasattr(self, 'delete_selected'):
                self.delete_selected()
            elif hasattr(self, 'delete_item'):
                self.delete_item()
            elif hasattr(self, 'delete'):
                self.delete()
            else:
                print("عملية الحذف غير متاحة")
        except Exception as e:
            print(f"خطأ في عملية الحذف: {{e}}")
    
    def shortcut_copy(self):
        """عملية النسخ (F3)"""
        try:
            if hasattr(self, 'copy_data'):
                self.copy_data()
            else:
                # نسخ عامة للبيانات المحددة
                import pyperclip
                pyperclip.copy("تم النسخ من النافذة")
                print("تم نسخ البيانات")
        except Exception as e:
            print(f"خطأ في عملية النسخ: {{e}}")
    
    def shortcut_paste(self):
        """عملية اللصق (F4)"""
        try:
            if hasattr(self, 'paste_data'):
                self.paste_data()
            else:
                import pyperclip
                clipboard_text = pyperclip.paste()
                if clipboard_text:
                    print(f"تم لصق: {{clipboard_text[:50]}}")
                else:
                    print("لا توجد بيانات في الحافظة")
        except Exception as e:
            print(f"خطأ في عملية اللصق: {{e}}")'''
            
            # إضافة الدوال في نهاية الكلاس
            new_class_content = class_content + shortcuts_method
            content = content.replace(class_content, new_class_content)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ تم إضافة setup_shortcuts إلى {file_path}")
            return True
        else:
            print(f"❌ لم يتم العثور على الكلاس {class_name} في {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في إضافة setup_shortcuts إلى {file_path}: {e}")
        return False

def add_shortcuts_call(file_path, init_method_pattern):
    """إضافة استدعاء setup_shortcuts في دالة __init__"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # التحقق من وجود الاستدعاء بالفعل
        if 'self.setup_shortcuts()' in content:
            print(f"✅ استدعاء setup_shortcuts موجود بالفعل في {file_path}")
            return True
        
        # البحث عن دالة __init__
        init_match = re.search(init_method_pattern, content, re.DOTALL)
        
        if init_match:
            init_content = init_match.group(0)
            
            # إضافة الاستدعاء في نهاية __init__
            lines = init_content.split('\n')
            
            # البحث عن آخر سطر غير فارغ
            last_line_index = len(lines) - 1
            while last_line_index > 0 and not lines[last_line_index].strip():
                last_line_index -= 1
            
            # إضافة الاستدعاء
            indent = '        '  # 8 مسافات للتبويب
            shortcuts_call = f"\n{indent}# تفعيل مفاتيح الاختصار العامة\n{indent}self.setup_shortcuts()"
            
            lines.insert(last_line_index + 1, shortcuts_call)
            new_init_content = '\n'.join(lines)
            
            content = content.replace(init_content, new_init_content)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ تم إضافة استدعاء setup_shortcuts إلى {file_path}")
            return True
        else:
            print(f"❌ لم يتم العثور على دالة __init__ في {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في إضافة استدعاء setup_shortcuts إلى {file_path}: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 تطبيق مفاتيح الاختصار على جميع نوافذ البرنامج")
    print("=" * 60)
    
    # قائمة النوافذ المراد تحديثها
    windows_to_update = [
        {
            'file': 'ui/inventory_window.py',
            'class': 'InventoryWindow',
            'init_pattern': r'def __init__\(self.*?\n(?:.*?\n)*?(?=\n    def|\Z)'
        },
        {
            'file': 'ui/transactions_window.py', 
            'class': 'TransactionsWindow',
            'init_pattern': r'def __init__\(self.*?\n(?:.*?\n)*?(?=\n    def|\Z)'
        },
        {
            'file': 'ui/organizational_chart_window.py',
            'class': 'OrganizationalChartWindow', 
            'init_pattern': r'def __init__\(self.*?\n(?:.*?\n)*?(?=\n    def|\Z)'
        },
        {
            'file': 'ui/departments_window.py',
            'class': 'DepartmentsWindow',
            'init_pattern': r'def __init__\(self.*?\n(?:.*?\n)*?(?=\n    def|\Z)'
        },
        {
            'file': 'ui/units_window.py',
            'class': 'UnitsWindow', 
            'init_pattern': r'def __init__\(self.*?\n(?:.*?\n)*?(?=\n    def|\Z)'
        },
        {
            'file': 'ui/users_management_window.py',
            'class': 'UsersManagementWindow',
            'init_pattern': r'def __init__\(self.*?\n(?:.*?\n)*?(?=\n    def|\Z)'
        }
    ]
    
    success_count = 0
    total_count = len(windows_to_update)
    
    for window_info in windows_to_update:
        file_path = window_info['file']
        class_name = window_info['class']
        init_pattern = window_info['init_pattern']
        
        print(f"\n📁 معالجة {file_path}...")
        
        if not os.path.exists(file_path):
            print(f"❌ الملف غير موجود: {file_path}")
            continue
        
        # إضافة الاستيراد
        import_success = add_shortcuts_import(file_path)
        
        # إضافة دوال مفاتيح الاختصار
        setup_success = add_shortcuts_setup(file_path, class_name)
        
        # إضافة استدعاء setup_shortcuts
        call_success = add_shortcuts_call(file_path, init_pattern)
        
        if import_success and setup_success and call_success:
            success_count += 1
            print(f"✅ تم تحديث {file_path} بنجاح")
        else:
            print(f"⚠️ تحديث جزئي لـ {file_path}")
    
    print(f"\n📊 النتائج:")
    print(f"✅ تم تحديث {success_count} من {total_count} ملف بنجاح")
    print(f"📋 مفاتيح الاختصار المفعلة:")
    print(f"   F1 - الحفظ")
    print(f"   F2 - الحذف") 
    print(f"   F3 - النسخ")
    print(f"   F4 - اللصق")

if __name__ == "__main__":
    main()
