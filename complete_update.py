#!/usr/bin/env python3
"""
تحديث شامل للبرنامج
Complete Update Script
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
from datetime import datetime

def print_header(title):
    """طباعة عنوان رئيسي"""
    print(f"\n{'='*60}")
    print(f"🚀 {title}")
    print(f"{'='*60}")

def print_step(step):
    """طباعة خطوة"""
    print(f"\n🔄 {step}")
    print("-" * 40)

def print_success(message):
    """طباعة نجاح"""
    print(f"✅ {message}")

def print_error(message):
    """طباعة خطأ"""
    print(f"❌ {message}")

def print_info(message):
    """طباعة معلومات"""
    print(f"ℹ️ {message}")

def main():
    """التحديث الشامل"""
    print_header("تحديث شامل لنظام إدارة المخازن")
    print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # الخطوة 1: تنظيف الملفات القديمة
    print_step("تنظيف الملفات القديمة")
    
    old_items = ["dist", "build", "__pycache__"]
    for item in old_items:
        item_path = Path(item)
        if item_path.exists():
            try:
                if item_path.is_dir():
                    shutil.rmtree(item_path)
                else:
                    item_path.unlink()
                print_success(f"تم مسح: {item}")
            except Exception as e:
                print_error(f"فشل في مسح {item}: {e}")
    
    # الخطوة 2: تحديث التاريخ في main.py
    print_step("تحديث معلومات الإصدار")
    
    main_file = Path("main.py")
    if main_file.exists():
        try:
            content = main_file.read_text(encoding='utf-8')
            current_date = datetime.now().strftime("%Y-%m-%d")
            content = content.replace(
                "تاريخ التحديث: 2025-06-15",
                f"تاريخ التحديث: {current_date}"
            )
            main_file.write_text(content, encoding='utf-8')
            print_success(f"تم تحديث التاريخ إلى: {current_date}")
        except Exception as e:
            print_error(f"فشل في تحديث التاريخ: {e}")
    
    # الخطوة 3: بناء البرنامج
    print_step("بناء البرنامج")
    
    try:
        print_info("بدء عملية البناء...")
        result = subprocess.run([
            'pyinstaller', 
            '--clean',
            '--noconfirm',
            'desktop_stores_app.spec'
        ], check=True, capture_output=True, text=True)
        
        print_success("تم بناء البرنامج بنجاح!")
        
        # التحقق من النتيجة
        exe_file = Path("dist/نظام_إدارة_المخازن.exe")
        if exe_file.exists():
            file_size = exe_file.stat().st_size / (1024 * 1024)
            print_success(f"الملف التنفيذي: {file_size:.2f} MB")
        else:
            print_error("الملف التنفيذي غير موجود!")
            return False
            
    except subprocess.CalledProcessError as e:
        print_error(f"فشل في بناء البرنامج: {e}")
        return False
    
    # الخطوة 4: نشر البرنامج
    print_step("نشر البرنامج إلى المسار النهائي")
    
    # المسار الهدف
    target_dir = Path("E:/desktop_stores_app/dist/نظام_إدارة_المخازن_مجلد")
    
    # إنشاء المجلد الهدف
    target_dir.mkdir(parents=True, exist_ok=True)
    print_success(f"تم إنشاء المجلد الهدف")
    
    # نسخ الملف التنفيذي
    exe_file = Path("dist/نظام_إدارة_المخازن.exe")
    target_exe = target_dir / "نظام_إدارة_المخازن.exe"
    
    try:
        # مسح الملف القديم إذا كان موجود
        if target_exe.exists():
            target_exe.unlink()
        
        # نسخ الملف الجديد
        shutil.copy2(exe_file, target_exe)
        print_success("تم نسخ الملف التنفيذي")
        
        # نسخ الملفات الإضافية
        additional_items = ["data", "assets", "settings.json"]
        
        for item_name in additional_items:
            item_path = Path(item_name)
            if item_path.exists():
                target_item = target_dir / item_path.name
                
                if item_path.is_dir():
                    if target_item.exists():
                        shutil.rmtree(target_item)
                    shutil.copytree(item_path, target_item)
                    print_success(f"تم نسخ المجلد: {item_path.name}")
                else:
                    shutil.copy2(item_path, target_item)
                    print_success(f"تم نسخ الملف: {item_path.name}")
        
    except Exception as e:
        print_error(f"فشل في نشر البرنامج: {e}")
        return False
    
    # الخطوة 5: تنظيف الملفات المؤقتة
    print_step("تنظيف الملفات المؤقتة")
    
    temp_items = ["build"]
    for item in temp_items:
        item_path = Path(item)
        if item_path.exists():
            try:
                shutil.rmtree(item_path)
                print_success(f"تم مسح: {item}")
            except Exception as e:
                print_error(f"فشل في مسح {item}: {e}")
    
    # الخطوة 6: الملخص النهائي
    print_step("ملخص العملية")
    
    if target_exe.exists():
        file_size = target_exe.stat().st_size / (1024 * 1024)
        creation_time = datetime.fromtimestamp(target_exe.stat().st_mtime)
        
        print_success("تم تحديث البرنامج بنجاح!")
        print(f"📁 المسار: {target_dir}")
        print(f"🎯 الملف التنفيذي: {target_exe}")
        print(f"📏 الحجم: {file_size:.2f} MB")
        print(f"🕒 تاريخ الإنشاء: {creation_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # عرض محتويات المجلد
        print(f"\n📋 محتويات المجلد النهائي:")
        for item in target_dir.iterdir():
            if item.is_file():
                size = item.stat().st_size / (1024 * 1024)
                print(f"   📄 {item.name} ({size:.2f} MB)")
            else:
                print(f"   📁 {item.name}/")
        
        print(f"\n🎉 البرنامج جاهز للاستخدام!")
        print(f"💡 يمكنك تشغيله من: {target_exe}")
        
        return True
    else:
        print_error("فشل في إنشاء البرنامج!")
        return False

if __name__ == "__main__":
    try:
        success = main()
        
        if success:
            print(f"\n{'='*60}")
            print("🎊 تم التحديث بنجاح!")
            print("📋 الخطوات المكتملة:")
            print("   ✅ تنظيف الملفات القديمة")
            print("   ✅ تحديث معلومات الإصدار")
            print("   ✅ بناء البرنامج")
            print("   ✅ نشر البرنامج")
            print("   ✅ تنظيف الملفات المؤقتة")
            print(f"{'='*60}")
        
        input(f"\n{'✅ نجح التحديث' if success else '❌ فشل التحديث'} - اضغط Enter للخروج...")
        
    except KeyboardInterrupt:
        print("\n⚠️ تم إلغاء العملية بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")