#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لاستيراد الشاشات
Simple Test for Window Imports
"""

import sys
import os
import traceback

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """اختبار استيراد الشاشات"""
    print("🧪 بدء اختبار استيراد الشاشات...")
    print("=" * 50)
    
    # قائمة الشاشات للاختبار
    windows_to_test = [
        ("ui.transactions_window", "TransactionsWindow", "عمليات الصرف"),
        ("ui.reports_window", "ReportsWindow", "التقارير"),
        ("ui.inventory_window", "InventoryWindow", "الأصناف"),
        ("ui.inventory_dashboard_window", "InventoryDashboardWindow", "لوحة تحكم المخزون"),
        ("ui.beneficiaries_window", "BeneficiariesWindow", "المستفيدين")
    ]
    
    results = []
    
    for module_name, class_name, display_name in windows_to_test:
        try:
            print(f"🔄 اختبار استيراد {display_name}...")
            
            # محاولة استيراد الوحدة
            module = __import__(module_name, fromlist=[class_name])
            
            # محاولة الحصول على الكلاس
            window_class = getattr(module, class_name)
            
            print(f"✅ نجح استيراد {display_name}")
            results.append((display_name, True, None))
            
        except Exception as e:
            print(f"❌ فشل استيراد {display_name}: {e}")
            print(f"   التفاصيل: {traceback.format_exc()}")
            results.append((display_name, False, str(e)))
    
    # عرض النتائج النهائية
    print("\n" + "=" * 50)
    print("📋 نتائج الاختبار:")
    print("=" * 50)
    
    success_count = 0
    for display_name, success, error in results:
        if success:
            print(f"✅ {display_name} - يعمل بشكل صحيح")
            success_count += 1
        else:
            print(f"❌ {display_name} - خطأ: {error}")
    
    print(f"\n📊 النتيجة النهائية: {success_count}/{len(results)} شاشات تعمل")
    
    if success_count == len(results):
        print("🎉 جميع الشاشات تستورد بنجاح!")
    else:
        print("⚠️ بعض الشاشات تحتاج إلى إصلاح")
        
        # اقتراحات للإصلاح
        print("\n💡 اقتراحات للإصلاح:")
        print("1. تحقق من وجود الملفات المطلوبة")
        print("2. تحقق من صحة أسماء الكلاسات")
        print("3. تحقق من استيراد المكتبات المطلوبة")
        print("4. تحقق من قاعدة البيانات والنماذج")

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    print("\n🔗 اختبار الاتصال بقاعدة البيانات...")
    try:
        from database import db_manager
        print("✅ تم استيراد مدير قاعدة البيانات")
        
        # محاولة الاتصال
        if hasattr(db_manager, 'connect'):
            db_manager.connect()
            print("✅ تم الاتصال بقاعدة البيانات")
        else:
            print("⚠️ لا توجد دالة connect في مدير قاعدة البيانات")
            
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")

def test_models():
    """اختبار استيراد النماذج"""
    print("\n📋 اختبار استيراد النماذج...")

    models_to_test = [
        "User", "Department", "Section", "Beneficiary",
        "Item", "Category", "Transaction", "TransactionItem"
    ]

    try:
        import models
        print("✅ تم استيراد وحدة النماذج")

        for model_name in models_to_test:
            try:
                model_class = getattr(models, model_name)
                print(f"✅ {model_name} - متوفر")
            except AttributeError:
                print(f"❌ {model_name} - غير متوفر")

    except Exception as e:
        print(f"❌ خطأ في استيراد النماذج: {e}")

def test_config():
    """اختبار استيراد الإعدادات"""
    print("\n⚙️ اختبار استيراد الإعدادات...")
    try:
        from config import APP_CONFIG, UI_CONFIG, get_message
        print("✅ تم استيراد الإعدادات")
        print(f"   اسم التطبيق: {APP_CONFIG.get('app_name', 'غير محدد')}")
        print(f"   إصدار التطبيق: {APP_CONFIG.get('app_version', 'غير محدد')}")
    except Exception as e:
        print(f"❌ خطأ في استيراد الإعدادات: {e}")

if __name__ == "__main__":
    try:
        # اختبار الإعدادات أولاً
        test_config()
        
        # اختبار قاعدة البيانات
        test_database_connection()
        
        # اختبار النماذج
        test_models()
        
        # اختبار استيراد الشاشات
        test_imports()
        
        print("\n🏁 انتهى الاختبار")
        
    except Exception as e:
        print(f"❌ خطأ عام في الاختبار: {e}")
        traceback.print_exc()
