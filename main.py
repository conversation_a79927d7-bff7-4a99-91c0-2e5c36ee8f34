#!/usr/bin/env python3
"""
التطبيق الرئيسي - نظام إدارة المخازن والمستودعات
Main Application - Desktop Stores Management System

الإصدار: 1.1
تاريخ التحديث: 2025-06-18

تشغيل التطبيق:
python main.py
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
import sys
import os
from pathlib import Path
from datetime import datetime
import threading
import webbrowser
import subprocess

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def suppress_ui_errors():
    """قمع رسائل أخطاء واجهة المستخدم وعدم إظهارها للمستخدم"""

    # إعادة تعريف messagebox.showerror لتكون صامتة
    original_showerror = messagebox.showerror

    def silent_showerror(title, message, **kwargs):
        # تسجيل الخطأ في ملف السجل فقط
        try:
            from utils.logger import setup_logger
            logger = setup_logger()
            logger.error(f"UI Error - {title}: {message}")
        except (ImportError, AttributeError):
            pass
        # لا تظهر أي رسالة للمستخدم
        return None

    # استبدال دالة عرض الأخطاء
    messagebox.showerror = silent_showerror

    # معالجة أخطاء Tkinter العامة
    def handle_tk_error(exc, val, tb):
        error_msg = str(val)
        # تجاهل أخطاء واجهة المستخدم المعروفة
        silent_errors = [
            "invalid command name",
            "bad window path name",
            "application has been destroyed",
            "toplevel",
            "treeview",
            "display column"
        ]

        if any(silent_error in error_msg.lower() for silent_error in silent_errors):
            # تسجيل صامت فقط
            try:
                from utils.logger import setup_logger
                logger = setup_logger()
                logger.debug(f"Suppressed UI Error: {error_msg}")
            except (ImportError, AttributeError):
                pass
            return

        # للأخطاء الأخرى، استخدم المعالج الافتراضي
        sys.__excepthook__(exc, val, tb)

    # تعيين معالج الأخطاء الجديد
    def callback_exception_handler(self, exc, val, tb):
        return handle_tk_error(exc, val, tb)

    tk.Tk.report_callback_exception = callback_exception_handler

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 8):
        messagebox.showerror(
            "خطأ في الإصدار",
            f"يتطلب هذا التطبيق Python 3.8 أو أحدث\n"
            f"الإصدار الحالي: {sys.version}\n"
            f"يرجى تحديث Python وإعادة المحاولة"
        )
        return False
    return True

def check_dependencies():
    """التحقق من المكتبات المطلوبة وتثبيتها إذا لزم الأمر"""
    required_packages = {
        'ttkbootstrap': 'ttkbootstrap',
        'bcrypt': 'bcrypt',
        'pillow': 'PIL',
        'reportlab': 'reportlab',
        'openpyxl': 'openpyxl',
        'pandas': 'pandas'
    }

    missing_packages = []

    for package_name, import_name in required_packages.items():
        try:
            __import__(import_name)
        except ImportError:
            missing_packages.append(package_name)

    if missing_packages:
        response = messagebox.askyesno(
            "مكتبات مفقودة",
            f"المكتبات التالية مطلوبة ولكنها غير مثبتة:\n\n"
            f"{chr(10).join(f'- {pkg}' for pkg in missing_packages)}\n\n"
            f"هل تريد تثبيتها تلقائياً؟"
        )

        if response:
            return install_dependencies(missing_packages)
        else:
            messagebox.showinfo(
                "تعليمات التثبيت",
                f"لتثبيت المكتبات المطلوبة، قم بتشغيل:\n\n"
                f"pip install {' '.join(missing_packages)}"
            )
            return False

    return True

def install_dependencies(packages):
    """تثبيت المكتبات المطلوبة"""
    try:
        for package in packages:
            print(f"تثبيت {package}...")
            result = subprocess.run(
                [sys.executable, "-m", "pip", "install", package],
                capture_output=True,
                text=True
            )

            if result.returncode != 0:
                messagebox.showerror(
                    "خطأ في التثبيت",
                    f"فشل في تثبيت {package}:\n{result.stderr}"
                )
                return False

        messagebox.showinfo("نجح التثبيت", "تم تثبيت جميع المكتبات بنجاح!")
        return True

    except Exception as e:
        messagebox.showerror("خطأ", f"حدث خطأ أثناء التثبيت: {e}")
        return False

def setup_environment():
    """إعداد بيئة التطبيق"""
    try:
        # استيراد المسارات من config
        from config import (
            BASE_DIR, DATA_DIR, REPORTS_DIR, BACKUPS_DIR,
            LOGS_DIR, ASSETS_DIR, ICONS_DIR
        )

        # إنشاء المجلدات المطلوبة
        directories = [
            DATA_DIR, REPORTS_DIR, BACKUPS_DIR,
            LOGS_DIR, ASSETS_DIR, ICONS_DIR
        ]

        for directory in directories:
            directory.mkdir(exist_ok=True)

        print(f"[نجح] تم إعداد بيئة التطبيق في: {BASE_DIR}")
        return True

    except Exception as e:
        messagebox.showerror("خطأ في الإعداد", f"فشل في إعداد بيئة التطبيق: {e}")
        return False

# استيراد الوحدات بعد التحقق من المتطلبات
try:
    # فرض استخدام Arial أولاً
    import font_manager

    from config import APP_CONFIG, UI_CONFIG, get_message
    from database import db_manager
    from models import (
        User, Department, Section, Beneficiary, Item, Category,
        InventoryMovement, Transaction, TransactionItem
    )
    from auth_manager import AuthManager
    from ui.new_login_window import NewLoginWindow
    from ui.main_window import MainWindow
    from ui.splash_screen import SplashScreen
    from utils.logger import setup_logger
    from utils.backup_manager import BackupManager
    from safe_window_manager import safe_window_manager, create_safe_window
except ImportError as e:
    print(f"خطأ في استيراد الوحدات: {e}")
    print("يرجى التأكد من تثبيت جميع المكتبات المطلوبة")

class StoresManagementApp:
    """التطبيق الرئيسي لإدارة المخازن"""

    def __init__(self):
        self.logger = setup_logger()
        self.auth_manager = AuthManager()
        self.backup_manager = BackupManager()
        self.current_user = None
        self.main_window = None
        self.login_window = None
        self.root = None

        # إعداد التطبيق
        self.setup_application()

    def setup_application(self):
        """إعداد التطبيق الأساسي"""
        try:
            # إنشاء النافذة الجذر
            self.root = ttk_bs.Window(
                title=APP_CONFIG["app_name"],
                themename=APP_CONFIG["theme"],
                size=(APP_CONFIG["window_width"], APP_CONFIG["window_height"]),
                minsize=(APP_CONFIG["min_width"], APP_CONFIG["min_height"]),
                resizable=(True, True)
            )

            # إخفاء النافذة الجذر في البداية
            self.root.withdraw()

            # تعيين أيقونة التطبيق
            self.set_app_icon()

            # تعيين موضع النافذة في المنتصف
            self.center_window()

            # إعداد معالجات الأحداث
            self.setup_event_handlers()

            # إعداد الستايل
            self.setup_styles()

            self.logger.info("تم إعداد التطبيق بنجاح")

        except Exception as e:
            self.logger.error(f"خطأ في إعداد التطبيق: {e}")
            messagebox.showerror("خطأ", f"فشل في إعداد التطبيق: {e}")
            sys.exit(1)

    def set_app_icon(self):
        """تعيين أيقونة التطبيق"""
        try:
            icon_path = Path(__file__).parent / "assets" / "icons" / "app_icon.ico"
            if icon_path.exists():
                self.root.iconbitmap(str(icon_path))
        except Exception as e:
            self.logger.warning(f"تعذر تحميل أيقونة التطبيق: {e}")

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        try:
            self.root.update_idletasks()

            # الحصول على أبعاد الشاشة
            screen_width = self.root.winfo_screenwidth()
            screen_height = self.root.winfo_screenheight()

            # حساب موضع النافذة
            x = (screen_width - APP_CONFIG["window_width"]) // 2
            y = (screen_height - APP_CONFIG["window_height"]) // 2

            # تعيين موضع النافذة
            geometry = (
                f"{APP_CONFIG['window_width']}x{APP_CONFIG['window_height']}"
                f"+{x}+{y}"
            )
            self.root.geometry(geometry)

        except Exception as e:
            self.logger.warning(f"تعذر توسيط النافذة: {e}")

    def setup_event_handlers(self):
        """إعداد معالجات الأحداث"""
        # معالج إغلاق التطبيق
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # معالج تغيير حجم النافذة
        self.root.bind("<Configure>", self.on_window_resize)

        # اختصارات لوحة المفاتيح
        self.setup_keyboard_shortcuts()

    def setup_keyboard_shortcuts(self):
        """إعداد اختصارات لوحة المفاتيح"""
        # Ctrl + Q للخروج
        self.root.bind("<Control-q>", lambda e: self.on_closing())

        # F11 للشاشة الكاملة
        self.root.bind("<F11>", self.toggle_fullscreen)

        # Escape للخروج من الشاشة الكاملة
        self.root.bind("<Escape>", self.exit_fullscreen)

        # Ctrl + B للنسخ الاحتياطي
        self.root.bind("<Control-b>", lambda e: self.create_backup())

        # F5 لتحديث البيانات
        self.root.bind("<F5>", lambda e: self.refresh_data())

    def setup_styles(self):
        """إعداد أنماط واجهة المستخدم"""
        try:
            style = ttk_bs.Style()

            # تخصيص الألوان بطريقة آمنة
            try:
                style.configure("Primary.TButton",
                              background=UI_CONFIG["primary_color"],
                              foreground="white")
            except Exception:
                pass

            try:
                style.configure("Success.TButton",
                              background=UI_CONFIG["success_color"],
                              foreground="white")
            except Exception:
                pass

            try:
                style.configure("Warning.TButton",
                              background=UI_CONFIG["warning_color"],
                              foreground="white")
            except Exception:
                pass

            try:
                style.configure("Danger.TButton",
                              background=UI_CONFIG["danger_color"],
                              foreground="white")
            except Exception:
                pass

            # تخصيص الخطوط (معطل مؤقتاً)
            # style.configure("Heading.TLabel",
            #                 font=(UI_CONFIG["font_family"],
            #                       UI_CONFIG["font_size_large"], "bold"))

            # style.configure("Title.TLabel",
            #                 font=(UI_CONFIG["font_family"],
            #                       UI_CONFIG["font_size_xlarge"], "bold"))

        except Exception as e:
            self.logger.warning(f"تعذر إعداد الأنماط: {e}")

    def show_splash_screen(self):
        """عرض شاشة البداية"""
        try:
            splash = SplashScreen(self.root)
            splash.show()

            # تحديث شاشة البداية
            for i in range(101):
                splash.update_progress(i, f"جاري التحميل... {i}%")
                self.root.update()
                threading.Event().wait(0.02)  # تأخير قصير

            splash.hide()

        except Exception as e:
            self.logger.warning(f"تعذر عرض شاشة البداية: {e}")

    def show_login_window(self):
        """عرض نافذة تسجيل الدخول المحسنة في وسط الشاشة"""
        try:
            print("[دخول] إنشاء نافذة تسجيل الدخول المحسنة...")

            # التأكد من إخفاء النافذة الجذر تماماً
            self.root.withdraw()
            self.root.update()

            # استخدام نافذة تسجيل الدخول الجديدة والمحسنة
            self.login_window = NewLoginWindow(self.auth_manager)
            self.login_window.on_login_success = self.on_login_success

            print("[نجح] تم إنشاء نافذة تسجيل الدخول المحسنة")
            print("[حجم] 400x300 - موسطة تماماً في وسط الشاشة")
            print("[تصميم] محسن مع ألوان النظام")
            print("[موضع] النافذة تفتح في الوسط بدقة")

            # عرض النافذة
            self.login_window.show()

        except Exception as e:
            self.logger.error(f"خطأ في عرض نافذة تسجيل الدخول: {e}")
            print(f"[خطأ] {e}")
            messagebox.showerror("خطأ", f"فشل في عرض نافذة تسجيل الدخول: {e}")

    def on_login_success(self, user):
        """معالج نجاح تسجيل الدخول"""
        try:
            print(f"[تتبع] بداية on_login_success للمستخدم: {user.username}")
            
            self.current_user = user
            self.logger.info(f"تم تسجيل دخول المستخدم: {user.username}")

            print("[تتبع] سيتم عرض النافذة الرئيسية...")
            
            # عرض النافذة الرئيسية (ستستخدم نفس النافذة الجذر)
            self.show_main_window()
            
            print("[تتبع] تم عرض النافذة الرئيسية بنجاح")

        except Exception as e:
            print(f"[خطأ] في on_login_success: {e}")
            import traceback
            traceback.print_exc()
            self.logger.error(f"خطأ في معالجة نجاح تسجيل الدخول: {e}")
            messagebox.showerror("خطأ", f"فشل في تسجيل الدخول: {e}")

    def show_main_window(self):
        """عرض النافذة الرئيسية"""
        try:
            print("[تتبع] بداية show_main_window")
            
            # استخدام النافذة الجذر نفسها بدلاً من إنشاء نافذة جديدة
            # تحديث عنوان النافذة
            print("[تتبع] تحديث عنوان النافذة...")
            self.root.title(f"{APP_CONFIG['app_name']} - {self.current_user.full_name}")

            # مسح محتوى النافذة الحالي
            print("[تتبع] مسح محتوى النافذة الحالي...")
            for widget in self.root.winfo_children():
                widget.destroy()

            # إعادة تعيين خصائص النافذة للشاشة الرئيسية
            print("[تتبع] إعادة تعيين خصائص النافذة...")
            geometry = f"{APP_CONFIG['window_width']}x{APP_CONFIG['window_height']}"
            self.root.geometry(geometry)
            self.root.resizable(True, True)

            # إزالة خاصية toolwindow لتفعيل أزرار التكبير والتصغير
            try:
                self.root.attributes('-toolwindow', False)
            except tk.TclError:
                pass

            print("[تتبع] إنشاء النافذة الرئيسية...")
            # إنشاء النافذة الرئيسية في النافذة الجذر
            self.main_window = MainWindow(self.root, self.current_user, self)
            print("[تتبع] تم إنشاء النافذة الرئيسية بنجاح")

            # معالج إغلاق النافذة الرئيسية
            self.root.protocol("WM_DELETE_WINDOW", self.on_main_window_closing)

            # توسيط النافذة
            print("[تتبع] توسيط النافذة...")
            self.center_window()

            # إظهار النافذة وجعلها في المقدمة
            print("[تتبع] إظهار النافذة...")
            self.root.deiconify()
            self.root.focus_force()
            
            print("[تتبع] تم عرض النافذة الرئيسية بنجاح")

        except Exception as e:
            print(f"[خطأ] في show_main_window: {e}")
            import traceback
            traceback.print_exc()
            self.logger.error(f"خطأ في عرض النافذة الرئيسية: {e}")
            messagebox.showerror("خطأ", f"فشل في عرض النافذة الرئيسية: {e}")

    def on_main_window_closing(self):
        """معالج إغلاق النافذة الرئيسية"""
        try:
            # استدعاء دالة الإغلاق العامة
            self.on_closing()

        except Exception as e:
            self.logger.error(f"خطأ في إغلاق النافذة الرئيسية: {e}")
            self.root.quit()

    def on_closing(self):
        """معالج إغلاق التطبيق"""
        try:
            if messagebox.askyesno("تأكيد الخروج", "هل تريد الخروج من التطبيق؟"):
                # إنشاء نسخة احتياطية تلقائية (معطل)
                if APP_CONFIG.get("auto_backup", False):
                    self.create_backup()

                # تسجيل خروج المستخدم
                if self.current_user:
                    self.auth_manager.logout(self.current_user)

                # إغلاق جميع النوافذ الآمنة
                try:
                    from safe_window_manager import close_all_safe_windows
                    close_all_safe_windows()
                except (ImportError, AttributeError):
                    pass

                # إغلاق قاعدة البيانات
                db_manager.disconnect()

                # إغلاق التطبيق
                self.root.quit()

        except Exception as e:
            self.logger.error(f"خطأ في إغلاق التطبيق: {e}")
            # محاولة إغلاق آمن في حالة الخطأ
            try:
                from safe_window_manager import close_all_safe_windows
                close_all_safe_windows()
            except (ImportError, AttributeError):
                pass
            self.root.quit()

    def on_window_resize(self, event):
        """معالج تغيير حجم النافذة"""
        # يمكن إضافة منطق إضافي هنا عند الحاجة
        pass

    def toggle_fullscreen(self, event=None):
        """تبديل وضع الشاشة الكاملة"""
        try:
            current_state = self.root.attributes("-fullscreen")
            self.root.attributes("-fullscreen", not current_state)
        except Exception as e:
            self.logger.warning(f"تعذر تبديل وضع الشاشة الكاملة: {e}")

    def exit_fullscreen(self, event=None):
        """الخروج من وضع الشاشة الكاملة"""
        try:
            self.root.attributes("-fullscreen", False)
        except Exception as e:
            self.logger.warning(f"تعذر الخروج من وضع الشاشة الكاملة: {e}")

    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        try:
            if self.backup_manager.create_backup():
                messagebox.showinfo("نجح", "تم إنشاء النسخة الاحتياطية بنجاح")
            else:
                messagebox.showerror("خطأ", "فشل في إنشاء النسخة الاحتياطية")
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            messagebox.showerror("خطأ", f"فشل في إنشاء النسخة الاحتياطية: {e}")

    def refresh_data(self):
        """تحديث البيانات"""
        try:
            if self.main_window:
                self.main_window.refresh_all_data()
                messagebox.showinfo("تم", "تم تحديث البيانات بنجاح")
        except Exception as e:
            self.logger.error(f"خطأ في تحديث البيانات: {e}")
            messagebox.showerror("خطأ", f"فشل في تحديث البيانات: {e}")

    def run(self):
        """تشغيل التطبيق"""
        try:
            # عرض شاشة البداية (مع النافذة الجذر مخفية)
            self.show_splash_screen()

            # عرض نافذة تسجيل الدخول مباشرة (النافذة الجذر مخفية)
            self.show_login_window()

            # بدء حلقة الأحداث الرئيسية
            self.root.mainloop()

        except Exception as e:
            self.logger.error(f"خطأ في تشغيل التطبيق: {e}")
            messagebox.showerror("خطأ فادح", f"فشل في تشغيل التطبيق: {e}")
            sys.exit(1)
        finally:
            # تنظيف الموارد
            try:
                db_manager.disconnect()
            except (AttributeError, Exception):
                pass

def clear_widget_cache():
    """مسح ذاكرة التخزين المؤقت للعناصر لمنع أخطاء invalid command name"""
    try:
        import gc

        print("[تنظيف] مسح ذاكرة التخزين المؤقت للعناصر...")

        # مسح جميع النوافذ المفتوحة أولاً
        try:
            if hasattr(tk, '_default_root') and tk._default_root:
                # البحث عن جميع النوافذ الفرعية
                for widget in tk._default_root.winfo_children():
                    try:
                        if hasattr(widget, 'destroy'):
                            widget.destroy()
                    except Exception:
                        pass
        except Exception:
            pass

        # مسح متغيرات tkinter
        try:
            if hasattr(tk, '_default_root') and tk._default_root:
                try:
                    tk._default_root.quit()
                except Exception:
                    pass
                try:
                    tk._default_root.destroy()
                except Exception:
                    pass
                tk._default_root = None
        except Exception:
            pass

        # مسح وحدات ttkbootstrap من الذاكرة (بحذر)
        modules_to_remove = []
        for module_name in sys.modules:
            if any(keyword in module_name for keyword in ['ttkbootstrap', 'tkinter']):
                if module_name not in ['tkinter', 'ttkbootstrap']:
                    modules_to_remove.append(module_name)

        for module_name in modules_to_remove:
            try:
                if module_name in sys.modules:
                    del sys.modules[module_name]
            except Exception:
                pass

        # تشغيل جامع القمامة
        gc.collect()

        print("[نجح] مسح ذاكرة التخزين المؤقت")

    except Exception as e:
        print(f"تحذير: خطأ في مسح ذاكرة التخزين المؤقت: {e}")

def fix_display_column_error():
    """إصلاح خطأ Display column cannot be set"""
    try:
        print("[إصلاح] خطأ Display column...")

        # مسح ذاكرة التخزين المؤقت أولاً
        clear_widget_cache()

        # إعادة تعيين متغيرات tkinter
        import gc

        # إغلاق جميع النوافذ المفتوحة
        try:
            if hasattr(tk, '_default_root') and tk._default_root:
                for child in tk._default_root.winfo_children():
                    try:
                        child.destroy()
                    except Exception:
                        pass
                tk._default_root.destroy()
            tk._default_root = None
        except Exception:
            pass

        # مسح مراجع Treeview المكسورة
        try:
            import sys
            for module_name in list(sys.modules.keys()):
                if 'treeview' in module_name.lower() or 'ttk' in module_name.lower():
                    if module_name not in ['tkinter.ttk']:
                        try:
                            del sys.modules[module_name]
                        except Exception:
                            pass
        except Exception:
            pass

        # تشغيل جامع القمامة
        gc.collect()

        print("[نجح] إصلاح خطأ Display column")
        return True

    except Exception as e:
        print(f"[فشل] في إصلاح خطأ Display column: {e}")
        return False

def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    try:
        print("[نظام] إدارة المخازن والمستودعات")
        print("=" * 50)

        # قمع رسائل أخطاء واجهة المستخدم (معطل مؤقتاً للتشخيص)
        print("[تشخيص] تم تعطيل الوضع الصامت مؤقتاً لعرض الأخطاء...")
        # suppress_ui_errors()

        # مسح ذاكرة التخزين المؤقت لمنع أخطاء العناصر
        print("[تنظيف] مسح ذاكرة التخزين المؤقت...")
        clear_widget_cache()

        # التحقق من إصدار Python
        if not check_python_version():
            return

        print("[نجح] إصدار Python مناسب")

        # إعداد بيئة التطبيق
        if not setup_environment():
            return

        print("[نجح] إعداد بيئة التطبيق")

        # التحقق من المكتبات المطلوبة
        if not check_dependencies():
            return

        print("[نجح] جميع المكتبات متوفرة")

        # بدء التطبيق
        print("[بدء] تشغيل التطبيق...")
        app = StoresManagementApp()
        app.run()

    except KeyboardInterrupt:
        print("\n[إيقاف] تم إيقاف التطبيق بواسطة المستخدم")
    except Exception as e:
        error_msg = f"حدث خطأ فادح في التطبيق: {e}"
        print(f"[خطأ] {error_msg}")

        # إذا كان الخطأ متعلق بـ invalid command name أو display column، جرب الإصلاح
        error_keywords = [
            "invalid command name", "toplevel", "display column", "cannot be set",
            "bad window path name", "application has been destroyed",
            "winfo_", "tkinter", "ttkbootstrap"
        ]

        if any(keyword in str(e).lower() for keyword in error_keywords):
            print("[إصلاح] محاولة إصلاح الخطأ...")
            try:
                if "display column" in str(e).lower() or "treeview" in str(e).lower():
                    fix_display_column_error()
                else:
                    clear_widget_cache()

                print("✅ تم تطبيق الإصلاح")
                print("💡 الحلول المقترحة:")
                print("   1. شغل: python fix_widget_error.py")
                print("   2. أو استخدم: restart_safe.bat")
                print("   3. أو أعد تشغيل التطبيق مباشرة")
                print("   4. في حالة استمرار المشكلة، أعد تشغيل الكمبيوتر")

            except Exception as fix_error:
                print(f"[فشل] في الإصلاح التلقائي: {fix_error}")
                print("[نصيحة] جرب تشغيل: python fix_widget_error.py")

        # تسجيل الخطأ في ملف السجل فقط (بدون عرض رسالة للمستخدم)
        try:
            from utils.logger import setup_logger
            logger = setup_logger()
            logger.error(error_msg)
        except Exception:
            pass

        sys.exit(1)

if __name__ == "__main__":
    # التحقق من وجود معامل لمسح بيانات المخزون
    if len(sys.argv) > 1 and sys.argv[1] == "--clear-inventory":
        print("[أداة] تشغيل مسح بيانات المخزون...")
        try:
            from clear_inventory_data import clear_inventory_data
            clear_inventory_data()
        except ImportError:
            print("[تحذير] لم يتم العثور على ملف clear_inventory_data.py")
            print("[نصيحة] تأكد من وجود الملف في نفس مجلد التطبيق")
        except Exception as e:
            print(f"[خطأ] في تشغيل أداة مسح البيانات: {e}")
    else:
        # تشغيل التطبيق العادي
        main()
