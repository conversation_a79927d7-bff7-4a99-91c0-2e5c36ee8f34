@echo off
chcp 65001 >nul
echo 🧹 تنظيف يدوي للمشروع
echo ========================

echo.
echo 🗑️ حذف ملفات الاختبار غير المهمة...

REM حذف ملفات الاختبار (احتفظ بالمهمة فقط)
for %%f in (test_*.py) do (
    if not "%%f"=="test_inventory_simple.py" (
        if not "%%f"=="test_enhanced_add_item.py" (
            if not "%%f"=="test_duplication_message.py" (
                if not "%%f"=="test_inventory_fixes.py" (
                    echo حذف: %%f
                    del "%%f" 2>nul
                )
            )
        )
    )
)

echo.
echo 🔧 حذف ملفات التصحيح والعرض...

REM حذف ملفات debug و demo
for %%f in (debug_*.py) do (
    echo حذف: %%f
    del "%%f" 2>nul
)

for %%f in (demo_*.py) do (
    echo حذف: %%f
    del "%%f" 2>nul
)

echo.
echo 📚 حذف ملفات التوثيق المكررة...

REM حذف ملفات .md المكررة (احتفظ بالمهمة فقط)
for %%f in (*.md) do (
    if not "%%f"=="ملخص_إصلاحات_إدارة_الأصناف.md" (
        if not "%%f"=="إصلاحات_شاشة_إدارة_الأصناف.md" (
            if not "%%f"=="ملخص_التحسينات_المطبقة.md" (
                if not "%%f"=="تحسينات_شاشة_إضافة_الأصناف.md" (
                    if not "%%f"=="README_تحسينات_إضافة_الأصناف.md" (
                        if not "%%f"=="دليل_استخدام_أزرار_الإكسل.md" (
                            echo حذف توثيق: %%f
                            del "%%f" 2>nul
                        )
                    )
                )
            )
        )
    )
)

echo.
echo ⚙️ حذف ملفات .spec القديمة...

REM احتفظ بملف spec واحد فقط
set count=0
for %%f in (*.spec) do (
    set /a count+=1
    if !count! gtr 1 (
        echo حذف spec: %%f
        del "%%f" 2>nul
    )
)

echo.
echo 📜 حذف ملفات .bat غير المهمة...

REM حذف ملفات bat (احتفظ بالمهمة فقط)
for %%f in (*.bat) do (
    if not "%%f"=="quick_cleanup.bat" (
        if not "%%f"=="manual_cleanup.bat" (
            echo حذف bat: %%f
            del "%%f" 2>nul
        )
    )
)

echo.
echo 🧹 حذف ملفات Python المؤقتة...

REM حذف ملفات Python المؤقتة
for /r %%i in (*.pyc) do (
    echo حذف: %%i
    del "%%i" 2>nul
)

for /r %%i in (*.pyo) do (
    echo حذف: %%i
    del "%%i" 2>nul
)

echo.
echo 📋 تنظيف مجلد السجلات...

REM تنظيف مجلد logs (احتفظ بآخر ملف فقط)
if exist "logs" (
    cd logs
    set count=0
    for /f "tokens=*" %%f in ('dir *.log /b /o-d 2^>nul') do (
        set /a count+=1
        if !count! gtr 1 (
            echo حذف سجل قديم: %%f
            del "%%f" 2>nul
        )
    )
    cd ..
)

echo.
echo 🎉 تم التنظيف اليدوي بنجاح!
echo 🚀 البرنامج يجب أن يكون أسرع الآن
echo.
echo ✅ الملفات المهمة المحتفظ بها:
echo   • test_inventory_simple.py
echo   • test_enhanced_add_item.py
echo   • test_duplication_message.py
echo   • test_inventory_fixes.py
echo   • ملخص_إصلاحات_إدارة_الأصناف.md
echo   • إصلاحات_شاشة_إدارة_الأصناف.md
echo   • وملفات أخرى مهمة...
echo.
pause