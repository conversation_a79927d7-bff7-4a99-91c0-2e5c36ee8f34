#!/usr/bin/env python3
"""
أداة مسح البيانات - نظام إدارة المخازن
Data Clearing Tool - Desktop Stores Management System

هذا الملف يقوم بمسح جميع البيانات من جداول:
- عمليات الصرف (transaction_items)
- الأصناف المضافة (added_items)
- المعاملات (transactions)
- الأصناف (items)
"""

import sqlite3
import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from database import db_manager

def clear_all_data():
    """مسح جميع البيانات من الجداول المحددة"""
    
    print("🗑️ بدء عملية مسح البيانات...")
    
    try:
        # قائمة الجداول التي سيتم مسحها
        tables_to_clear = [
            'transaction_items',    # عناصر المعاملات
            'transactions',         # المعاملات
            'added_items',         # الأصناف المضافة
            'items',               # الأصناف
            'inventory_movements', # حركات المخزون
        ]
        
        # تعطيل المفاتيح الخارجية مؤقتاً
        db_manager.execute_query("PRAGMA foreign_keys = OFF")
        
        # مسح البيانات من كل جدول
        for table in tables_to_clear:
            try:
                # التحقق من وجود الجدول
                check_query = "SELECT name FROM sqlite_master WHERE type='table' AND name=?"
                result = db_manager.fetch_one(check_query, (table,))
                
                if result:
                    # عد الصفوف قبل المسح
                    count_query = f"SELECT COUNT(*) FROM {table}"
                    count_result = db_manager.fetch_one(count_query)
                    row_count = count_result[0] if count_result else 0
                    
                    if row_count > 0:
                        # مسح البيانات
                        delete_query = f"DELETE FROM {table}"
                        db_manager.execute_query(delete_query)
                        
                        # إعادة تعيين العداد التلقائي
                        reset_query = f"DELETE FROM sqlite_sequence WHERE name='{table}'"
                        db_manager.execute_query(reset_query)
                        
                        print(f"✅ تم مسح {row_count} صف من جدول {table}")
                    else:
                        print(f"ℹ️ جدول {table} فارغ بالفعل")
                else:
                    print(f"⚠️ جدول {table} غير موجود")
                    
            except Exception as e:
                print(f"❌ خطأ في مسح جدول {table}: {str(e)}")
        
        # إعادة تفعيل المفاتيح الخارجية
        db_manager.execute_query("PRAGMA foreign_keys = ON")
        
        # تنظيف قاعدة البيانات
        db_manager.execute_query("VACUUM")
        
        print("✅ تم مسح جميع البيانات بنجاح!")
        print("📊 قاعدة البيانات جاهزة للاستخدام مع بيانات جديدة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ عام في مسح البيانات: {str(e)}")
        return False

def show_data_summary():
    """عرض ملخص البيانات الحالية"""
    
    print("\n📊 ملخص البيانات الحالية:")
    print("=" * 50)
    
    tables_info = [
        ('transactions', 'المعاملات'),
        ('transaction_items', 'عناصر المعاملات'),
        ('added_items', 'الأصناف المضافة'),
        ('items', 'الأصناف'),
        ('inventory_movements', 'حركات المخزون'),
        ('beneficiaries', 'المستفيدين'),
        ('departments', 'الأقسام'),
        ('sections', 'الشعب'),
        ('units', 'الوحدات'),
    ]
    
    for table_name, arabic_name in tables_info:
        try:
            # التحقق من وجود الجدول
            check_query = "SELECT name FROM sqlite_master WHERE type='table' AND name=?"
            result = db_manager.fetch_one(check_query, (table_name,))
            
            if result:
                count_query = f"SELECT COUNT(*) FROM {table_name}"
                count_result = db_manager.fetch_one(count_query)
                row_count = count_result[0] if count_result else 0
                print(f"📋 {arabic_name} ({table_name}): {row_count} صف")
            else:
                print(f"⚠️ {arabic_name} ({table_name}): الجدول غير موجود")
                
        except Exception as e:
            print(f"❌ خطأ في قراءة {arabic_name}: {str(e)}")

def main():
    """الدالة الرئيسية"""
    
    print("🏪 أداة مسح البيانات - نظام إدارة المخازن")
    print("=" * 60)
    
    # عرض البيانات الحالية
    show_data_summary()
    
    print("\n⚠️ تحذير: هذه العملية ستقوم بمسح جميع البيانات التالية:")
    print("   • جميع المعاملات وعمليات الصرف")
    print("   • جميع الأصناف المضافة")
    print("   • جميع حركات المخزون")
    print("   • جميع عناصر المعاملات")
    
    # طلب التأكيد
    response = input("\n❓ هل تريد المتابعة؟ (اكتب 'نعم' للتأكيد): ").strip()
    
    if response.lower() in ['نعم', 'yes', 'y']:
        print("\n🔄 بدء عملية المسح...")
        
        if clear_all_data():
            print("\n🎉 تم مسح البيانات بنجاح!")
            print("💡 يمكنك الآن تشغيل البرنامج وإدخال بيانات جديدة")
            
            # عرض البيانات بعد المسح
            show_data_summary()
        else:
            print("\n❌ فشل في مسح البيانات")
            
    else:
        print("\n🚫 تم إلغاء العملية")

if __name__ == "__main__":
    main()