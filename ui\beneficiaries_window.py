"""
شاشة إدارة المستفيدين - تطبيق إدارة المخازن
Beneficiaries Management Window - Desktop Stores Management System
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog, filedialog
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime, date
from typing import List, Optional
import pandas as pd
import os
from pathlib import Path

from config import APP_CONFIG, UI_CONFIG, get_message
from models import Beneficiary, Department, Section, User, Unit
from ui.success_message import AutoSuccessMessage
from permissions_manager import check_permission, can_access
from ui.global_shortcuts import GlobalShortcuts, ContextHandler

class BeneficiariesWindow:
    """نافذة إدارة المستفيدين"""
    
    def __init__(self, parent, main_window):
        self.parent = parent
        self.main_window = main_window
        self.beneficiaries_data = []
        self.filtered_data = []
        self.current_beneficiary = None
        self.tree = None  # تهيئة المتغير
        
        # متغيرات البحث والفلترة
        self.search_var = tk.StringVar()
        self.department_filter_var = tk.StringVar()
        self.status_filter_var = tk.StringVar()
        
        self.setup_beneficiaries_interface()
        
        # ربط أحداث البحث بعد إنشاء الواجهة
        self.search_var.trace('w', self.on_search_change)
        self.department_filter_var.trace('w', self.on_filter_change)
        self.status_filter_var.trace('w', self.on_filter_change)
        
        self.load_beneficiaries_data()

        # تفعيل مفاتيح الاختصار
        self.setup_shortcuts()
    
    def setup_beneficiaries_interface(self):
        """إعداد واجهة المستفيدين"""
        # مسح المحتوى الحالي
        self.main_window.clear_main_content()
        
        # تحديث شريط الحالة
        if hasattr(self.main_window, 'status_var'):
            self.main_window.status_var.set("إدارة المستفيدين")
        
        # العنوان الرئيسي
        header_frame = ttk_bs.Frame(self.main_window.main_frame)
        header_frame.pack(fill=X, pady=10)
        
        ttk_bs.Label(
            header_frame,
            text="👥 إدارة المستفيدين",
            bootstyle="primary"
        ).pack(side=LEFT)
        
        # أزرار العمليات الأساسية
        buttons_frame = ttk_bs.Frame(header_frame)
        buttons_frame.pack(side=RIGHT)

        # الصف الأول من الأزرار
        row1_frame = ttk_bs.Frame(buttons_frame)
        row1_frame.pack(fill=X, pady=(0, 5))

        # زر إضافة مستفيد - فقط للمستخدمين الذين لديهم صلاحية إضافة
        if check_permission(self.main_window.current_user, 'beneficiaries', 'add'):
            ttk_bs.Button(
                row1_frame,
                text="➕ إضافة مستفيد",
                command=self.add_beneficiary,
                bootstyle="success",
                width=20
            ).pack(side=LEFT, padx=2)

        # زر تعديل - فقط للمستخدمين الذين لديهم صلاحية تعديل
        if check_permission(self.main_window.current_user, 'beneficiaries', 'edit'):
            ttk_bs.Button(
                row1_frame,
                text="✏️ تعديل",
                command=self.edit_beneficiary,
                bootstyle="warning",
                width=15
            ).pack(side=LEFT, padx=2)

        # زر حذف - فقط للمستخدمين الذين لديهم صلاحية حذف
        if check_permission(self.main_window.current_user, 'beneficiaries', 'delete'):
            ttk_bs.Button(
                row1_frame,
                text="🗑️ حذف",
                command=self.delete_beneficiary,
                bootstyle="danger",
                width=18
            ).pack(side=LEFT, padx=2)

        ttk_bs.Button(
            row1_frame,
            text="🔄 تحديث",
            command=self.refresh_data,
            bootstyle="info",
            width=15
        ).pack(side=LEFT, padx=2)

        # الصف الثاني من الأزرار (Excel)
        row2_frame = ttk_bs.Frame(buttons_frame)
        row2_frame.pack(fill=X)

        # زر استيراد Excel - فقط للمستخدمين الذين لديهم صلاحية إضافة
        if check_permission(self.main_window.current_user, 'beneficiaries', 'add'):
            ttk_bs.Button(
                row2_frame,
                text="📥 استيراد Excel",
                command=self.import_excel,
                bootstyle="primary",
                width=20
            ).pack(side=LEFT, padx=2)

        # زر تصدير Excel - متاح للجميع الذين يمكنهم عرض البيانات
        if check_permission(self.main_window.current_user, 'beneficiaries', 'view'):
            ttk_bs.Button(
                row2_frame,
                text="📤 تصدير Excel",
                command=self.export_excel,
                bootstyle="secondary",
                width=20
            ).pack(side=LEFT, padx=2)

        ttk_bs.Button(
            row2_frame,
            text="📋 نموذج البيانات",
            command=self.download_template,
            bootstyle="light",
            width=22
        ).pack(side=LEFT, padx=2)

        # زر حذف الكل - فقط لمديري النظام
        if self.main_window.current_user.is_admin:
            ttk_bs.Button(
                row2_frame,
                text="🗑️💥 حذف الكل",
                command=self.delete_all_beneficiaries,
                bootstyle="danger-outline",
                width=20
            ).pack(side=LEFT, padx=2)
        
        # شريط البحث والفلترة
        self.create_search_filter_bar()
        
        # جدول المستفيدين
        self.create_beneficiaries_table()
        
        # شريط المعلومات
        self.create_info_bar()
    
    def create_search_filter_bar(self):
        """إنشاء شريط البحث والفلترة"""
        search_frame = ttk_bs.Frame(self.main_window.main_frame)
        search_frame.pack(fill=X, pady=10)
        
        # البحث
        ttk_bs.Label(search_frame, text="🔍 البحث", width=12).pack(side=LEFT, padx=5)
        search_entry = ttk_bs.Entry(
            search_frame,
            textvariable=self.search_var,
            width=25
        )
        search_entry.pack(side=LEFT, padx=5)

        # فلتر الإدارة
        ttk_bs.Label(search_frame, text="الإدارة", width=12).pack(side=LEFT, padx=(20, 5))
        department_combo = ttk_bs.Combobox(
            search_frame,
            textvariable=self.department_filter_var,
            width=20,
            state="readonly"
        )
        department_combo.pack(side=LEFT, padx=5)

        # فلتر الحالة
        ttk_bs.Label(search_frame, text="الحالة", width=12).pack(side=LEFT, padx=(20, 5))
        status_combo = ttk_bs.Combobox(
            search_frame,
            textvariable=self.status_filter_var,
            values=["الكل", "نشط", "غير نشط"],
            width=15,
            state="readonly"
        )
        status_combo.set("الكل")
        status_combo.pack(side=LEFT, padx=5)
        
        # تحميل قائمة الإدارات
        self.load_departments_filter(department_combo)
    
    def create_beneficiaries_table(self):
        """إنشاء جدول المستفيدين"""
        table_frame = ttk_bs.Frame(self.main_window.main_frame)
        table_frame.pack(fill=BOTH, expand=True, pady=10)
        
        # أعمدة الجدول - بدون عمود الهاتف
        columns = ("id", "name", "number", "rank", "unit", "department", "section", "data_entry_user", "status")
        column_names = ("الرقم", "الاسم", "الرقم العام", "الرتبة", "الوحدة", "الإدارة", "القسم", "مدخل البيانات", "الحالة")
        
        self.tree = ttk_bs.Treeview(
            table_frame,
            columns=columns,
            show="headings",
            height=15
        )
        
        # تعيين عناوين الأعمدة
        for col, name in zip(columns, column_names):
            self.tree.heading(col, text=name)
            if col == "id":
                self.tree.column(col, width=60, anchor=CENTER)
            elif col == "name":
                self.tree.column(col, width=180, anchor=E)
            elif col == "number":
                self.tree.column(col, width=100, anchor=CENTER)
            elif col == "rank":
                self.tree.column(col, width=120, anchor=CENTER)
            elif col == "unit":
                self.tree.column(col, width=120, anchor=E)
            elif col == "department":
                self.tree.column(col, width=120, anchor=E)
            elif col == "section":
                self.tree.column(col, width=120, anchor=E)
            elif col == "data_entry_user":
                self.tree.column(col, width=120, anchor=E)
            elif col == "status":
                self.tree.column(col, width=80, anchor=CENTER)
        
        # شريط التمرير
        scrollbar = ttk_bs.Scrollbar(table_frame, orient=VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)
        
        # ربط أحداث الجدول
        self.tree.bind("<Double-1>", self.on_item_double_click)
        self.tree.bind("<<TreeviewSelect>>", self.on_item_select)
        self.tree.bind("<Button-3>", self.show_context_menu)  # النقر بالزر الأيمن

        # إنشاء القائمة المنبثقة
        self.create_context_menu()
    
    def create_info_bar(self):
        """إنشاء شريط المعلومات"""
        info_frame = ttk_bs.Frame(self.main_window.main_frame)
        info_frame.pack(fill=X, pady=5)
        
        self.info_label = ttk_bs.Label(
            info_frame,
            text="جاري تحميل البيانات...",
            bootstyle="secondary"
        )
        self.info_label.pack(side=LEFT)
        
        # معلومات الإحصائيات
        self.stats_label = ttk_bs.Label(
            info_frame,
            text="",
            bootstyle="info"
        )
        self.stats_label.pack(side=RIGHT)

    def create_context_menu(self):
        """إنشاء القائمة المنبثقة"""
        self.context_menu = tk.Menu(self.tree, tearoff=0)

        # إضافة عناصر القائمة
        self.context_menu.add_command(
            label="✏️ تعديل مستفيد",
            command=self.edit_beneficiary
        )

        self.context_menu.add_separator()

        self.context_menu.add_command(
            label="🗑️ حذف مستفيد",
            command=self.delete_beneficiary_with_confirmation
        )

    def show_context_menu(self, event):
        """إظهار القائمة المنبثقة"""
        # تحديد العنصر المنقور عليه
        item = self.tree.identify_row(event.y)
        if item:
            self.tree.selection_set(item)
            self.tree.focus(item)

            # تحديث المستفيد الحالي
            item_data = self.tree.item(item)
            beneficiary_id = item_data['values'][0]
            self.current_beneficiary = next(
                (b for b in self.beneficiaries_data if b.id == beneficiary_id), None
            )

            # إظهار القائمة
            try:
                self.context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                self.context_menu.grab_release()
    
    def load_departments_filter(self, combo):
        """تحميل قائمة الإدارات للفلتر"""
        try:
            departments = Department.get_all()
            dept_names = ["الكل"] + [dept.name for dept in departments]
            combo['values'] = dept_names
            combo.set("الكل")
        except Exception as e:
            print(f"خطأ في تحميل الإدارات: {e}")
    
    def load_beneficiaries_data(self):
        """تحميل بيانات المستفيدين"""
        try:
            self.beneficiaries_data = Beneficiary.get_all()
            self.filtered_data = self.beneficiaries_data.copy()
            self.update_table()
            self.update_info_bar()
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل بيانات المستفيدين: {e}")
    
    def update_table(self):
        """تحديث جدول المستفيدين"""
        # التأكد من وجود الجدول
        if not hasattr(self, 'tree') or self.tree is None:
            return
            
        # مسح البيانات الحالية
        for item in self.tree.get_children():
            self.tree.delete(item)

        # إضافة البيانات المفلترة
        for beneficiary in self.filtered_data:
            # الحصول على اسم الوحدة
            unit_name = ""
            if beneficiary.unit_id:
                try:
                    unit = Unit.get_by_id(beneficiary.unit_id)
                    if unit:
                        unit_name = unit.name
                except:
                    pass

            # الحصول على اسم الإدارة
            department_name = ""
            if beneficiary.department_id:
                try:
                    dept = Department.get_by_id(beneficiary.department_id)
                    if dept:
                        department_name = dept.name
                except:
                    pass

            # الحصول على اسم القسم
            section_name = ""
            if beneficiary.section_id:
                try:
                    section = Section.get_by_id(beneficiary.section_id)
                    if section:
                        section_name = section.name
                except:
                    pass

            # الحصول على اسم مدخل البيانات
            data_entry_user_name = beneficiary.get_data_entry_user_name()

            status = "نشط" if beneficiary.is_active else "غير نشط"

            # التأكد من أن الرقم العام يُعرض كنص صحيح
            number_display = ""
            if beneficiary.number:
                # إزالة أي أرقام عشرية وعرض الرقم كعدد صحيح
                try:
                    # إذا كان الرقم يحتوي على نقطة عشرية، نأخذ الجزء الصحيح فقط
                    if '.' in str(beneficiary.number):
                        number_display = str(int(float(beneficiary.number)))
                    else:
                        number_display = str(beneficiary.number)
                except (ValueError, TypeError):
                    number_display = str(beneficiary.number)

            self.tree.insert("", "end", values=(
                beneficiary.id,
                beneficiary.name,
                number_display,
                beneficiary.rank or "",
                unit_name,
                department_name,
                section_name,
                data_entry_user_name,
                status
            ))
    
    def update_info_bar(self):
        """تحديث شريط المعلومات"""
        total = len(self.beneficiaries_data)
        filtered = len(self.filtered_data)
        active = len([b for b in self.beneficiaries_data if b.is_active])
        
        self.info_label.config(text=f"عرض {filtered} من {total} مستفيد")
        self.stats_label.config(text=f"النشطين: {active} | غير النشطين: {total - active}")
    
    def on_search_change(self, *args):
        """معالج تغيير البحث"""
        self.apply_filters()
    
    def on_filter_change(self, *args):
        """معالج تغيير الفلاتر"""
        self.apply_filters()
    
    def apply_filters(self):
        """تطبيق الفلاتر والبحث"""
        search_text = self.search_var.get().lower()
        department_filter = self.department_filter_var.get()
        status_filter = self.status_filter_var.get()
        
        self.filtered_data = []
        
        for beneficiary in self.beneficiaries_data:
            # فلتر البحث
            if search_text:
                if not any(search_text in str(getattr(beneficiary, field, "")).lower() 
                          for field in ['name', 'number', 'rank', 'phone']):
                    continue
            
            # فلتر الإدارة
            if department_filter and department_filter != "الكل":
                try:
                    if beneficiary.department_id:
                        dept = Department.get_by_id(beneficiary.department_id)
                        if not dept or dept.name != department_filter:
                            continue
                    else:
                        continue
                except:
                    continue
            
            # فلتر الحالة
            if status_filter and status_filter != "الكل":
                if status_filter == "نشط" and not beneficiary.is_active:
                    continue
                elif status_filter == "غير نشط" and beneficiary.is_active:
                    continue
            
            self.filtered_data.append(beneficiary)
        
        self.update_table()
        self.update_info_bar()
    
    def on_item_select(self, event):
        """معالج اختيار عنصر من الجدول"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            beneficiary_id = item['values'][0]
            self.current_beneficiary = next(
                (b for b in self.beneficiaries_data if b.id == beneficiary_id), None
            )
    
    def on_item_double_click(self, event):
        """معالج النقر المزدوج على عنصر"""
        self.edit_beneficiary()
    
    def add_beneficiary(self):
        """إضافة مستفيد جديد"""
        # فتح شاشة إضافة مستفيد جديد منفصلة
        add_window = AddBeneficiaryWindow(self.parent, self)

        # تحديث البيانات بعد الإضافة
        self.refresh_data()
    
    def edit_beneficiary(self):
        """تعديل المستفيد المحدد"""
        if not self.current_beneficiary:
            messagebox.showwarning("تحذير", "يرجى اختيار مستفيد للتعديل")
            return

        # استخدام نفس شاشة الإضافة ولكن في وضع التعديل
        edit_window = AddBeneficiaryWindow(self.parent, self, edit_mode=True, beneficiary_data=self.current_beneficiary)

        # تحديث البيانات بعد التعديل
        self.refresh_data()
    
    def delete_beneficiary(self):
        """حذف المستفيد المحدد (للأزرار العادية)"""
        self.delete_beneficiary_with_confirmation()

    def delete_beneficiary_with_confirmation(self):
        """حذف المستفيد مع رسالة تأكيد محسنة"""
        if not self.current_beneficiary:
            messagebox.showwarning("تحذير", "يرجى اختيار مستفيد للحذف")
            return

        # رسالة تأكيد مفصلة
        confirmation_message = f"""
⚠️ تأكيد حذف المستفيد ⚠️

الاسم: {self.current_beneficiary.name}
الرقم: {self.current_beneficiary.number or 'غير محدد'}
الرتبة: {self.current_beneficiary.rank or 'غير محدد'}

هل أنت متأكد من حذف هذا المستفيد؟

⚠️ تحذير: هذا الإجراء لا يمكن التراجع عنه!
        """

        if messagebox.askyesno(
            "🗑️ تأكيد الحذف",
            confirmation_message.strip()
        ):
            try:
                if self.current_beneficiary.delete():
                    # تحديث البيانات أولاً
                    self.refresh_data()
                    
                    # رسالة نجاح تلقائية بعد تأخير قصير
                    self.main_window.root.after(100, lambda: AutoSuccessMessage.show(
                        self.main_window.root, 
                        f"تم حذف المستفيد '{self.current_beneficiary.name}' بنجاح ✅\n\nتم إزالة المستفيد من النظام"
                    ))
                else:
                    messagebox.showerror("❌ خطأ", "فشل في حذف المستفيد")
            except Exception as e:
                messagebox.showerror("❌ خطأ", f"حدث خطأ أثناء الحذف: {e}")
    
    def refresh_data(self):
        """تحديث البيانات"""
        self.load_beneficiaries_data()
        self.current_beneficiary = None
    
    def update_beneficiary_from_dialog(self, beneficiary, data):
        """تحديث بيانات المستفيد من النافذة"""
        beneficiary.name = data['name']
        beneficiary.number = data['number']
        beneficiary.rank = data['rank']
        beneficiary.department_id = data['department_id']
        beneficiary.section_id = data['section_id']
        beneficiary.phone = data['phone']
        beneficiary.email = data['email']
        beneficiary.address = data['address']
        beneficiary.notes = data['notes']
        beneficiary.is_active = data['is_active']
        beneficiary.entry_date = data['entry_date']

    def import_excel(self):
        """استيراد بيانات المستفيدين من ملف Excel"""
        try:
            # اختيار ملف Excel
            file_path = filedialog.askopenfilename(
                title="اختر ملف Excel للاستيراد",
                filetypes=[
                    ("Excel files", "*.xlsx *.xls"),
                    ("All files", "*.*")
                ]
            )

            if not file_path:
                return

            # قراءة ملف Excel
            df = pd.read_excel(file_path)

            # التحقق من الأعمدة المطلوبة
            required_columns = ['الاسم', 'الرقم العام', 'الفئة', 'الرتبة']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                messagebox.showerror(
                    "خطأ في الملف",
                    f"الأعمدة التالية مفقودة في الملف:\n{', '.join(missing_columns)}"
                )
                return

            # التحقق من وجود أرقام مكررة في الملف نفسه
            numbers_in_file = []
            duplicate_numbers_in_file = []
            
            for index, row in df.iterrows():
                if pd.notna(row['الرقم العام']):
                    number = str(row['الرقم العام']).strip()
                    if number:
                        if number in numbers_in_file:
                            duplicate_numbers_in_file.append(f"الصف {index + 2}: الرقم '{number}'")
                        else:
                            numbers_in_file.append(number)
            
            # إذا كان هناك تكرار في الملف نفسه، أظهر تحذير
            if duplicate_numbers_in_file:
                dup_message = f"تم العثور على أرقام مكررة في الملف:\n\n"
                dup_message += "\n".join(duplicate_numbers_in_file[:10])
                if len(duplicate_numbers_in_file) > 10:
                    dup_message += f"\n... و {len(duplicate_numbers_in_file) - 10} تكرارات أخرى"
                dup_message += "\n\nهل تريد المتابعة؟ (سيتم تجاهل التكرارات)"
                
                if not messagebox.askyesno("تحذير - أرقام مكررة", dup_message):
                    return

            # عرض معاينة البيانات وخيارات التعامل مع التكرار
            preview_message = f"تم العثور على {len(df)} صف في الملف.\n\nكيف تريد التعامل مع البيانات المكررة مع قاعدة البيانات؟"
            
            # إنشاء حوار مخصص للخيارات
            choice_dialog = tk.Toplevel(self.parent)
            choice_dialog.title("خيارات الاستيراد")
            choice_dialog.geometry("450x250")
            choice_dialog.transient(self.parent)
            choice_dialog.grab_set()
            choice_dialog.resizable(False, False)
            
            # توسيط النافذة
            choice_dialog.update_idletasks()
            x = (choice_dialog.winfo_screenwidth() // 2) - (450 // 2)
            y = (choice_dialog.winfo_screenheight() // 2) - (250 // 2)
            choice_dialog.geometry(f"450x250+{x}+{y}")
            
            # متغير لحفظ الاختيار
            choice_var = tk.StringVar(value="skip")
            
            # إضافة العناصر
            ttk_bs.Label(choice_dialog, text=preview_message, wraplength=400).pack(pady=15)
            
            ttk_bs.Radiobutton(
                choice_dialog, 
                text="تجاهل البيانات المكررة (الأرقام الموجودة مسبقاً)", 
                variable=choice_var, 
                value="skip"
            ).pack(anchor=W, padx=30, pady=5)
            
            ttk_bs.Radiobutton(
                choice_dialog, 
                text="تحديث البيانات المكررة (استبدال البيانات الموجودة)", 
                variable=choice_var, 
                value="update"
            ).pack(anchor=W, padx=30, pady=5)
            
            ttk_bs.Radiobutton(
                choice_dialog, 
                text="إلغاء الاستيراد", 
                variable=choice_var, 
                value="cancel"
            ).pack(anchor=W, padx=30, pady=5)
            
            # أزرار التحكم
            buttons_frame = ttk_bs.Frame(choice_dialog)
            buttons_frame.pack(pady=20)
            
            def on_ok():
                choice_dialog.destroy()
            
            def on_cancel():
                choice_var.set("cancel")
                choice_dialog.destroy()
            
            ttk_bs.Button(buttons_frame, text="موافق", command=on_ok, width=15,
            bootstyle="success").pack(side=LEFT, padx=5)
            ttk_bs.Button(buttons_frame, text="إلغاء", command=on_cancel, width=15,
            bootstyle="secondary").pack(side=LEFT, padx=5)
            
            # انتظار إغلاق النافذة
            choice_dialog.wait_window()
            
            # التحقق من الاختيار
            duplicate_action = choice_var.get()
            if duplicate_action == "cancel":
                return

            # استيراد البيانات
            imported_count = 0
            updated_count = 0
            skipped_count = 0
            errors = []
            processed_numbers = set()  # لتجنب معالجة نفس الرقم أكثر من مرة

            for index, row in df.iterrows():
                try:
                    # استخراج البيانات الأساسية
                    name = str(row['الاسم']).strip()
                    number = str(row['الرقم العام']).strip() if pd.notna(row['الرقم العام']) else None
                    
                    # التحقق من وجود الاسم
                    if not name:
                        errors.append(f"الصف {index + 2}: الاسم مطلوب")
                        continue
                    
                    # تجاهل الأرقام المكررة في الملف نفسه
                    if number and number in processed_numbers:
                        skipped_count += 1
                        continue
                    
                    if number:
                        processed_numbers.add(number)
                    
                    # التحقق من تكرار الرقم العام في قاعدة البيانات
                    existing_beneficiary = None
                    if number:
                        existing_beneficiary = Beneficiary.get_by_number(number)
                        if existing_beneficiary:
                            if duplicate_action == "skip":
                                errors.append(f"الصف {index + 2}: الرقم العام '{number}' موجود مسبقاً للمستفيد '{existing_beneficiary.name}' - تم التجاهل")
                                skipped_count += 1
                                continue
                            elif duplicate_action == "update":
                                # سيتم تحديث البيانات الموجودة
                                pass
                    
                    # إنشاء مستفيد جديد أو استخدام الموجود للتحديث
                    if existing_beneficiary and duplicate_action == "update":
                        beneficiary = existing_beneficiary
                    else:
                        beneficiary = Beneficiary()
                    
                    beneficiary.name = name
                    beneficiary.number = number

                    # دمج الفئة والرتبة
                    category = str(row['الفئة']).strip() if pd.notna(row['الفئة']) else ""
                    rank = str(row['الرتبة']).strip() if pd.notna(row['الرتبة']) else ""

                    if category and rank:
                        beneficiary.rank = f"{category} - {rank}"
                    elif rank:
                        beneficiary.rank = rank
                    elif category:
                        beneficiary.rank = category

                    beneficiary.is_active = True
                    if not existing_beneficiary:  # فقط للمستفيدين الجدد
                        beneficiary.entry_date = date.today()

                    # البحث عن الوحدة
                    if 'الوحدة' in row and pd.notna(row['الوحدة']):
                        unit_name = str(row['الوحدة']).strip()
                        units = Unit.get_all()
                        for unit in units:
                            if unit.name == unit_name:
                                beneficiary.unit_id = unit.id
                                break

                    # البحث عن الإدارة
                    if 'الإدارة' in row and pd.notna(row['الإدارة']):
                        dept_name = str(row['الإدارة']).strip()
                        departments = Department.get_all()
                        for dept in departments:
                            if dept.name == dept_name:
                                beneficiary.department_id = dept.id
                                break

                    # البحث عن القسم
                    if 'القسم' in row and pd.notna(row['القسم']):
                        section_name = str(row['القسم']).strip()
                        sections = Section.get_all()
                        for section in sections:
                            if section.name == section_name:
                                beneficiary.section_id = section.id
                                break

                    # البحث عن مدخل البيانات
                    if 'مدخل البيانات' in row and pd.notna(row['مدخل البيانات']):
                        username = str(row['مدخل البيانات']).strip()
                        users = User.get_all()
                        for user in users:
                            if user.username == username:
                                beneficiary.data_entry_user_id = user.id
                                break

                    # حفظ المستفيد
                    if beneficiary.save():
                        if existing_beneficiary and duplicate_action == "update":
                            updated_count += 1
                        else:
                            imported_count += 1
                    else:
                        errors.append(f"الصف {index + 2}: فشل في حفظ {beneficiary.name}")

                except Exception as e:
                    errors.append(f"الصف {index + 2}: {str(e)}")

            # عرض النتائج
            result_message = f"تم استيراد {imported_count} مستفيد جديد بنجاح"
            if updated_count > 0:
                result_message += f"\nتم تحديث {updated_count} مستفيد موجود"
            if skipped_count > 0:
                result_message += f"\nتم تجاهل {skipped_count} سجل مكرر"
            
            if errors:
                result_message += f"\n\nأخطاء ({len(errors)}):\n" + "\n".join(errors[:5])
                if len(errors) > 5:
                    result_message += f"\n... و {len(errors) - 5} أخطاء أخرى"

            # رسالة نجاح تلقائية للاستيراد
            if imported_count > 0 or updated_count > 0:
                AutoSuccessMessage.show(
                    self.main_window.root, 
                    result_message,
                    duration=5000  # 5 ثوانٍ للاستيراد لأن الرسالة أطول
                )
            else:
                messagebox.showinfo("نتيجة الاستيراد", result_message)

            # تحديث البيانات
            self.refresh_data()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء الاستيراد: {e}")

    def export_excel(self):
        """تصدير بيانات المستفيدين إلى ملف Excel"""
        try:
            if not self.beneficiaries_data:
                messagebox.showwarning("تحذير", "لا توجد بيانات للتصدير")
                return

            # اختيار مكان الحفظ
            file_path = filedialog.asksaveasfilename(
                title="حفظ ملف Excel",
                defaultextension=".xlsx",
                filetypes=[
                    ("Excel files", "*.xlsx"),
                    ("All files", "*.*")
                ]
            )

            if not file_path:
                return

            # تحضير البيانات للتصدير
            export_data = []

            for beneficiary in self.beneficiaries_data:
                # الحصول على أسماء الوحدة والإدارة والقسم
                unit_name = ""
                department_name = ""
                section_name = ""
                data_entry_user = ""

                if beneficiary.unit_id:
                    try:
                        unit = Unit.get_by_id(beneficiary.unit_id)
                        if unit:
                            unit_name = unit.name
                    except:
                        pass

                if beneficiary.department_id:
                    try:
                        dept = Department.get_by_id(beneficiary.department_id)
                        if dept:
                            department_name = dept.name
                    except:
                        pass

                if beneficiary.section_id:
                    try:
                        section = Section.get_by_id(beneficiary.section_id)
                        if section:
                            section_name = section.name
                    except:
                        pass

                if beneficiary.data_entry_user_id:
                    try:
                        user = User.get_by_id(beneficiary.data_entry_user_id)
                        if user:
                            data_entry_user = user.username
                    except:
                        pass

                # استخراج الفئة والرتبة من حقل الرتبة المدمج
                category = ""
                rank = ""
                if beneficiary.rank:
                    rank_parts = beneficiary.rank.split(" - ")
                    if len(rank_parts) == 2:
                        category, rank = rank_parts
                    else:
                        rank = beneficiary.rank

                export_data.append({
                    'الاسم': beneficiary.name,
                    'الرقم العام': beneficiary.number or '',
                    'الفئة': category,
                    'الرتبة': rank,
                    'الوحدة': unit_name,
                    'الإدارة': department_name,
                    'القسم': section_name,
                    'مدخل البيانات': data_entry_user
                })

            # إنشاء DataFrame وحفظه
            df = pd.DataFrame(export_data)
            df.to_excel(file_path, index=False, engine='openpyxl')

            messagebox.showinfo(
                "نجح التصدير",
                f"تم تصدير {len(export_data)} مستفيد إلى:\n{file_path}"
            )

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء التصدير: {e}")

    def download_template(self):
        """تنزيل نموذج Excel للبيانات"""
        try:
            # اختيار مكان الحفظ
            file_path = filedialog.asksaveasfilename(
                title="حفظ نموذج Excel",
                defaultextension=".xlsx",
                filetypes=[
                    ("Excel files", "*.xlsx"),
                    ("All files", "*.*")
                ]
            )

            if not file_path:
                return

            # إنشاء نموذج البيانات
            template_data = {
                'الاسم': ['أحمد محمد علي', 'فاطمة أحمد محمد', 'خالد سعد عبدالله'],
                'الرقم العام': ['12345', '67890', '11111'],
                'الفئة': ['ضابط', 'فرد', 'أخرى'],
                'الرتبة': ['ملازم أول فني', 'وكيل رقيب', 'موظف'],
                'الوحدة': ['الوحدة الأولى', 'الوحدة الثانية', 'الوحدة الثالثة'],
                'الإدارة': ['الإدارة العامة', 'إدارة الموارد البشرية', 'إدارة تقنية المعلومات'],
                'القسم': ['قسم التخطيط', 'قسم التوظيف', 'قسم البرمجة'],
                'مدخل البيانات': ['admin', 'user1', 'user2']
            }

            # إنشاء DataFrame وحفظه
            df = pd.DataFrame(template_data)

            # إنشاء ملف Excel مع تنسيق
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='نموذج البيانات', index=False)

                # الحصول على ورقة العمل لتنسيقها
                worksheet = writer.sheets['نموذج البيانات']

                # تعديل عرض الأعمدة
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width

            messagebox.showinfo(
                "نجح التنزيل",
                f"تم إنشاء نموذج البيانات في:\n{file_path}\n\nيمكنك استخدام هذا النموذج لإدخال بيانات المستفيدين ثم استيرادها."
            )

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء النموذج: {e}")

    def delete_all_beneficiaries(self):
        """حذف جميع بيانات المستفيدين"""
        try:
            # التحقق من وجود بيانات
            if not self.beneficiaries_data:
                messagebox.showwarning("تحذير", "لا توجد بيانات للحذف")
                return
            
            # عدد المستفيدين
            total_count = len(self.beneficiaries_data)
            
            # رسالة تأكيد مخصصة محسنة
            confirm_dialog = tk.Toplevel(self.main_window.root)
            confirm_dialog.title("⚠️ تأكيد حذف جميع البيانات")
            confirm_dialog.geometry("600x400")
            confirm_dialog.resizable(False, False)
            confirm_dialog.transient(self.main_window.root)
            confirm_dialog.grab_set()
            confirm_dialog.configure(bg='#fff3cd')
            
            # توسيط النافذة بشكل مثالي
            confirm_dialog.update_idletasks()
            screen_width = confirm_dialog.winfo_screenwidth()
            screen_height = confirm_dialog.winfo_screenheight()
            x = (screen_width - 600) // 2
            y = (screen_height - 400) // 2
            confirm_dialog.geometry(f"600x400+{x}+{y}")
            
            # جعل النافذة في المقدمة
            confirm_dialog.lift()
            confirm_dialog.attributes('-topmost', True)
            
            # متغير للنتيجة
            result = [False]
            
            # إطار المحتوى مع خلفية ملونة
            main_frame = tk.Frame(confirm_dialog, bg='#fff3cd')
            main_frame.pack(fill=BOTH, expand=True, padx=25, pady=25)
            
            # أيقونة التحذير أكبر وأوضح
            warning_label = tk.Label(
                main_frame,
                text="⚠️",
                font=("Arial", 56, "bold"),
                fg="#856404",
                bg='#fff3cd'
            )
            warning_label.pack(pady=(15, 25))
            
            # عنوان التحذير
            title_label = tk.Label(
                main_frame,
                text="تحذير خطير: حذف جميع البيانات",
                font=("Arial", 18, "bold"),
                fg="#721c24",
                bg='#fff3cd'
            )
            title_label.pack(pady=(0, 20))
            
            # نص التحذير المحسن
            warning_text = f"""سيتم حذف جميع بيانات المستفيدين ({total_count} مستفيد)

🚨 تحذيرات مهمة:
• هذا الإجراء لا يمكن التراجع عنه نهائياً
• ستفقد جميع البيانات والمعلومات المرتبطة
• يُنصح بشدة بعمل نسخة احتياطية أولاً
• تأكد من أنك تريد فعلاً حذف جميع البيانات

هل أنت متأكد 100% من المتابعة؟"""
            
            message_label = tk.Label(
                main_frame,
                text=warning_text,
                font=("Arial", 12),
                justify=CENTER,
                wraplength=520,
                fg="#856404",
                bg='#fff3cd'
            )
            message_label.pack(pady=(0, 25))
            
            # أزرار التحكم محسنة
            buttons_frame = tk.Frame(main_frame, bg='#fff3cd')
            buttons_frame.pack(pady=25)
            
            def on_confirm():
                result[0] = True
                confirm_dialog.destroy()
            
            def on_cancel():
                result[0] = False
                confirm_dialog.destroy()
            
            # زر الإلغاء (أكبر وأوضح)
            cancel_btn = tk.Button(
                buttons_frame,
                text="❌ إلغاء - لا تحذف",
                command=on_cancel,
                font=("Arial", 12, "bold"),
                bg="#6c757d",
                fg="white",
                width=18,
                height=2,
                relief="raised",
                bd=3
            )
            cancel_btn.pack(side=LEFT, padx=15)
            
            # زر التأكيد (خطير)
            confirm_btn = tk.Button(
                buttons_frame,
                text="🗑️💥 نعم، احذف الكل",
                command=on_confirm,
                font=("Arial", 12, "bold"),
                bg="#dc3545",
                fg="white",
                width=18,
                height=2,
                relief="raised",
                bd=3
            )
            confirm_btn.pack(side=LEFT, padx=15)
            
            # تركيز افتراضي على زر الإلغاء (للأمان)
            cancel_btn.focus_set()
            
            # انتظار إغلاق النافذة
            confirm_dialog.wait_window()
            
            # إذا لم يوافق المستخدم، اخرج
            if not result[0]:
                return
            
            # تنفيذ الحذف
            from database import db_manager
            
            # حذف جميع المستفيدين (تعطيل بدلاً من الحذف الفعلي للأمان)
            delete_query = "UPDATE beneficiaries SET is_active = 0, updated_at = CURRENT_TIMESTAMP"
            db_manager.execute_query(delete_query)
            
            # تحديث البيانات أولاً
            self.refresh_data()
            
            # رسالة نجاح تلقائية بعد تأخير قصير لضمان ظهورها في الوسط
            self.main_window.root.after(100, lambda: AutoSuccessMessage.show(
                self.main_window.root, 
                f"تم حذف جميع بيانات المستفيدين ({total_count} مستفيد) بنجاح ✅\n\nتم تعطيل جميع المستفيدين من النظام",
                duration=4000  # 4 ثوانٍ لأن الرسالة مهمة
            ))
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حذف البيانات: {e}")


class BeneficiaryDialog:
    """نافذة حوار إضافة/تعديل المستفيد"""
    
    def __init__(self, parent, title, beneficiary=None):
        self.result = None
        self.beneficiary = beneficiary
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("600x700")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.center_window()

        # تسجيل النافذة في مراقبة النشاط (معطل)
        # try:
        #     from activity_monitor import register_window_for_activity_monitoring
        #     register_window_for_activity_monitoring(self.dialog)
        # except Exception as e:
        #     print(f"تحذير: فشل في تسجيل النافذة في مراقبة النشاط: {e}")

        # إعداد المحتوى
        self.setup_dialog()

        # انتظار إغلاق النافذة
        self.dialog.wait_window()
    
    def center_window(self):
        """توسيط النافذة"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() - 600) // 2
        y = (self.dialog.winfo_screenheight() - 700) // 2
        self.dialog.geometry(f"600x700+{x}+{y}")
    
    def setup_dialog(self):
        """إعداد محتوى النافذة"""
        main_frame = ttk_bs.Frame(self.dialog)
        main_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)
        
        # العنوان
        title_label = ttk_bs.Label(
            main_frame,
            text="👤 بيانات المستفيد",
            bootstyle="primary"
        )
        title_label.pack(pady=10)
        
        # إنشاء النموذج
        self.create_form(main_frame)
        
        # أزرار العمليات
        self.create_buttons(main_frame)
        
        # تعبئة البيانات إذا كان في وضع التعديل
        if self.beneficiary:
            self.populate_form()
    
    def create_form(self, parent):
        """إنشاء نموذج البيانات"""
        form_frame = ttk_bs.Frame(parent)
        form_frame.pack(fill=BOTH, expand=True, pady=20)
        
        # متغيرات النموذج
        self.name_var = tk.StringVar()
        self.number_var = tk.StringVar()
        self.rank_var = tk.StringVar()
        self.department_var = tk.StringVar()
        self.section_var = tk.StringVar()
        self.phone_var = tk.StringVar()
        self.email_var = tk.StringVar()
        self.address_var = tk.StringVar()
        self.notes_var = tk.StringVar()
        self.is_active_var = tk.BooleanVar(value=True)
        self.entry_date_var = tk.StringVar(value=datetime.now().strftime("%Y-%m-%d"))
        
        # الحقول
        fields = [
            ("الاسم الكامل *", self.name_var, "entry"),
            ("رقم الموظف", self.number_var, "entry"),
            ("الرتبة/المسمى الوظيفي", self.rank_var, "entry"),
            ("الإدارة", self.department_var, "combobox"),
            ("القسم", self.section_var, "combobox"),
            ("رقم الهاتف", self.phone_var, "entry"),
            ("البريد الإلكتروني", self.email_var, "entry"),
            ("العنوان", self.address_var, "entry"),
            ("تاريخ الدخول", self.entry_date_var, "date"),
            ("ملاحظات", self.notes_var, "text"),
        ]
        
        for i, (label, var, field_type) in enumerate(fields):
            field_frame = ttk_bs.Frame(form_frame)
            field_frame.pack(fill=X, pady=5)
            
            ttk_bs.Label(field_frame, text=label, width=20).pack(side=LEFT, anchor=E, padx=5)
            
            if field_type == "entry":
                widget = ttk_bs.Entry(field_frame, textvariable=var, width=40)
            elif field_type == "combobox":
                widget = ttk_bs.Combobox(field_frame, textvariable=var, width=37, state="readonly")
                if "department" in label.lower():
                    self.load_departments(widget)
                elif "section" in label.lower():
                    self.load_sections(widget)
            elif field_type == "date":
                widget = ttk_bs.Entry(field_frame, textvariable=var, width=40)
            elif field_type == "text":
                widget = tk.Text(field_frame, height=3, width=40)
                # ربط النص بالمتغير
                widget.bind('<KeyRelease>', lambda e: var.set(widget.get("1.0", "end-1c")))
            
            widget.pack(side=LEFT, padx=5)
            
            if field_type == "text":
                setattr(self, f"notes_text", widget)
        
        # خانة اختيار الحالة
        status_frame = ttk_bs.Frame(form_frame)
        status_frame.pack(fill=X, pady=10)
        
        ttk_bs.Checkbutton(
            status_frame,
            text="المستفيد نشط",
            variable=self.is_active_var,
            bootstyle="success"
        ).pack(side=LEFT)
    
    def create_buttons(self, parent):
        """إنشاء أزرار العمليات"""
        buttons_frame = ttk_bs.Frame(parent)
        buttons_frame.pack(pady=20)
        
        ttk_bs.Button(
            buttons_frame,
            text="💾 حفظ",
            command=self.save_data,
            bootstyle="success",
            width=15
        ).pack(side=LEFT, padx=10)
        
        ttk_bs.Button(
            buttons_frame,
            text="❌ إلغاء",
            command=self.cancel,
            bootstyle="secondary",
            width=15
        ).pack(side=LEFT, padx=10)
    
    def load_departments(self, combo):
        """تحميل قائمة الإدارات"""
        try:
            departments = Department.get_all()
            dept_names = [dept.name for dept in departments]
            combo['values'] = dept_names
        except Exception as e:
            print(f"خطأ في تحميل الإدارات: {e}")
    
    def load_sections(self, combo):
        """تحميل قائمة الأقسام"""
        try:
            sections = Section.get_all()
            section_names = [section.name for section in sections]
            combo['values'] = section_names
        except Exception as e:
            print(f"خطأ في تحميل الأقسام: {e}")
    
    def populate_form(self):
        """تعبئة النموذج ببيانات المستفيد"""
        if not self.beneficiary:
            return
        
        self.name_var.set(self.beneficiary.name or "")
        self.number_var.set(self.beneficiary.number or "")
        self.rank_var.set(self.beneficiary.rank or "")
        self.phone_var.set(self.beneficiary.phone or "")
        self.email_var.set(self.beneficiary.email or "")
        self.address_var.set(self.beneficiary.address or "")
        self.is_active_var.set(self.beneficiary.is_active)
        
        if self.beneficiary.entry_date:
            self.entry_date_var.set(str(self.beneficiary.entry_date))
        
        if self.beneficiary.notes:
            self.notes_text.insert("1.0", self.beneficiary.notes)
        
        # تحميل الإدارة والقسم
        if self.beneficiary.department_id:
            try:
                dept = Department.get_by_id(self.beneficiary.department_id)
                if dept:
                    self.department_var.set(dept.name)
            except:
                pass
        
        if self.beneficiary.section_id:
            try:
                section = Section.get_by_id(self.beneficiary.section_id)
                if section:
                    self.section_var.set(section.name)
            except:
                pass
    
    def save_data(self):
        """حفظ البيانات"""
        # التحقق من صحة البيانات
        if not self.name_var.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستفيد")
            return
        
        # جمع البيانات
        try:
            # البحث عن معرف الإدارة
            department_id = None
            if self.department_var.get():
                departments = Department.get_all()
                for dept in departments:
                    if dept.name == self.department_var.get():
                        department_id = dept.id
                        break
            
            # البحث عن معرف القسم
            section_id = None
            if self.section_var.get():
                sections = Section.get_all()
                for section in sections:
                    if section.name == self.section_var.get():
                        section_id = section.id
                        break
            
            # تحويل تاريخ الدخول
            entry_date = None
            if self.entry_date_var.get():
                try:
                    entry_date = datetime.strptime(self.entry_date_var.get(), "%Y-%m-%d").date()
                except:
                    pass
            
            self.result = {
                'name': self.name_var.get().strip(),
                'number': self.number_var.get().strip() or None,
                'rank': self.rank_var.get().strip() or None,
                'department_id': department_id,
                'section_id': section_id,
                'phone': self.phone_var.get().strip() or None,
                'email': self.email_var.get().strip() or None,
                'address': self.address_var.get().strip() or None,
                'notes': self.notes_text.get("1.0", "end-1c").strip() or None,
                'is_active': self.is_active_var.get(),
                'entry_date': entry_date
            }
            
            self.dialog.destroy()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في معالجة البيانات: {e}")
    
    def cancel(self):
        """إلغاء العملية"""
        self.dialog.destroy()


class AddBeneficiaryWindow:
    """شاشة إضافة/تعديل مستفيد"""

    def __init__(self, parent, beneficiaries_window, edit_mode=False, beneficiary_data=None):
        self.parent = parent
        self.beneficiaries_window = beneficiaries_window
        self.window = None
        self.edit_mode = edit_mode
        self.beneficiary_data = beneficiary_data

        # متغيرات النموذج - التأكد من أنها فارغة في البداية
        self.name_var = tk.StringVar(value="")
        self.number_var = tk.StringVar(value="")
        self.category_var = tk.StringVar(value="-- اختر الفئة --")
        self.rank_var = tk.StringVar(value="-- اختر الرتبة --")
        self.unit_var = tk.StringVar(value="-- اختر الوحدة --")
        self.department_var = tk.StringVar(value="-- اختر الإدارة --")
        self.section_var = tk.StringVar(value="-- اختر القسم --")
        self.data_entry_var = tk.StringVar(value="")

        # قوائم البيانات
        self.units = []
        self.departments = []
        self.sections = []

        # قوائم الرتب حسب الفئة
        self.officer_ranks = [
            # ملازم
            "ملازم فني", "ملازم مهندس",
            # ملازم أول
            "ملازم أول فني", "ملازم أول مهندس",
            # نقيب
            "نقيب فني", "نقيب مهندس",
            # رائد
            "رائد فني", "رائد مهندس", "رائد ركن",
            # مقدم
            "مقدم فني", "مقدم مهندس", "مقدم ركن",
            # عقيد
            "عقيد فني", "عقيد مهندس", "عقيد ركن",
            # عميد
            "عميد فني", "عميد مهندس", "عميد ركن",
            # لواء
            "لواء فني", "لواء مهندس", "لواء ركن"
        ]

        self.soldier_ranks = [
            "جندي", "جندي أول", "عريف", "وكيل رقيب", "رقيب", "رقيب أول",
            "رئيس رقباء"
        ]

        self.other_ranks = [
            "موظف", "متعاقد"
        ]

        self.setup_window()
        self.load_data()

    def setup_window(self):
        """إعداد النافذة مع حجم مناسب"""
        self.window = tk.Toplevel(self.parent)

        # تعيين العنوان حسب الوضع
        if self.edit_mode:
            self.window.title("✏️ تعديل بيانات المستفيد")
        else:
            self.window.title("✨ إضافة مستفيد جديد")

        self.window.geometry("1100x500")
        self.window.resizable(False, False)

        # تعيين الحد الأدنى لحجم النافذة
        self.window.minsize(1100, 500)

        # تعيين أيقونة النافذة (إذا كانت متوفرة)
        try:
            self.window.iconbitmap("assets/icon.ico")
        except:
            pass

        # توسيط النافذة
        self.center_window()

        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()
        self.window.lift()
        self.window.focus_force()

        # تسجيل النافذة في مراقبة النشاط (معطل)
        # try:
        #     from activity_monitor import register_window_for_activity_monitoring
        #     register_window_for_activity_monitoring(self.window)
        # except Exception as e:
        #     print(f"تحذير: فشل في تسجيل النافذة في مراقبة النشاط: {e}")

        # إعداد المحتوى
        self.setup_content()

        # تفعيل مفاتيح الاختصار
        self.setup_add_beneficiary_shortcuts()

    def center_window(self):
        """توسيط النافذة على الشاشة مع حجم مناسب"""
        self.window.update_idletasks()

        # الحصول على أبعاد الشاشة
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()

        # أبعاد النافذة المناسبة
        window_width = 1100
        window_height = 500

        # حساب موضع النافذة للتوسيط
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        # تعيين موضع النافذة
        self.window.geometry(f"{window_width}x{window_height}+{x}+{y}")

    def setup_content(self):
        """إعداد محتوى النافذة بدون شريط تمرير"""
        # إنشاء إطار رئيسي مباشر بدون شريط تمرير
        main_frame = ttk_bs.Frame(self.window)
        main_frame.pack(fill="both", expand=True, padx=10, pady=5)

        # العنوان والأزرار العلوية
        self.create_header(main_frame)

        # النموذج الرئيسي
        self.create_form(main_frame)

        # أزرار العمليات (مهم: في النهاية)
        self.create_action_buttons(main_frame)

        # تحديث النافذة للتأكد من ظهور جميع العناصر
        self.window.update_idletasks()

        print("✅ تم إعداد محتوى النافذة بدون شريط تمرير")

    def create_header(self, parent):
        """إنشاء العنوان والأزرار العلوية المضغوطة"""
        # إطار العنوان الرئيسي مع خلفية جميلة
        header_container = ttk_bs.Frame(parent)
        header_container.pack(fill=X, pady=(0, 8))

        # إطار العنوان مع تدرج لوني
        header_frame = ttk_bs.LabelFrame(
            header_container,
            text="",
            bootstyle="primary",
            padding=8
        )
        header_frame.pack(fill=X)

        # زر العودة للقائمة مع أيقونة
        back_btn = ttk_bs.Button(
            header_frame,
            text="🔙 العودة",
            command=self.close_window,
            bootstyle="outline-secondary",
            width=18
        )
        back_btn.pack(side=LEFT, pady=2)

        # العنوان الرئيسي مع أيقونة جميلة
        title_frame = ttk_bs.Frame(header_frame)
        title_frame.pack(side=RIGHT, fill=X, expand=True)

        # تحديد النص حسب الوضع
        if self.edit_mode:
            title_text = "✏️ تعديل بيانات المستفيد"
            subtitle_text = "قم بتعديل البيانات أدناه وحفظ التغييرات"
        else:
            title_text = "✨ إضافة مستفيد جديد"
            subtitle_text = "املأ البيانات أدناه لإضافة مستفيد جديد"

        title_label = ttk_bs.Label(
            title_frame,
            text=title_text,
            bootstyle="primary",
            anchor="center"
        )
        title_label.pack(pady=2)

        # عنوان فرعي
        subtitle_label = ttk_bs.Label(
            title_frame,
            text=subtitle_text,
            bootstyle="secondary",
            anchor="center"
        )
        subtitle_label.pack()

    def create_form(self, parent):
        """إنشاء النموذج الرئيسي"""
        # إطار النموذج مع خلفية
        form_container = ttk_bs.LabelFrame(
            parent,
            text="",
            bootstyle="light",
            padding=20
        )
        form_container.pack(fill=X, pady=(0, 20))

        # إنشاء شبكة للحقول
        self.create_form_grid(form_container)

    def create_form_grid(self, parent):
        """إنشاء نموذج بتخطيط 4 حقول يمين و 4 حقول يسار"""

        # إطار النموذج الرئيسي
        form_frame = ttk_bs.Frame(parent)
        form_frame.pack(fill=X, pady=20, padx=40)

        # الصف الأول: الاسم (يمين) والرقم العام (يسار)
        row1_frame = ttk_bs.Frame(form_frame)
        row1_frame.pack(fill=X, pady=10)

        # الاسم (الجانب الأيمن)
        name_label = ttk_bs.Label(row1_frame, text="الاسم *", width=15, anchor=E)
        name_label.pack(side=RIGHT, padx=(0, 5))

        self.name_entry = ttk_bs.Entry(
            row1_frame,
            textvariable=self.name_var,
            width=50,
            state="normal"
        )
        self.name_entry.pack(side=RIGHT, padx=(0, 50))

        # ربط أحداث التركيز والكتابة والنقر
        self.name_entry.bind("<FocusIn>", self.on_name_focus_in)
        self.name_entry.bind("<KeyPress>", self.on_name_key_press)
        self.name_entry.bind("<Button-1>", self.on_name_click)

        # الرقم العام (الجانب الأيسر)
        self.number_entry = ttk_bs.Entry(
            row1_frame,
            textvariable=self.number_var,
            width=50,
            state="normal"
        )
        self.number_entry.pack(side=LEFT, padx=(50, 5))

        # ربط أحداث التركيز والكتابة والنقر
        self.number_entry.bind("<FocusIn>", self.on_number_focus_in)
        self.number_entry.bind("<KeyPress>", self.on_number_key_press)
        self.number_entry.bind("<Button-1>", self.on_number_click)

        number_label = ttk_bs.Label(row1_frame, text="الرقم العام *", width=15, anchor=E)
        number_label.pack(side=LEFT, padx=(0, 50))

        # الصف الثاني: الفئة (يمين) والرتبة (يسار)
        row2_frame = ttk_bs.Frame(form_frame)
        row2_frame.pack(fill=X, pady=10)

        # الفئة (الجانب الأيمن)
        category_label = ttk_bs.Label(row2_frame, text="الفئة", width=15, anchor=E)
        category_label.pack(side=RIGHT, padx=(0, 5))

        self.category_combo = ttk_bs.Combobox(
            row2_frame,
            textvariable=self.category_var,
            values=["-- اختر الفئة --", "ضابط", "فرد", "أخرى"],
            state="readonly",
            width=50
        )
        self.category_combo.set("-- اختر الفئة --")
        self.category_combo.pack(side=RIGHT, padx=(0, 50))
        self.category_combo.bind("<<ComboboxSelected>>", self.on_category_change)

        # الرتبة (الجانب الأيسر)
        self.rank_combo = ttk_bs.Combobox(
            row2_frame,
            textvariable=self.rank_var,
            state="readonly",
            width=50
        )
        self.rank_combo.set("-- اختر الرتبة --")
        self.rank_combo.pack(side=LEFT, padx=(50, 5))

        rank_label = ttk_bs.Label(row2_frame, text="الرتبة", width=15, anchor=E)
        rank_label.pack(side=LEFT, padx=(0, 50))

        # الصف الثالث: الوحدة (يمين) والإدارة (يسار)
        row3_frame = ttk_bs.Frame(form_frame)
        row3_frame.pack(fill=X, pady=10)

        # الوحدة (الجانب الأيمن)
        unit_label = ttk_bs.Label(row3_frame, text="الوحدة *", width=15, anchor=E)
        unit_label.pack(side=RIGHT, padx=(0, 5))

        self.unit_combo = ttk_bs.Combobox(
            row3_frame,
            textvariable=self.unit_var,
            state="readonly",
            width=50
        )
        self.unit_combo.pack(side=RIGHT, padx=(0, 50))
        self.unit_combo.bind("<<ComboboxSelected>>", self.on_unit_change)

        # الإدارة (الجانب الأيسر)
        self.dept_combo = ttk_bs.Combobox(
            row3_frame,
            textvariable=self.department_var,
            state="readonly",
            width=50
        )
        self.dept_combo.pack(side=LEFT, padx=(50, 5))
        self.dept_combo.bind("<<ComboboxSelected>>", self.on_department_change)

        dept_label = ttk_bs.Label(row3_frame, text="الإدارة *", width=15, anchor=E)
        dept_label.pack(side=LEFT, padx=(0, 50))

        # الصف الرابع: القسم (يمين) ومدخل البيانات (يسار)
        row4_frame = ttk_bs.Frame(form_frame)
        row4_frame.pack(fill=X, pady=10)

        # القسم (الجانب الأيمن)
        section_label = ttk_bs.Label(row4_frame, text="القسم", width=15, anchor=E)
        section_label.pack(side=RIGHT, padx=(0, 5))

        self.section_combo = ttk_bs.Combobox(
            row4_frame,
            textvariable=self.section_var,
            state="readonly",
            width=50
        )
        self.section_combo.pack(side=RIGHT, padx=(0, 50))

        # مدخل البيانات (الجانب الأيسر)
        data_entry_entry = ttk_bs.Entry(
            row4_frame,
            textvariable=self.data_entry_var,
            state="readonly",
            width=50
        )
        data_entry_entry.pack(side=LEFT, padx=(50, 5))

        data_entry_label = ttk_bs.Label(row4_frame, text="مدخل البيانات", width=15, anchor=E)
        data_entry_label.pack(side=LEFT, padx=(0, 50))

        # تفعيل الحقول فوراً وبقوة
        self.name_entry.configure(state="normal")
        self.number_entry.configure(state="normal")

        # تعيين اسم المستخدم الحالي
        self.set_current_user()

        # تفعيل إضافي للحقول
        self.name_entry.configure(state="normal")
        self.number_entry.configure(state="normal")

        # إنشاء رسالة النجاح (مخفية في البداية)
        self.create_success_message(parent)

        # تأكيد أن الحقول جاهزة للاستخدام
        self.window.after(50, self.ensure_fields_ready)

        # تفعيل نهائي للحقول
        self.window.after(100, self.final_enable_fields)

        # تعبئة البيانات في وضع التعديل
        if self.edit_mode and self.beneficiary_data:
            self.window.after(200, self.populate_form_data)

    def create_action_buttons(self, parent):
        """إنشاء أزرار العمليات المضغوطة جداً"""
        # إطار الأزرار مع خلفية جميلة
        buttons_container = ttk_bs.LabelFrame(
            parent,
            text="⚡ العمليات",
            bootstyle="light",
            padding=6
        )
        buttons_container.pack(fill=X, pady=(8, 3))

        # إطار داخلي للأزرار مع تخطيط أفضل
        buttons_frame = ttk_bs.Frame(buttons_container)
        buttons_frame.pack(anchor=CENTER)

        # زر الحفظ الرئيسي مع تصميم بارز
        save_text = "💾 حفظ التعديلات" if self.edit_mode else "💾 حفظ البيانات"
        self.save_btn = ttk_bs.Button(
            buttons_frame,
            text=save_text,
            command=self.save_beneficiary,
            bootstyle="success",
            width=18
        )
        self.save_btn.pack(side=LEFT, padx=5, pady=3, ipady=3)

        # زر مسح البيانات
        clear_btn = ttk_bs.Button(
            buttons_frame,
            text="🔄 مسح",
            command=self.clear_form_with_confirmation,
            bootstyle="warning-outline",
            width=15
        )
        clear_btn.pack(side=LEFT, padx=3, pady=3, ipady=3)

        # زر الإلغاء
        self.cancel_btn = ttk_bs.Button(
            buttons_frame,
            text="❌ إغلاق",
            command=self.close_window,
            bootstyle="danger-outline",
            width=18
        )
        self.cancel_btn.pack(side=LEFT, padx=5, pady=3, ipady=3)

        # ربط النقر في أي مكان لإخفاء رسالة النجاح
        self.bind_click_events()

        print("✅ تم إنشاء الأزرار بنجاح")

    def bind_click_events(self):
        """ربط أحداث النقر لإخفاء رسالة النجاح"""
        try:
            # ربط النقر على النافذة الرئيسية
            self.window.bind("<Button-1>", self.hide_success_message)

            # ربط النقر على جميع العناصر الفرعية
            def bind_recursive(widget):
                widget.bind("<Button-1>", self.hide_success_message)
                for child in widget.winfo_children():
                    bind_recursive(child)

            bind_recursive(self.window)

        except Exception as e:
            print(f"خطأ في ربط أحداث النقر: {e}")

    def create_success_message(self, parent):
        """إنشاء رسالة النجاح الجميلة والمتطورة"""
        # إطار رسالة النجاح مع تأثيرات بصرية متقدمة
        self.success_frame = ttk_bs.Frame(parent)

        # إطار خارجي مع ظل وتأثيرات
        success_outer_frame = ttk_bs.Frame(
            self.success_frame,
            bootstyle="success",
            padding=5
        )
        success_outer_frame.pack(fill=X, padx=20, pady=10)

        # إطار داخلي للمحتوى مع تصميم أنيق
        success_inner_frame = ttk_bs.LabelFrame(
            success_outer_frame,
            text="",
            bootstyle="success",
            padding=20
        )
        success_inner_frame.pack(fill=X)

        # صف الأيقونة والرسالة الرئيسية
        main_row = ttk_bs.Frame(success_inner_frame, bootstyle="success")
        main_row.pack(fill=X, pady=(0, 10))

        # أيقونة النجاح الكبيرة والجميلة
        success_icon = ttk_bs.Label(
            main_row,
            text="✅",
            bootstyle="success"
        )
        success_icon.pack(side=LEFT, padx=(0, 15))

        # إطار النصوص
        text_frame = ttk_bs.Frame(main_row, bootstyle="success")
        text_frame.pack(side=LEFT, fill=X, expand=True)

        # نص الرسالة الرئيسية
        self.success_label = ttk_bs.Label(
            text_frame,
            text="تم حفظ البيانات بنجاح! 🎉",
            bootstyle="success"
        )
        self.success_label.pack(anchor=tk.W)

        # رسالة فرعية مفيدة
        sub_message = ttk_bs.Label(
            text_frame,
            text="يمكنك الآن إضافة مستفيد آخر أو إغلاق النافذة",
            bootstyle="success"
        )
        sub_message.pack(anchor=tk.W, pady=(5, 0))

        # زر إغلاق الرسالة الأنيق
        close_msg_btn = ttk_bs.Button(
            main_row,
            text="✕",
            command=self.hide_success_message,
            bootstyle="success-outline",
            width=15
        )
        close_msg_btn.pack(side=RIGHT, padx=(15, 0))

        # شريط تقدم صغير لإظهار الوقت المتبقي
        self.progress_bar = ttk_bs.Progressbar(
            success_inner_frame,
            mode='determinate',
            bootstyle="success-striped",
            length=300
        )
        self.progress_bar.pack(pady=(10, 0))

        # إخفاء الرسالة في البداية
        self.success_frame.pack_forget()

    def set_current_user(self):
        """تعيين اسم المستخدم الحالي"""
        try:
            # محاولة الحصول على المستخدم الحالي من النافذة الرئيسية
            if hasattr(self.beneficiaries_window, 'main_window') and self.beneficiaries_window.main_window:
                if hasattr(self.beneficiaries_window.main_window, 'current_user'):
                    current_user = self.beneficiaries_window.main_window.current_user
                    if current_user and hasattr(current_user, 'full_name'):
                        self.data_entry_var.set(current_user.full_name)
                        return

            # إذا لم نجد المستخدم، استخدم قيمة افتراضية
            self.data_entry_var.set("مدير النظام")

        except Exception as e:
            print(f"خطأ في تعيين المستخدم الحالي: {e}")
            self.data_entry_var.set("مدير النظام")

        # التأكد من أن حقول الاسم والرقم العام فارغة عند البداية (فقط في وضع الإضافة)
        if not self.edit_mode:
            self.name_var.set("")
            self.number_var.set("")

        # التركيز على حقل الاسم بعد تحميل النافذة
        self.window.after(50, self.focus_name_field)

    def focus_name_field(self):
        """التركيز على حقل الاسم وتمكين الكتابة فيه"""
        try:
            if hasattr(self, 'name_entry'):
                # التأكد من أن الحقل في حالة normal
                self.name_entry.configure(state="normal")
                # التركيز على الحقل
                self.name_entry.focus_set()
                # وضع المؤشر في بداية الحقل
                self.name_entry.icursor(0)
                print("✅ تم التركيز على حقل الاسم وتفعيله للكتابة")
        except Exception as e:
            print(f"خطأ في التركيز على حقل الاسم: {e}")

    def on_name_click(self, event):
        """معالج النقر على حقل الاسم"""
        try:
            # التأكد من أن الحقل قابل للكتابة
            if hasattr(self, 'name_entry'):
                self.name_entry.configure(state="normal")
                self.name_entry.focus_set()
                # تأكيد إضافي للتفعيل
                self.window.after(10, lambda: self.name_entry.configure(state="normal"))
                print("✅ تم تفعيل حقل الاسم عند النقر")
        except Exception as e:
            print(f"خطأ في تفعيل حقل الاسم عند النقر: {e}")

    def on_name_focus_in(self, event):
        """معالج التركيز على حقل الاسم"""
        try:
            # التأكد من أن الحقل قابل للكتابة
            if hasattr(self, 'name_entry'):
                self.name_entry.configure(state="normal")
                print("✅ تم تفعيل حقل الاسم للكتابة")
        except Exception as e:
            print(f"خطأ في تفعيل حقل الاسم: {e}")

    def on_name_key_press(self, event):
        """معالج الضغط على مفاتيح في حقل الاسم"""
        try:
            # التأكد من أن الحقل يقبل الإدخال
            print(f"تم الضغط على مفتاح في حقل الاسم: {event.keysym}")
            return True  # السماح بالإدخال
        except Exception as e:
            print(f"خطأ في معالجة الضغط على مفتاح في حقل الاسم: {e}")
            return True

    def on_number_click(self, event):
        """معالج النقر على حقل الرقم العام"""
        try:
            # التأكد من أن الحقل قابل للكتابة
            if hasattr(self, 'number_entry'):
                self.number_entry.configure(state="normal")
                self.number_entry.focus_set()
                # تأكيد إضافي للتفعيل
                self.window.after(10, lambda: self.number_entry.configure(state="normal"))
                print("✅ تم تفعيل حقل الرقم العام عند النقر")
        except Exception as e:
            print(f"خطأ في تفعيل حقل الرقم العام عند النقر: {e}")

    def on_number_focus_in(self, event):
        """معالج التركيز على حقل الرقم العام"""
        try:
            # التأكد من أن الحقل قابل للكتابة
            if hasattr(self, 'number_entry'):
                self.number_entry.configure(state="normal")
                print("✅ تم تفعيل حقل الرقم العام للكتابة")
        except Exception as e:
            print(f"خطأ في تفعيل حقل الرقم العام: {e}")

    def on_number_key_press(self, event):
        """معالج الضغط على مفاتيح في حقل الرقم العام"""
        try:
            # التأكد من أن الحقل يقبل الإدخال
            print(f"تم الضغط على مفتاح في حقل الرقم العام: {event.keysym}")
            return True  # السماح بالإدخال
        except Exception as e:
            print(f"خطأ في معالجة الضغط على مفتاح في حقل الرقم العام: {e}")
            return True

    def ensure_fields_ready(self):
        """التأكد من أن جميع الحقول جاهزة للاستخدام"""
        try:
            # التأكد من أن حقول الإدخال جاهزة للكتابة
            if hasattr(self, 'name_entry'):
                self.name_entry.configure(state="normal")
                # لا نمسح المحتوى إلا في وضع الإضافة الجديدة
                if not self.edit_mode:
                    self.name_var.set("")

            if hasattr(self, 'number_entry'):
                self.number_entry.configure(state="normal")
                # لا نمسح المحتوى إلا في وضع الإضافة الجديدة
                if not self.edit_mode:
                    self.number_var.set("")

            # التركيز على حقل الاسم
            self.focus_name_field()

            print("✅ تم التأكد من جاهزية جميع الحقول")

        except Exception as e:
            print(f"خطأ في التأكد من جاهزية الحقول: {e}")

    def final_enable_fields(self):
        """التفعيل النهائي للحقول"""
        try:
            # تفعيل قوي للحقول
            if hasattr(self, 'name_entry'):
                self.name_entry.configure(state="normal")
                self.name_entry.focus_set()
                print("✅ تم التفعيل النهائي لحقل الاسم")

            if hasattr(self, 'number_entry'):
                self.number_entry.configure(state="normal")
                print("✅ تم التفعيل النهائي لحقل الرقم العام")

        except Exception as e:
            print(f"خطأ في التفعيل النهائي للحقول: {e}")

    def populate_form_data(self):
        """تعبئة النموذج ببيانات المستفيد في وضع التعديل"""
        try:
            if not self.edit_mode or not self.beneficiary_data:
                return

            beneficiary = self.beneficiary_data

            # تعبئة البيانات الأساسية
            self.name_var.set(beneficiary.name or "")
            self.number_var.set(beneficiary.number or "")

            # تعبئة الفئة والرتبة من حقل الرتبة المدمج
            if beneficiary.rank:
                rank_parts = beneficiary.rank.split(" - ")
                if len(rank_parts) == 2:
                    category, rank = rank_parts
                    self.category_var.set(category)
                    # تحديث قائمة الرتب أولاً
                    self.on_category_change()
                    self.rank_var.set(rank)
                else:
                    # إذا لم يكن مقسم، ضعه في الرتبة مباشرة
                    self.rank_var.set(beneficiary.rank)

            # تعبئة الإدارات والأقسام
            if beneficiary.department_id:
                for dept in self.departments:
                    if dept.id == beneficiary.department_id:
                        self.department_var.set(dept.name)
                        self.on_department_change()  # تحديث الأقسام
                        break

            if beneficiary.section_id:
                for section in self.sections:
                    if section.id == beneficiary.section_id:
                        self.section_var.set(section.name)
                        break

            if beneficiary.unit_id:
                for unit in self.units:
                    if unit.id == beneficiary.unit_id:
                        self.unit_var.set(unit.name)
                        break

            print("✅ تم تعبئة البيانات في وضع التعديل")

        except Exception as e:
            print(f"خطأ في تعبئة البيانات: {e}")

    def check_duplicate_number(self, number):
        """التحقق من تكرار الرقم العام"""
        try:
            from database import db_manager

            # البحث عن مستفيد بنفس الرقم
            result = db_manager.fetch_one(
                "SELECT id, name FROM beneficiaries WHERE number = ? AND is_active = 1",
                (number,)
            )

            if result:
                return {'id': result[0], 'name': result[1]}
            return None

        except Exception as e:
            print(f"خطأ في التحقق من تكرار الرقم: {e}")
            return None

    def load_data(self):
        """تحميل البيانات المطلوبة"""
        self.load_units()
        self.load_departments()
        self.load_sections()



    def load_units(self, department_id=None):
        """تحميل قائمة الوحدات"""
        try:
            if department_id:
                self.units = Unit.get_all(department_id=department_id)
            else:
                self.units = Unit.get_all()

            unit_names = ["-- اختر الوحدة --"] + [unit.name for unit in self.units]
            if hasattr(self, 'unit_combo'):
                self.unit_combo['values'] = unit_names
                self.unit_combo.set("-- اختر الوحدة --")
        except Exception as e:
            print(f"خطأ في تحميل الوحدات: {e}")

    def load_departments(self):
        """تحميل قائمة الإدارات"""
        try:
            self.departments = Department.get_all()
            dept_names = ["-- اختر الإدارة --"] + [dept.name for dept in self.departments]
            if hasattr(self, 'dept_combo'):
                self.dept_combo['values'] = dept_names
                self.dept_combo.set("-- اختر الإدارة --")
        except Exception as e:
            print(f"خطأ في تحميل الإدارات: {e}")

    def load_sections(self, department_id=None):
        """تحميل قائمة الأقسام"""
        try:
            if department_id:
                self.sections = Section.get_all(department_id=department_id)
            else:
                self.sections = Section.get_all()

            section_names = ["-- اختر القسم --"] + [section.name for section in self.sections]
            if hasattr(self, 'section_combo'):
                self.section_combo['values'] = section_names
                self.section_combo.set("-- اختر القسم --")
        except Exception as e:
            print(f"خطأ في تحميل الأقسام: {e}")

    def on_category_change_old(self, event=None):
        """معالج تغيير الفئة - نسخة قديمة"""
        category = self.category_var.get()

        if category == "ضابط":
            ranks = ["-- اختر الرتبة --"] + self.officer_ranks
            self.rank_combo['values'] = ranks
        elif category == "فرد":
            ranks = ["-- اختر الرتبة --"] + self.soldier_ranks
            self.rank_combo['values'] = ranks
        elif category == "أخرى":
            ranks = ["-- اختر الرتبة --"] + self.other_ranks
            self.rank_combo['values'] = ranks
        else:
            self.rank_combo['values'] = ["-- اختر الرتبة --"]

        # إعادة تعيين الرتبة
        self.rank_combo.set("-- اختر الرتبة --")

    def on_unit_change(self, event=None):
        """معالج تغيير الوحدة"""
        unit_name = self.unit_var.get()
        if unit_name and unit_name != "-- اختر الوحدة --":
            # البحث عن الوحدة وتحديد الإدارة التابعة لها
            for unit in self.units:
                if unit.name == unit_name:
                    # تحديد الإدارة التابعة للوحدة
                    if unit.department_id:
                        dept = next((d for d in self.departments if d.id == unit.department_id), None)
                        if dept:
                            self.department_var.set(dept.name)
                            self.load_sections(dept.id)
                    break

    def on_department_change(self, event=None):
        """معالج تغيير الإدارة"""
        dept_name = self.department_var.get()
        if dept_name and dept_name != "-- اختر الإدارة --":
            # البحث عن معرف الإدارة
            dept_id = None
            for dept in self.departments:
                if dept.name == dept_name:
                    dept_id = dept.id
                    break

            if dept_id:
                self.load_sections(dept_id)
                self.load_units(dept_id)  # تحديث الوحدات حسب الإدارة
            else:
                self.load_sections()
                self.load_units()
        else:
            self.load_sections()
            self.load_units()

    def validate_form(self):
        """التحقق من صحة البيانات"""
        errors = []

        # التحقق من الاسم
        name = self.name_var.get().strip()
        if not name:
            errors.append("يرجى إدخال الاسم")
        elif len(name) < 2:
            errors.append("يجب أن يكون الاسم أكثر من حرفين")

        # التحقق من الرقم العام
        number = self.number_var.get().strip()
        if not number:
            errors.append("يرجى إدخال الرقم العام")
        elif len(number) < 1:
            errors.append("يجب إدخال رقم عام صحيح")

        # التحقق من الوحدة (إجباري)
        if not self.unit_var.get() or self.unit_var.get() == "-- اختر الوحدة --":
            errors.append("يرجى اختيار الوحدة")

        # التحقق من الإدارة (إجباري)
        if not self.department_var.get() or self.department_var.get() == "-- اختر الإدارة --":
            errors.append("يرجى اختيار الإدارة")

        # الحقول الاختيارية: الفئة، الرتبة، القسم - لا نتحقق منها

        return errors

    def save_beneficiary(self):
        """حفظ بيانات المستفيد"""
        # التحقق من صحة البيانات
        errors = self.validate_form()
        if errors:
            messagebox.showerror("خطأ في البيانات", "\n".join(errors))
            return

        try:
            # التحقق من عدم تكرار الرقم العام (إلا في وضع التعديل للمستفيد نفسه)
            number = self.number_var.get().strip()
            if number:
                existing_beneficiary = self.check_duplicate_number(number)
                if existing_beneficiary:
                    # في وضع التعديل، تجاهل إذا كان نفس المستفيد
                    if not (self.edit_mode and self.beneficiary_data and
                           existing_beneficiary['id'] == self.beneficiary_data.id):
                        messagebox.showerror(
                            "رقم مكرر",
                            f"الرقم العام '{number}' موجود مسبقاً للمستفيد: {existing_beneficiary['name']}\n"
                            f"يرجى استخدام رقم مختلف."
                        )
                        return

            # إنشاء أو تحديث المستفيد
            if self.edit_mode and self.beneficiary_data:
                beneficiary = self.beneficiary_data
            else:
                beneficiary = Beneficiary()

            # تعيين البيانات الأساسية
            beneficiary.name = self.name_var.get().strip()

            # تنظيف الرقم العام لضمان عرضه كعدد صحيح
            cleaned_number = None
            if number:
                try:
                    # إزالة أي أرقام عشرية وعرض الرقم كعدد صحيح
                    if '.' in str(number):
                        cleaned_number = str(int(float(number)))
                    else:
                        cleaned_number = str(number)
                except (ValueError, TypeError):
                    cleaned_number = str(number)

            beneficiary.number = cleaned_number

            # تعيين الفئة والرتبة
            category = self.category_var.get() if self.category_var.get() != "-- اختر الفئة --" else None
            rank = self.rank_var.get() if self.rank_var.get() != "-- اختر الرتبة --" else None

            # دمج الفئة والرتبة في حقل الرتبة
            if category and rank:
                beneficiary.rank = f"{category} - {rank}"
            elif rank:
                beneficiary.rank = rank
            else:
                beneficiary.rank = category

            beneficiary.is_active = True
            beneficiary.entry_date = date.today()

            # البحث عن معرف الوحدة
            unit_name = self.unit_var.get()
            if unit_name and unit_name != "-- اختر الوحدة --":
                for unit in self.units:
                    if unit.name == unit_name:
                        beneficiary.unit_id = unit.id
                        break

            # البحث عن معرف الإدارة
            dept_name = self.department_var.get()
            if dept_name and dept_name != "-- اختر الإدارة --":
                for dept in self.departments:
                    if dept.name == dept_name:
                        beneficiary.department_id = dept.id
                        break

            # البحث عن معرف القسم
            section_name = self.section_var.get()
            if section_name and section_name != "-- اختر القسم --":
                for section in self.sections:
                    if section.name == section_name:
                        beneficiary.section_id = section.id
                        break

            # تعيين مدخل البيانات (المستخدم الحالي)
            try:
                if hasattr(self.beneficiaries_window, 'main_window') and self.beneficiaries_window.main_window:
                    if hasattr(self.beneficiaries_window.main_window, 'current_user'):
                        current_user = self.beneficiaries_window.main_window.current_user
                        if current_user and hasattr(current_user, 'id'):
                            beneficiary.data_entry_user_id = current_user.id
            except:
                pass  # إذا لم نتمكن من الحصول على المستخدم، نتركه فارغاً

            # حفظ المستفيد
            if beneficiary.save():
                # إظهار رسالة النجاح المحسنة
                if self.edit_mode:
                    success_message = "تم تحديث بيانات المستفيد بنجاح"
                    button_text = "تم التحديث ✅"
                else:
                    success_message = "تم إضافة المستفيد بنجاح"
                    button_text = "تم الحفظ ✅"

                self.show_success_message(success_message)

                # تعطيل زر الحفظ مؤقتاً
                self.save_btn.configure(state="disabled", text=button_text)

                # في وضع الإضافة: مسح النموذج، في وضع التعديل: إبقاء البيانات
                if not self.edit_mode:
                    self.clear_form()

                # تحديث شاشة إدارة المستفيدين
                if hasattr(self, 'beneficiaries_window') and self.beneficiaries_window:
                    self.beneficiaries_window.refresh_data()

                # إعادة تفعيل زر الحفظ بعد 2 ثانية
                self.window.after(2000, self.reset_save_button)

            else:
                messagebox.showerror("خطأ", "فشل في حفظ بيانات المستفيد")

        except Exception as e:
            error_message = str(e)
            if "UNIQUE constraint failed" in error_message:
                messagebox.showerror(
                    "رقم مكرر",
                    f"الرقم العام '{number}' موجود مسبقاً في النظام.\n"
                    f"يرجى استخدام رقم مختلف."
                )
            else:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء الحفظ: {e}")
                print(f"تفاصيل الخطأ: {e}")

    def reset_save_button(self):
        """إعادة تفعيل زر الحفظ"""
        try:
            if hasattr(self, 'save_btn'):
                self.save_btn.configure(state="normal", text="💾 حفظ البيانات")
                print("✅ تم إعادة تفعيل زر الحفظ")
        except Exception as e:
            print(f"خطأ في إعادة تفعيل زر الحفظ: {e}")

    def show_success_message(self, message="تم حفظ البيانات بنجاح! 🎉"):
        """إظهار رسالة النجاح المحسنة مع تأثيرات بصرية"""
        try:
            # تحديث نص الرسالة
            if hasattr(self, 'success_label'):
                self.success_label.configure(text=message)

            # إظهار رسالة النجاح في أعلى النافذة
            if hasattr(self, 'success_frame'):
                # إظهار الرسالة في موضع مناسب
                try:
                    # محاولة وضع الرسالة في أعلى النافذة
                    children = list(self.success_frame.master.winfo_children())
                    if children:
                        self.success_frame.pack(fill=tk.X, pady=(10, 0), before=children[0])
                    else:
                        self.success_frame.pack(fill=tk.X, pady=(10, 0))
                except:
                    # في حالة فشل الطريقة الأولى، استخدم طريقة بسيطة
                    self.success_frame.pack(fill=tk.X, pady=(10, 0))

            # إعداد شريط التقدم
            if hasattr(self, 'progress_bar'):
                self.progress_bar['value'] = 100

            # تحديث النافذة
            self.window.update()

            # تمرير النافذة لأعلى لإظهار الرسالة
            self.window.focus_force()

            # إضافة تأثير بصري متقدم
            self.animate_success_message()

            # بدء العد التنازلي مع شريط التقدم
            self.start_countdown_timer()

            # إخفاء الرسالة تلقائياً بعد 3 ثوان
            self.hide_timer = self.window.after(3000, self.hide_success_message)

            print("✅ تم إظهار رسالة النجاح")

        except Exception as e:
            print(f"خطأ في إظهار رسالة النجاح: {e}")
            # رسالة نجاح بديلة بسيطة
            try:
                messagebox.showinfo("نجح", message)
            except:
                print(f"رسالة النجاح: {message}")

    def start_countdown_timer(self):
        """بدء العد التنازلي مع شريط التقدم"""
        try:
            duration = 3000  # 3 ثوان
            steps = 30  # 30 خطوة
            step_time = duration // steps

            def update_progress(step=0):
                if step <= steps:
                    progress_value = 100 - (step * 100 // steps)
                    self.progress_bar['value'] = progress_value
                    self.window.after(step_time, lambda: update_progress(step + 1))

            update_progress()

        except Exception as e:
            print(f"خطأ في العد التنازلي: {e}")

    def animate_success_message(self):
        """إضافة تأثير بصري لرسالة النجاح"""
        try:
            # تأثير وميض خفيف
            original_style = "success"

            def flash_effect(count=0):
                if count < 3:  # 3 ومضات
                    if count % 2 == 0:
                        # تغيير إلى لون أفتح
                        for widget in self.success_frame.winfo_children():
                            if hasattr(widget, 'configure'):
                                widget.configure(bootstyle="success")
                    else:
                        # العودة للون الأصلي
                        for widget in self.success_frame.winfo_children():
                            if hasattr(widget, 'configure'):
                                widget.configure(bootstyle="success")

                    self.window.after(200, lambda: flash_effect(count + 1))

            flash_effect()

        except Exception as e:
            print(f"خطأ في تأثير رسالة النجاح: {e}")

    def hide_success_message(self, event=None):
        """إخفاء رسالة النجاح"""
        try:
            # إلغاء المؤقت إذا كان موجوداً
            if hasattr(self, 'hide_timer'):
                self.window.after_cancel(self.hide_timer)

            # إخفاء الرسالة مع تأثير انزلاق
            self.success_frame.pack_forget()

            # إعادة التركيز للنافذة
            self.window.focus_force()

        except Exception as e:
            print(f"خطأ في إخفاء رسالة النجاح: {e}")

    def close_after_save(self):
        """إغلاق النافذة بعد الحفظ"""
        try:
            # إعادة تفعيل زر الحفظ
            self.save_btn.configure(state="normal", text="💾 حفظ")

            # إغلاق النافذة
            self.close_window()

        except Exception as e:
            print(f"خطأ في إغلاق النافذة: {e}")
            self.close_window()

    def clear_form(self):
        """مسح جميع البيانات من النموذج"""
        try:
            # مسح جميع الحقول بدون تأكيد (للاستخدام بعد الحفظ)
            self.name_var.set("")
            self.number_var.set("")
            self.category_var.set("-- اختر الفئة --")
            self.rank_var.set("-- اختر الرتبة --")
            self.unit_var.set("-- اختر الوحدة --")
            self.department_var.set("-- اختر الإدارة --")
            self.section_var.set("-- اختر القسم --")

            # إعادة تعيين القوائم
            if hasattr(self, 'category_combo'):
                self.category_combo.set("-- اختر الفئة --")
            if hasattr(self, 'rank_combo'):
                self.rank_combo.set("-- اختر الرتبة --")
            if hasattr(self, 'unit_combo'):
                self.unit_combo.set("-- اختر الوحدة --")
            if hasattr(self, 'dept_combo'):
                self.dept_combo.set("-- اختر الإدارة --")
            if hasattr(self, 'section_combo'):
                self.section_combo.set("-- اختر القسم --")

            # إعادة تحميل قوائم الرتب
            if hasattr(self, 'rank_combo'):
                self.rank_combo['values'] = ["-- اختر الرتبة --"]

            # تفعيل الحقول صراحة
            if hasattr(self, 'name_entry'):
                self.name_entry.configure(state="normal")
            if hasattr(self, 'number_entry'):
                self.number_entry.configure(state="normal")

            # إخفاء رسالة النجاح إذا كانت ظاهرة
            self.hide_success_message()

            # التركيز على حقل الاسم مع تأخير قصير
            self.window.after(50, self.focus_name_field)

            print("✅ تم مسح البيانات وإعداد النموذج للإدخال التالي")

        except Exception as e:
            print(f"خطأ في مسح البيانات: {e}")

    def clear_form_with_confirmation(self):
        """مسح البيانات مع التأكيد (للاستخدام مع زر المسح)"""
        try:
            # تأكيد المسح
            if messagebox.askyesno("تأكيد المسح", "هل تريد مسح جميع البيانات؟"):
                self.clear_form()
        except Exception as e:
            print(f"خطأ في مسح البيانات مع التأكيد: {e}")

    def reset_save_button(self):
        """إعادة تفعيل زر الحفظ"""
        try:
            if hasattr(self, 'save_btn'):
                save_text = "💾 حفظ التعديلات" if self.edit_mode else "💾 حفظ البيانات"
                self.save_btn.configure(state="normal", text=save_text)
                print("✅ تم إعادة تفعيل زر الحفظ")
        except Exception as e:
            print(f"خطأ في إعادة تفعيل زر الحفظ: {e}")

    def show_success_message(self, message="تم حفظ البيانات بنجاح! 🎉"):
        """إظهار رسالة النجاح"""
        try:
            if hasattr(self, 'success_label'):
                self.success_label.configure(text=message)
            if hasattr(self, 'success_frame'):
                self.success_frame.pack(fill=tk.X, pady=(10, 0))
            print(f"✅ رسالة النجاح: {message}")
        except Exception as e:
            print(f"خطأ في إظهار رسالة النجاح: {e}")

    def hide_success_message(self, event=None):
        """إخفاء رسالة النجاح"""
        try:
            if hasattr(self, 'success_frame'):
                self.success_frame.pack_forget()
        except Exception as e:
            print(f"خطأ في إخفاء رسالة النجاح: {e}")

    def create_success_message(self, parent):
        """إنشاء رسالة النجاح"""
        try:
            self.success_frame = ttk_bs.Frame(parent)
            self.success_label = ttk_bs.Label(
                self.success_frame,
                text="",
                bootstyle="success",
                anchor="center"
            )
            self.success_label.pack(pady=10)
        except Exception as e:
            print(f"خطأ في إنشاء رسالة النجاح: {e}")

    def ensure_fields_ready(self):
        """التأكد من جاهزية الحقول"""
        try:
            if hasattr(self, 'name_entry'):
                self.name_entry.configure(state="normal")
            if hasattr(self, 'number_entry'):
                self.number_entry.configure(state="normal")
        except Exception as e:
            print(f"خطأ في تجهيز الحقول: {e}")

    def final_enable_fields(self):
        """تفعيل نهائي للحقول"""
        try:
            if hasattr(self, 'name_entry'):
                self.name_entry.configure(state="normal")
            if hasattr(self, 'number_entry'):
                self.number_entry.configure(state="normal")
        except Exception as e:
            print(f"خطأ في التفعيل النهائي: {e}")

    def focus_name_field(self):
        """التركيز على حقل الاسم"""
        try:
            if hasattr(self, 'name_entry'):
                self.name_entry.focus_set()
        except Exception as e:
            print(f"خطأ في التركيز على حقل الاسم: {e}")

    def set_current_user(self):
        """تعيين المستخدم الحالي"""
        try:
            if hasattr(self.beneficiaries_window, 'main_window'):
                if hasattr(self.beneficiaries_window.main_window, 'current_user'):
                    user = self.beneficiaries_window.main_window.current_user
                    if user and hasattr(user, 'username'):
                        self.data_entry_var.set(user.username)
        except Exception as e:
            print(f"خطأ في تعيين المستخدم الحالي: {e}")

    def populate_form_data(self):
        """تعبئة البيانات في وضع التعديل"""
        try:
            if self.edit_mode and self.beneficiary_data:
                self.name_var.set(self.beneficiary_data.name or "")
                self.number_var.set(self.beneficiary_data.number or "")
                # يمكن إضافة المزيد من الحقول هنا
        except Exception as e:
            print(f"خطأ في تعبئة البيانات: {e}")

    def check_duplicate_number(self, number):
        """التحقق من تكرار الرقم العام"""
        try:
            beneficiaries = Beneficiary.get_all()
            for beneficiary in beneficiaries:
                if beneficiary.number == number:
                    return {'id': beneficiary.id, 'name': beneficiary.name}
            return None
        except Exception as e:
            print(f"خطأ في التحقق من تكرار الرقم: {e}")
            return None

    # دوال معالجة الأحداث
    def on_name_focus_in(self, event):
        """عند التركيز على حقل الاسم"""
        pass

    def on_name_key_press(self, event):
        """عند الضغط على مفتاح في حقل الاسم"""
        pass

    def on_name_click(self, event):
        """عند النقر على حقل الاسم"""
        pass

    def on_number_focus_in(self, event):
        """عند التركيز على حقل الرقم"""
        pass

    def on_number_key_press(self, event):
        """عند الضغط على مفتاح في حقل الرقم"""
        pass

    def on_number_click(self, event):
        """عند النقر على حقل الرقم"""
        pass

    def on_category_change(self, event):
        """عند تغيير الفئة"""
        try:
            category = self.category_var.get()
            if category == "ضابط":
                self.rank_combo['values'] = ["-- اختر الرتبة --"] + self.officer_ranks
            elif category == "فرد":
                self.rank_combo['values'] = ["-- اختر الرتبة --"] + self.soldier_ranks
            elif category == "أخرى":
                self.rank_combo['values'] = ["-- اختر الرتبة --"] + self.other_ranks
            else:
                self.rank_combo['values'] = ["-- اختر الرتبة --"]
            
            self.rank_combo.set("-- اختر الرتبة --")
        except Exception as e:
            print(f"خطأ في تغيير الفئة: {e}")

    def on_unit_change(self, event):
        """عند تغيير الوحدة"""
        pass

    def on_department_change(self, event):
        """عند تغيير الإدارة"""
        pass

    def close_window(self):
        """إغلاق النافذة"""
        try:
            # إلغاء أي مؤقتات قبل الإغلاق
            if hasattr(self, 'hide_timer'):
                self.window.after_cancel(self.hide_timer)

            self.window.destroy()

        except Exception as e:
            print(f"خطأ في إغلاق النافذة: {e}")

    def setup_add_beneficiary_shortcuts(self):
        """إعداد مفاتيح الاختصار لنافذة إضافة/تعديل المستفيد"""
        try:
            # إنشاء معالج السياق
            self.context_handler = ContextHandler()

            # تعيين دوال العمليات
            self.context_handler.set_save_callback(self.shortcut_save_beneficiary)
            self.context_handler.set_delete_callback(self.shortcut_clear_form)
            self.context_handler.set_copy_callback(self.shortcut_copy_data)
            self.context_handler.set_paste_callback(self.shortcut_paste_data)

            # تفعيل مفاتيح الاختصار
            self.global_shortcuts = GlobalShortcuts(self.window, self.context_handler)
        except Exception as e:
            print(f"خطأ في إعداد مفاتيح الاختصار: {e}")

    def shortcut_save_beneficiary(self):
        """عملية الحفظ (F1)"""
        try:
            self.save_beneficiary()
        except Exception as e:
            print(f"خطأ في حفظ المستفيد: {e}")

    def shortcut_clear_form(self):
        """مسح النموذج (F2)"""
        try:
            # مسح جميع الحقول
            self.name_var.set("")
            self.number_var.set("")
            self.category_var.set("-- اختر الفئة --")
            self.rank_var.set("-- اختر الرتبة --")
            self.unit_var.set("-- اختر الوحدة --")
            self.department_var.set("-- اختر الإدارة --")
            self.section_var.set("-- اختر القسم --")
            print("تم مسح النموذج")
        except Exception as e:
            print(f"خطأ في مسح النموذج: {e}")

    def shortcut_copy_data(self):
        """نسخ البيانات (F3)"""
        try:
            import pyperclip
            # تجميع البيانات الحالية
            data_text = f"""الاسم: {self.name_var.get()}
الرقم العام: {self.number_var.get()}
الفئة: {self.category_var.get()}
الرتبة: {self.rank_var.get()}
الوحدة: {self.unit_var.get()}
الإدارة: {self.department_var.get()}
القسم: {self.section_var.get()}"""
            pyperclip.copy(data_text)
            print("تم نسخ بيانات المستفيد")
        except Exception as e:
            print(f"خطأ في نسخ البيانات: {e}")

    def shortcut_paste_data(self):
        """لصق البيانات (F4)"""
        try:
            import pyperclip
            clipboard_data = pyperclip.paste()
            print(f"البيانات المنسوخة: {clipboard_data}")
            # يمكن إضافة منطق لتحليل البيانات المنسوخة وملء النموذج
        except Exception as e:
            print(f"خطأ في لصق البيانات: {e}")

    def setup_shortcuts(self):
        """إعداد مفاتيح الاختصار للمستفيدين"""
        try:
            # إنشاء معالج السياق
            self.context_handler = ContextHandler()

            # تعيين دوال العمليات
            self.context_handler.set_save_callback(self.shortcut_save_beneficiary)
            self.context_handler.set_delete_callback(self.delete_selected_beneficiary)
            self.context_handler.set_copy_callback(self.copy_beneficiary_data)
            self.context_handler.set_paste_callback(self.paste_beneficiary_data)

            # تفعيل مفاتيح الاختصار على النافذة الرئيسية
            if hasattr(self.main_window, 'parent'):
                self.global_shortcuts = GlobalShortcuts(self.main_window.parent, self.context_handler)
        except Exception as e:
            print(f"خطأ في إعداد مفاتيح الاختصار: {e}")

    def shortcut_save_beneficiary(self):
        """حفظ بيانات المستفيد الحالي (مفتاح اختصار)"""
        try:
            # إذا كان هناك مستفيد محدد، فتح نافذة التعديل
            if self.current_beneficiary:
                self.edit_beneficiary()
            else:
                # فتح نافذة إضافة مستفيد جديد
                self.add_beneficiary()
        except Exception as e:
            print(f"خطأ في حفظ المستفيد: {e}")

    def delete_selected_beneficiary(self):
        """حذف المستفيد المحدد"""
        try:
            if self.tree and self.tree.selection():
                self.delete_beneficiary()
            else:
                print("لا يوجد مستفيد محدد للحذف")
        except Exception as e:
            print(f"خطأ في حذف المستفيد: {e}")

    def copy_beneficiary_data(self):
        """نسخ بيانات المستفيد المحدد"""
        try:
            if self.tree and self.tree.selection():
                selection = self.tree.selection()[0]
                item_data = self.tree.item(selection)
                values = item_data.get('values', [])

                if values:
                    import pyperclip
                    # تنسيق البيانات للنسخ
                    data_text = f"الاسم: {values[1]}\nالرقم العام: {values[2]}\nالوحدة: {values[3]}\nالإدارة: {values[4]}"
                    pyperclip.copy(data_text)
                    print("تم نسخ بيانات المستفيد")
            else:
                print("لا يوجد مستفيد محدد للنسخ")
        except Exception as e:
            print(f"خطأ في نسخ بيانات المستفيد: {e}")

    def paste_beneficiary_data(self):
        """لصق بيانات المستفيد"""
        try:
            # في هذا السياق، يمكن أن تكون عملية اللصق هي استيراد من Excel
            self.import_from_excel()
        except Exception as e:
            print(f"خطأ في لصق بيانات المستفيد: {e}")
