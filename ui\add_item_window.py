#!/usr/bin/env python3
"""
شاشة إضافة صنف جديد
Add New Item Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime, date
import re

from config import APP_CONFIG, UI_CONFIG, get_message
from models import Item, Category, OrganizationalChart, AddedItem
from ui.global_shortcuts import GlobalShortcuts, ContextHandler

class AddItemWindow:
    """شاشة إضافة صنف جديد"""
    
    def __init__(self, parent, main_window):
        self.parent = parent
        self.main_window = main_window
        self.add_window = None
        
        # متغيرات النموذج
        self.item_number_var = tk.StringVar()
        self.item_name_var = tk.StringVar()
        self.custody_type_var = tk.StringVar()
        self.classification_var = tk.StringVar()
        self.unit_var = tk.StringVar()
        self.quantity_var = tk.StringVar()
        self.data_entry_var = tk.StringVar()
        self.entry_date_var = tk.StringVar()
        
        # تعيين القيم الافتراضية
        self.data_entry_var.set(self.main_window.current_user.username if hasattr(self.main_window, 'current_user') else "المدير")
        self.entry_date_var.set(datetime.now().strftime("%Y-%m-%d"))

        # تحميل بيانات الجدول التنظيمي مرة واحدة
        self.organizational_items = {}
        self.load_organizational_data()

        self.setup_window()

        # تفعيل مفاتيح الاختصار
        self.setup_shortcuts()

    def load_organizational_data(self):
        """تحميل بيانات الجدول التنظيمي مرة واحدة"""
        try:
            # تحميل جميع الأصناف من الجدول التنظيمي
            organizational_items_list = OrganizationalChart.get_all(active_only=True)

            # تحويل القائمة إلى قاموس للوصول السريع
            for item in organizational_items_list:
                if hasattr(item, 'item_code') and hasattr(item, 'item_name'):
                    self.organizational_items[item.item_code] = item.item_name

            print(f"✅ تم تحميل {len(self.organizational_items)} صنف من الجدول التنظيمي")

        except Exception as e:
            print(f"خطأ في تحميل بيانات الجدول التنظيمي: {e}")
            self.organizational_items = {}

    def setup_window(self):
        """إعداد النافذة"""
        self.add_window = tk.Toplevel(self.parent)
        self.add_window.title("📦 إضافة صنف جديد")
        self.add_window.geometry("1100x500")
        self.add_window.resizable(False, False)
        self.add_window.transient(self.parent)
        self.add_window.grab_set()

        # إعداد المحتوى أولاً
        self.setup_content()

        # توسيط النافذة بعد إعداد المحتوى
        self.add_window.after(50, self.center_window)

        # جعل النافذة في المقدمة
        self.add_window.lift()
        self.add_window.focus_force()

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        # تحديث النافذة أولاً
        self.add_window.update_idletasks()

        # الحصول على أبعاد الشاشة
        screen_width = self.add_window.winfo_screenwidth()
        screen_height = self.add_window.winfo_screenheight()

        # أبعاد النافذة
        window_width = 1100
        window_height = 500

        # حساب الموضع للتوسيط الدقيق في وسط الشاشة
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        # التأكد من أن النافذة لا تخرج عن حدود الشاشة
        x = max(0, min(x, screen_width - window_width))
        y = max(0, min(y, screen_height - window_height))

        # تطبيق الموضع والحجم
        self.add_window.geometry(f"{window_width}x{window_height}+{x}+{y}")

        # تحديث إضافي للتأكد من التوسيط
        self.add_window.update()
        self.add_window.focus_force()
        print(f"✅ تم توسيط النافذة: {window_width}x{window_height} في الموضع ({x}, {y})")
    
    def setup_content(self):
        """إعداد محتوى النافذة"""
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.add_window)
        main_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)
        
        # العنوان
        self.create_header(main_frame)
        
        # النموذج
        self.create_form(main_frame)
        
        # الأزرار
        self.create_buttons(main_frame)
    
    def create_header(self, parent):
        """إنشاء العنوان"""
        header_frame = ttk_bs.Frame(parent)
        header_frame.pack(fill=X, pady=(0, 30))

        # زر العودة للقائمة
        back_btn = ttk_bs.Button(
            header_frame,
            text="← العودة للقائمة",
            command=self.close_window,
            bootstyle="outline-secondary",
            width=22
        )
        back_btn.pack(side=LEFT)

        # العنوان
        title_label = ttk_bs.Label(
            header_frame,
            text="📦 إضافة صنف جديد",
            bootstyle="primary"
        )
        title_label.pack(side=RIGHT)

        # خط فاصل
        separator = ttk_bs.Separator(parent, orient=HORIZONTAL)
        separator.pack(fill=X, pady=10)
    
    def create_form(self, parent):
        """إنشاء النموذج"""
        form_frame = ttk_bs.Frame(parent)
        form_frame.pack(fill=BOTH, expand=True, padx=20)

        # الصف الأول - رقم الصنف واسم الصنف
        row1 = ttk_bs.Frame(form_frame)
        row1.pack(fill=X, pady=15)

        # رقم الصنف (يمين) - مع بحث تلقائي محسن
        right_frame1 = ttk_bs.Frame(row1)
        right_frame1.pack(side=RIGHT, fill=X, expand=True, padx=10)
        ttk_bs.Label(right_frame1, text="رقم الصنف", width=15, anchor=E).pack(side=RIGHT, padx=5)
        
        # حقل البحث التلقائي لرقم الصنف
        self.item_number_combo = ttk_bs.Combobox(
            right_frame1,
            textvariable=self.item_number_var,
            values=self.get_item_numbers(),
            width=50
        )
        self.item_number_combo.pack(side=RIGHT, padx=5)
        self.item_number_combo.set("-- ابحث أو اختر رقم الصنف --")

        # ربط أحداث البحث التلقائي
        self.item_number_combo.bind('<<ComboboxSelected>>', self.on_item_number_change)
        self.item_number_combo.bind('<KeyRelease>', self.on_item_number_search)
        self.item_number_combo.bind('<FocusIn>', self.on_item_number_focus_in)
        self.item_number_combo.bind('<FocusOut>', self.on_item_number_focus_out)

        # اسم الصنف (يسار)
        left_frame1 = ttk_bs.Frame(row1)
        left_frame1.pack(side=LEFT, fill=X, expand=True, padx=10)
        ttk_bs.Label(left_frame1, text="اسم الصنف", width=15, anchor=E).pack(side=RIGHT, padx=5)
        item_name_entry = ttk_bs.Entry(
            left_frame1,
            textvariable=self.item_name_var,
            width=50
        )
        item_name_entry.pack(side=RIGHT, padx=5)
        
        # الصف الثاني - نوع العهدة والتصنيف
        row2 = ttk_bs.Frame(form_frame)
        row2.pack(fill=X, pady=15)

        # نوع العهدة (يمين)
        right_frame2 = ttk_bs.Frame(row2)
        right_frame2.pack(side=RIGHT, fill=X, expand=True, padx=10)
        ttk_bs.Label(right_frame2, text="نوع العهدة", width=15, anchor=E).pack(side=RIGHT, padx=5)
        custody_combo = ttk_bs.Combobox(
            right_frame2,
            textvariable=self.custody_type_var,
            values=["مستهلكة", "مستديمة", "أخرى"],
            state="readonly",
            width=50
        )
        custody_combo.pack(side=RIGHT, padx=5)
        custody_combo.set("-- اختر نوع العهدة --")

        # التصنيف (يسار) - مع فلترة وبحث
        left_frame2 = ttk_bs.Frame(row2)
        left_frame2.pack(side=LEFT, fill=X, expand=True, padx=10)
        ttk_bs.Label(left_frame2, text="التصنيف", width=15, anchor=E).pack(side=RIGHT, padx=5)
        
        # قائمة منسدلة قابلة للكتابة والبحث
        classification_combo = ttk_bs.Combobox(
            left_frame2,
            textvariable=self.classification_var,
            values=self.get_classifications(),
            width=50
        )
        classification_combo.pack(side=RIGHT, padx=5)
        classification_combo.set("-- اختر أو ابحث عن التصنيف --")
        
        # إضافة وظيفة البحث والفلترة
        def filter_classifications(event):
            """فلترة التصنيفات حسب النص المدخل"""
            typed_text = classification_combo.get().lower()
            if typed_text and not typed_text.startswith("--"):
                # فلترة القائمة حسب النص المدخل
                all_classifications = self.get_classifications()
                filtered = [item for item in all_classifications if typed_text in item.lower()]
                classification_combo['values'] = filtered
            else:
                # إعادة تعيين القائمة الكاملة
                classification_combo['values'] = self.get_classifications()
        
        # ربط حدث الكتابة
        classification_combo.bind('<KeyRelease>', filter_classifications)
        
        # الصف الثالث - وحدة الصرف والكمية
        row3 = ttk_bs.Frame(form_frame)
        row3.pack(fill=X, pady=15)

        # وحدة الصرف (يمين) - مع فلترة وبحث
        right_frame3 = ttk_bs.Frame(row3)
        right_frame3.pack(side=RIGHT, fill=X, expand=True, padx=10)
        ttk_bs.Label(right_frame3, text="وحدة الصرف", width=15, anchor=E).pack(side=RIGHT, padx=5)
        
        # قائمة منسدلة قابلة للكتابة والبحث
        unit_combo = ttk_bs.Combobox(
            right_frame3,
            textvariable=self.unit_var,
            values=self.get_units(),
            width=50
        )
        unit_combo.pack(side=RIGHT, padx=5)
        unit_combo.set("-- اختر أو ابحث عن وحدة الصرف --")
        
        # إضافة وظيفة البحث والفلترة
        def filter_units(event):
            """فلترة وحدات الصرف حسب النص المدخل"""
            typed_text = unit_combo.get().lower()
            if typed_text and not typed_text.startswith("--"):
                # فلترة القائمة حسب النص المدخل
                all_units = self.get_units()
                filtered = [item for item in all_units if typed_text in item.lower()]
                unit_combo['values'] = filtered
            else:
                # إعادة تعيين القائمة الكاملة
                unit_combo['values'] = self.get_units()
        
        # ربط حدث الكتابة
        unit_combo.bind('<KeyRelease>', filter_units)

        # الكمية (يسار)
        left_frame3 = ttk_bs.Frame(row3)
        left_frame3.pack(side=LEFT, fill=X, expand=True, padx=10)
        ttk_bs.Label(left_frame3, text="الكمية", width=15, anchor=E).pack(side=RIGHT, padx=5)
        quantity_entry = ttk_bs.Entry(
            left_frame3,
            textvariable=self.quantity_var,
            width=50,
            justify=CENTER
        )
        quantity_entry.pack(side=RIGHT, padx=5)

        # تعيين قيمة افتراضية للكمية
        self.quantity_var.set("1")

        # ربط التحقق من الأرقام فقط للكمية
        quantity_entry.bind('<KeyPress>', self.validate_number_input)
        
        # الصف الرابع - مدخل البيانات وتاريخ الإدخال
        row4 = ttk_bs.Frame(form_frame)
        row4.pack(fill=X, pady=15)

        # مدخل البيانات (يمين)
        right_frame4 = ttk_bs.Frame(row4)
        right_frame4.pack(side=RIGHT, fill=X, expand=True, padx=10)
        ttk_bs.Label(right_frame4, text="مدخل البيانات", width=15, anchor=E).pack(side=RIGHT, padx=5)
        data_entry_entry = ttk_bs.Entry(
            right_frame4,
            textvariable=self.data_entry_var,
            state="readonly",
            width=50
        )
        data_entry_entry.pack(side=RIGHT, padx=5)

        # تاريخ الإدخال (يسار)
        left_frame4 = ttk_bs.Frame(row4)
        left_frame4.pack(side=LEFT, fill=X, expand=True, padx=10)
        ttk_bs.Label(left_frame4, text="تاريخ الإدخال", width=15, anchor=E).pack(side=RIGHT, padx=5)
        date_entry = ttk_bs.Entry(
            left_frame4,
            textvariable=self.entry_date_var,
            state="readonly",
            width=50
        )
        date_entry.pack(side=RIGHT, padx=5)
    
    def create_buttons(self, parent):
        """إنشاء الأزرار"""
        buttons_frame = ttk_bs.Frame(parent)
        buttons_frame.pack(pady=40)

        # زر حفظ
        save_btn = ttk_bs.Button(
            buttons_frame,
            text="💾 حفظ",
            command=self.save_item,
            bootstyle="success",
            width=15
        )
        save_btn.pack(side=LEFT, padx=15)

        # زر إلغاء
        cancel_btn = ttk_bs.Button(
            buttons_frame,
            text="❌ إلغاء",
            command=self.close_window,
            bootstyle="secondary",
            width=15
        )
        cancel_btn.pack(side=LEFT, padx=15)
    
    def get_item_numbers(self):
        """الحصول على قائمة أرقام الأصناف من الجدول التنظيمي"""
        try:
            # استخدام البيانات المحملة مسبقاً
            item_numbers = list(self.organizational_items.keys())

            # ترتيب الأرقام
            item_numbers.sort()

            return item_numbers

        except Exception as e:
            print(f"خطأ في الحصول على أرقام الأصناف: {e}")
            # قائمة افتراضية في حالة الخطأ
            return ["708001", "708002", "708003", "708004", "708005"]
    
    def get_classifications(self):
        """الحصول على قائمة التصنيفات حسب الصورة"""
        return [
            "ملابس عسكرية",
            "أجهزة كهربائية",
            "أجهزة اتصالات",
            "أدوات رياضية",
            "أدوات سلامة",
            "أدوات مطبخ",
            "الوقود الكيماويات",
            "تجهيزات السكن",
            "سلاح",
            "عهدة شخصية",
            "أخرى"
        ]
    
    def get_units(self):
        """الحصول على قائمة وحدات الصرف الثابتة"""
        # قائمة ثابتة لوحدات الصرف - غير مرتبطة بأي شاشة أخرى
        return [
            "عدد",
            "قطعة", 
            "كرتون",
            "علبة",
            "أخرى"
        ]
    
    def on_item_number_change(self, event=None):
        """معالج تغيير رقم الصنف - تحديث اسم الصنف تلقائياً من الجدول التنظيمي"""
        try:
            selected_number = self.item_number_var.get()

            # التحقق من أن الرقم المختار صحيح
            if not selected_number or selected_number.startswith("--"):
                self.item_name_var.set("")
                self.hide_error_message()
                return

            # البحث عن الصنف في البيانات المحملة مسبقاً
            if selected_number in self.organizational_items:
                item_name = self.organizational_items[selected_number]
                self.item_name_var.set(item_name)
                print(f"✅ تم تحديث اسم الصنف: {item_name}")
                
                # التحقق من التكرار فوراً
                self.check_item_duplication(selected_number)
            else:
                # إذا لم يوجد الصنف، اتركه فارغ للمستخدم
                self.item_name_var.set("")
                print(f"⚠️ لم يتم العثور على اسم للصنف رقم: {selected_number}")
                self.hide_error_message()

        except Exception as e:
            print(f"خطأ في تحديث اسم الصنف: {e}")
            self.item_name_var.set("")
            self.hide_error_message()

    def on_item_number_search(self, event=None):
        """معالج البحث التلقائي في أرقام الأصناف"""
        try:
            typed_text = self.item_number_var.get().lower()
            
            # إخفاء رسالة الخطأ أثناء الكتابة
            self.hide_error_message()
            
            if typed_text and not typed_text.startswith("--"):
                # فلترة القائمة حسب النص المدخل
                all_numbers = self.get_item_numbers()
                filtered = [item for item in all_numbers if typed_text in item.lower()]
                self.item_number_combo['values'] = filtered
                
                # إذا كان هناك تطابق واحد فقط، اختره تلقائياً
                if len(filtered) == 1:
                    self.item_number_var.set(filtered[0])
                    self.on_item_number_change()
                    
            else:
                # إعادة تعيين القائمة الكاملة
                self.item_number_combo['values'] = self.get_item_numbers()
                
        except Exception as e:
            print(f"خطأ في البحث التلقائي: {e}")

    def on_item_number_focus_in(self, event=None):
        """عند التركيز على حقل رقم الصنف"""
        try:
            current_value = self.item_number_var.get()
            if current_value.startswith("--"):
                self.item_number_var.set("")
        except Exception as e:
            print(f"خطأ في focus_in: {e}")

    def on_item_number_focus_out(self, event=None):
        """عند فقدان التركيز من حقل رقم الصنف"""
        try:
            current_value = self.item_number_var.get().strip()
            if not current_value:
                self.item_number_var.set("-- ابحث أو اختر رقم الصنف --")
                self.item_name_var.set("")
                self.hide_error_message()
            else:
                # التحقق من التكرار عند فقدان التركيز
                self.check_item_duplication(current_value)
        except Exception as e:
            print(f"خطأ في focus_out: {e}")

    def check_item_duplication(self, item_number):
        """التحقق من تكرار رقم الصنف وعرض رسالة خطأ ملونة"""
        try:
            if not item_number or item_number.startswith("--"):
                self.hide_error_message()
                return False
                
            # التحقق من وجود الصنف في قاعدة البيانات
            existing_items = AddedItem.get_all()
            for item in existing_items:
                if item.item_number == item_number:
                    self.show_error_message(f"⚠️ الصنف رقم {item_number} مسجل مسبقاً ولا يمكن تسجيله مرة أخرى")
                    return True
                    
            # إخفاء رسالة الخطأ إذا لم يكن هناك تكرار
            self.hide_error_message()
            return False
            
        except Exception as e:
            print(f"خطأ في التحقق من التكرار: {e}")
            return False

    def show_error_message(self, message):
        """عرض رسالة خطأ ملونة بالأحمر"""
        try:
            # إنشاء أو تحديث تسمية الخطأ
            if not hasattr(self, 'error_label'):
                # إنشاء إطار للرسالة إذا لم يكن موجوداً
                if not hasattr(self, 'error_frame'):
                    self.error_frame = ttk_bs.Frame(self.add_window)
                    self.error_frame.pack(fill=X, padx=20, pady=(0, 10))
                
                self.error_label = ttk_bs.Label(
                    self.error_frame,
                    text="",
                    bootstyle="danger",
                    font=("Arial", 10, "bold")
                )
                self.error_label.pack()
            
            # تحديث النص وإظهار الرسالة
            self.error_label.config(text=message)
            self.error_frame.pack(fill=X, padx=20, pady=(0, 10))
            
        except Exception as e:
            print(f"خطأ في عرض رسالة الخطأ: {e}")

    def hide_error_message(self):
        """إخفاء رسالة الخطأ"""
        try:
            if hasattr(self, 'error_frame'):
                self.error_frame.pack_forget()
        except Exception as e:
            print(f"خطأ في إخفاء رسالة الخطأ: {e}")

    def validate_number_input(self, event):
        """التحقق من إدخال الأرقام الصحيحة فقط"""
        # السماح بالأرقام الصحيحة والمفاتيح الخاصة فقط
        allowed_keys = ['BackSpace', 'Delete', 'Left', 'Right', 'Tab', 'Return', 'KP_Enter']

        # السماح بالمفاتيح الخاصة
        if event.keysym in allowed_keys:
            return True

        # السماح بالأرقام فقط (0-9)
        if event.char.isdigit():
            return True

        # منع أي شيء آخر (بما في ذلك النقطة العشرية والعلامات)
        return "break"
    
    def save_item(self):
        """حفظ الصنف الجديد"""
        print("🚀 بدء عملية حفظ الصنف الجديد...")

        # التحقق من صحة البيانات
        if not self.validate_form():
            print("❌ فشل في التحقق من صحة البيانات")
            return

        try:
            print("📝 إنشاء كائن الصنف الجديد...")

            # إنشاء كائن الصنف الجديد في جدول الأصناف المضافة
            new_item = AddedItem()
            new_item.item_number = self.item_number_var.get().strip()
            new_item.item_name = self.item_name_var.get().strip()
            new_item.custody_type = self.custody_type_var.get().strip()
            new_item.classification = self.classification_var.get().strip()
            new_item.unit = self.unit_var.get().strip()
            new_item.current_quantity = int(self.quantity_var.get())
            new_item.entered_quantity = int(self.quantity_var.get())  # توحيد الكميتين
            new_item.data_entry_user = self.data_entry_var.get().strip()
            new_item.entry_date = self.entry_date_var.get().strip()
            new_item.is_active = True

            print(f"📋 بيانات الصنف المراد حفظه:")
            print(f"   رقم الصنف: '{new_item.item_number}'")
            print(f"   اسم الصنف: '{new_item.item_name}'")
            print(f"   نوع العهدة: '{new_item.custody_type}'")
            print(f"   التصنيف: '{new_item.classification}'")
            print(f"   الوحدة: '{new_item.unit}'")
            print(f"   الكمية: {new_item.current_quantity}")
            print(f"   مدخل البيانات: '{new_item.data_entry_user}'")
            print(f"   تاريخ الإدخال: '{new_item.entry_date}'")

            # التحقق من اتصال قاعدة البيانات
            from database import db_manager
            if not db_manager.is_connected():
                print("⚠️ محاولة إعادة الاتصال بقاعدة البيانات...")
                db_manager.connect()

            print("💾 محاولة حفظ الصنف في قاعدة البيانات...")

            # حفظ في قاعدة البيانات
            if new_item.save():
                print("✅ تم حفظ الصنف بنجاح!")

                # إنشاء حركة مخزون "إضافة" تلقائياً إذا كانت الكمية أكبر من صفر
                if new_item.current_quantity > 0:
                    print(f"📦 إنشاء حركة مخزون للكمية: {new_item.current_quantity}")
                    self.create_initial_inventory_movement(new_item)

                # عرض رسالة نجاح
                print("🎉 عرض رسالة النجاح...")
                self.show_success_message()

                # تحديث شاشة إدارة الأصناف إذا كانت مفتوحة
                print("🔄 تحديث الشاشات...")
                self.refresh_parent_windows()

                # إجبار تحديث فوري للواجهة
                self.add_window.update_idletasks()

                # مسح النموذج للإدخال الجديد
                print("🧹 مسح النموذج للإدخال الجديد...")
                self.clear_form()

                print("✅ تمت عملية الحفظ بنجاح!")
                # لا نغلق النافذة - نبقيها مفتوحة للإضافة مرة أخرى
            else:
                print("❌ فشل في حفظ الصنف في قاعدة البيانات")
                messagebox.showerror("خطأ", "فشل في حفظ الصنف في قاعدة البيانات\nيرجى التحقق من اتصال قاعدة البيانات")

        except ValueError as ve:
            error_msg = f"خطأ في البيانات المدخلة: {ve}"
            print(f"❌ {error_msg}")
            messagebox.showerror("خطأ في البيانات", error_msg)

        except Exception as e:
            error_msg = f"حدث خطأ أثناء الحفظ: {e}"
            print(f"❌ {error_msg}")
            print(f"   نوع الخطأ: {type(e).__name__}")
            import traceback
            print(f"   تفاصيل الخطأ: {traceback.format_exc()}")
            messagebox.showerror("خطأ", error_msg)

    def create_initial_inventory_movement(self, item):
        """إنشاء حركة مخزون إضافة تلقائية عند إضافة صنف جديد"""
        try:
            from models import InventoryMovement
            from datetime import datetime

            # إنشاء حركة مخزون "إضافة" للكمية المدخلة
            movement = InventoryMovement()
            movement.item_number = item.item_number
            movement.movement_type = "إضافة"
            movement.quantity = float(item.current_quantity)
            movement.organization_type = "إضافة أولية"
            movement.organization_name = "إضافة صنف جديد"
            movement.notes = f"إضافة تلقائية عند إنشاء الصنف - {item.item_name}"
            movement.movement_date = datetime.now()

            # الحصول على معرف المستخدم الحالي
            try:
                from database import db_manager
                # محاولة الحصول على المستخدم الحالي
                current_user = db_manager.fetch_one("""
                    SELECT id FROM users
                    WHERE is_active = 1
                    ORDER BY last_login DESC
                    LIMIT 1
                """)
                if current_user:
                    movement.user_id = current_user[0]
            except:
                movement.user_id = None

            # حفظ حركة المخزون
            if movement.save():
                print(f"✅ تم إنشاء حركة مخزون إضافة تلقائية للصنف {item.item_number} بكمية {item.current_quantity}")
            else:
                print(f"⚠️ فشل في إنشاء حركة المخزون التلقائية للصنف {item.item_number}")

        except Exception as e:
            print(f"❌ خطأ في إنشاء حركة المخزون التلقائية: {e}")
            # لا نعرض رسالة خطأ للمستخدم لأن هذا إجراء تلقائي

    def validate_form(self):
        """التحقق من صحة النموذج"""
        # التحقق من الحقول المطلوبة
        item_number = self.item_number_var.get().strip()
        if not item_number or item_number.startswith("--"):
            messagebox.showerror("خطأ", "يرجى اختيار رقم الصنف")
            return False

        if not self.item_name_var.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم الصنف")
            return False

        custody_type = self.custody_type_var.get()
        if not custody_type or custody_type.startswith("--"):
            messagebox.showerror("خطأ", "يرجى اختيار نوع العهدة")
            return False

        classification = self.classification_var.get()
        if not classification or classification.startswith("--"):
            messagebox.showerror("خطأ", "يرجى اختيار التصنيف")
            return False

        unit = self.unit_var.get()
        if not unit or unit.startswith("--"):
            messagebox.showerror("خطأ", "يرجى اختيار وحدة الصرف")
            return False

        if not self.quantity_var.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال الكمية")
            return False

        # التحقق من أن الكمية رقم صحيح
        try:
            quantity = int(self.quantity_var.get())
            if quantity < 0:
                messagebox.showerror("خطأ", "الكمية يجب أن تكون رقم موجب")
                return False
        except ValueError:
            messagebox.showerror("خطأ", "الكمية يجب أن تكون رقم صحيح")
            return False

        # التحقق من عدم تكرار رقم الصنف في الأصناف المضافة
        if self.check_item_duplication(item_number):
            # الرسالة معروضة بالفعل من دالة check_item_duplication
            return False

        return True
    
    def show_success_message(self):
        """عرض رسالة النجاح تختفي خلال 3 ثوان أو بالضغط"""
        # إنشاء نافذة رسالة مخصصة
        success_window = tk.Toplevel(self.add_window)
        success_window.title("✅ تم بنجاح")
        success_window.geometry("400x200")
        success_window.resizable(False, False)
        success_window.transient(self.add_window)
        success_window.grab_set()
        success_window.configure(bg='#d4edda')

        # توسيط النافذة
        success_window.update_idletasks()
        x = (success_window.winfo_screenwidth() - 400) // 2
        y = (success_window.winfo_screenheight() - 200) // 2
        success_window.geometry(f"400x200+{x}+{y}")

        # ربط الضغط في أي مكان لإغلاق النافذة
        def close_on_click(event=None):
            success_window.destroy()

        success_window.bind("<Button-1>", close_on_click)
        success_window.bind("<Key>", close_on_click)
        success_window.focus_set()

        # محتوى الرسالة
        frame = ttk_bs.Frame(success_window)
        frame.pack(fill=BOTH, expand=True, padx=30, pady=30)
        frame.bind("<Button-1>", close_on_click)

        # أيقونة النجاح
        success_icon = ttk_bs.Label(
            frame,
            text="✅",
            bootstyle="success"
        )
        success_icon.pack(pady=10)
        success_icon.bind("<Button-1>", close_on_click)

        # رسالة النجاح
        success_label = ttk_bs.Label(
            frame,
            text="تم حفظ الصنف بنجاح!",
            bootstyle="success"
        )
        success_label.pack(pady=10)
        success_label.bind("<Button-1>", close_on_click)

        # معلومات الصنف المحفوظ
        info_text = f"رقم الصنف: {self.item_number_var.get()}\nاسم الصنف: {self.item_name_var.get()}"
        info_label = ttk_bs.Label(
            frame,
            text=info_text,
            bootstyle="secondary",
            justify=CENTER
        )
        info_label.pack(pady=5)
        info_label.bind("<Button-1>", close_on_click)

        # رسالة للمستخدم
        hint_label = ttk_bs.Label(
            frame,
            text="(اضغط في أي مكان للإغلاق)",
            bootstyle="info"
        )
        hint_label.pack(pady=5)
        hint_label.bind("<Button-1>", close_on_click)

        # إغلاق تلقائي بعد 3 ثوان
        success_window.after(3000, close_on_click)

    def clear_form(self):
        """مسح النموذج للإدخال الجديد"""
        # مسح الحقول القابلة للتعديل
        self.item_number_var.set("-- ابحث أو اختر رقم الصنف --")
        self.item_name_var.set("")
        self.custody_type_var.set("-- اختر نوع العهدة --")
        self.classification_var.set("-- اختر التصنيف --")
        self.unit_var.set("-- اختر وحدة الصرف --")
        self.quantity_var.set("1")

        # تحديث تاريخ الإدخال للتاريخ الحالي
        self.entry_date_var.set(datetime.now().strftime("%Y-%m-%d"))
        
        # إخفاء رسالة الخطأ
        self.hide_error_message()

        print("✅ تم مسح النموذج للإدخال الجديد")

    def refresh_parent_windows(self):
        """تحديث النوافذ الأصلية بعد إضافة صنف جديد"""
        try:
            print("🔄 بدء تحديث النوافذ الأصلية...")

            # تحديث شاشة إدارة الأصناف إذا كانت مفتوحة
            if hasattr(self.main_window, 'load_items_data'):
                print("📦 تحديث شاشة إدارة الأصناف...")
                self.main_window.load_items_data()
                print("✅ تم تحديث شاشة إدارة الأصناف")

            # تحديث لوحة تحكم المخزون إذا كانت مفتوحة
            if hasattr(self.main_window, 'load_dashboard_data'):
                print("📊 تحديث لوحة تحكم المخزون...")
                self.main_window.load_dashboard_data()
                print("✅ تم تحديث لوحة تحكم المخزون")

            # إشعار النافذة الرئيسية بالتحديث
            if hasattr(self.main_window, 'refresh_all_data'):
                print("🏠 تحديث النافذة الرئيسية...")
                self.main_window.refresh_all_data()
                print("✅ تم تحديث جميع البيانات في النافذة الرئيسية")

            # إشعار عام للتحديث
            self.notify_all_windows_to_refresh()

        except Exception as e:
            print(f"⚠️ تحذير: فشل في تحديث بعض النوافذ: {e}")

    def notify_all_windows_to_refresh(self):
        """إشعار جميع النوافذ المفتوحة بالتحديث"""
        try:
            # البحث عن جميع النوافذ المفتوحة وتحديثها
            for widget in self.parent.winfo_children():
                if hasattr(widget, 'winfo_class') and widget.winfo_class() == 'Toplevel':
                    # البحث عن نوافذ لوحة التحكم أو إدارة الأصناف
                    for child in widget.winfo_children():
                        if hasattr(child, 'load_dashboard_data'):
                            print("🔄 تحديث لوحة تحكم مفتوحة...")
                            child.load_dashboard_data()
                        elif hasattr(child, 'load_items_data'):
                            print("🔄 تحديث شاشة أصناف مفتوحة...")
                            child.load_items_data()

            print("✅ تم إشعار جميع النوافذ بالتحديث")
        except Exception as e:
            print(f"⚠️ خطأ في إشعار النوافذ: {e}")

    def close_window(self):
        """إغلاق النافذة"""
        self.add_window.destroy()
    def setup_shortcuts(self):
        """إعداد مفاتيح الاختصار"""
        try:
            # إنشاء معالج السياق
            self.context_handler = ContextHandler()
            
            # تعيين دوال العمليات
            self.context_handler.set_save_callback(self.shortcut_save)
            self.context_handler.set_delete_callback(self.shortcut_delete)
            self.context_handler.set_copy_callback(self.shortcut_copy)
            self.context_handler.set_paste_callback(self.shortcut_paste)
            
            # تفعيل مفاتيح الاختصار
            target_window = getattr(self, 'add_window', None)
            if target_window:
                self.global_shortcuts = GlobalShortcuts(target_window, self.context_handler)
        except Exception as e:
            print(f"خطأ في إعداد مفاتيح الاختصار: {e}")
    
    def shortcut_save(self):
        """عملية الحفظ (F1)"""
        try:
            # البحث عن دوال الحفظ المتاحة
            save_methods = ['save_data', 'save_changes', 'save_item', 'save', 'add_item']
            for method_name in save_methods:
                if hasattr(self, method_name):
                    getattr(self, method_name)()
                    print(f"تم تنفيذ {method_name} بمفتاح F1")
                    return
            print("عملية الحفظ غير متاحة")
        except Exception as e:
            print(f"خطأ في عملية الحفظ: {e}")
    
    def shortcut_delete(self):
        """عملية الحذف (F2)"""
        try:
            # البحث عن دوال الحذف المتاحة
            delete_methods = ['delete_selected', 'delete_item', 'delete_data', 'delete']
            for method_name in delete_methods:
                if hasattr(self, method_name):
                    getattr(self, method_name)()
                    print(f"تم تنفيذ {method_name} بمفتاح F2")
                    return
            print("عملية الحذف غير متاحة")
        except Exception as e:
            print(f"خطأ في عملية الحذف: {e}")
    
    def shortcut_copy(self):
        """عملية النسخ (F3)"""
        try:
            # البحث عن دوال النسخ المتاحة
            copy_methods = ['copy_data', 'copy_selected', 'copy_item']
            for method_name in copy_methods:
                if hasattr(self, method_name):
                    getattr(self, method_name)()
                    print(f"تم تنفيذ {method_name} بمفتاح F3")
                    return
            
            # نسخ عامة
            import pyperclip
            pyperclip.copy("تم النسخ من النافذة")
            print("تم نسخ البيانات العامة")
        except Exception as e:
            print(f"خطأ في عملية النسخ: {e}")
    
    def shortcut_paste(self):
        """عملية اللصق (F4)"""
        try:
            # البحث عن دوال اللصق المتاحة
            paste_methods = ['paste_data', 'paste_item']
            for method_name in paste_methods:
                if hasattr(self, method_name):
                    getattr(self, method_name)()
                    print(f"تم تنفيذ {method_name} بمفتاح F4")
                    return
            
            # لصق عام
            import pyperclip
            clipboard_text = pyperclip.paste()
            if clipboard_text:
                print(f"تم لصق: {clipboard_text[:50]}")
            else:
                print("لا توجد بيانات في الحافظة")
        except Exception as e:
            print(f"خطأ في عملية اللصق: {e}")

# اختبار النافذة
if __name__ == "__main__":
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    # إنشاء نافذة الاختبار
    window = AddItemWindow(root, None)
    root.mainloop()
