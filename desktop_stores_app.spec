# -*- mode: python ; coding: utf-8 -*-

import os
import sys
from pathlib import Path

# تحديد مسار المشروع
project_root = Path.cwd()
sys.path.insert(0, str(project_root))

# تحديد الملفات والمجلدات المطلوبة
datas = []

# إضافة الملفات والمجلدات إذا كانت موجودة
if os.path.exists('ui'):
    datas.append(('ui', 'ui'))
if os.path.exists('utils'):
    datas.append(('utils', 'utils'))
if os.path.exists('assets'):
    datas.append(('assets', 'assets'))
if os.path.exists('data'):
    datas.append(('data', 'data'))
if os.path.exists('logs'):
    datas.append(('logs', 'logs'))
if os.path.exists('exports'):
    datas.append(('exports', 'exports'))
if os.path.exists('backups'):
    datas.append(('backups', 'backups'))

# إضافة الملفات الفردية
for file_pattern in ['*.db', '*.sqlite', '*.json', 'settings.json', 'requirements.txt']:
    import glob
    for file in glob.glob(file_pattern):
        datas.append((file, '.'))

# تحديد المكتبات المخفية المطلوبة - محدث 2025
hiddenimports = [
    # واجهة المستخدم الأساسية
    'tkinter',
    'tkinter.ttk',
    'tkinter.messagebox',
    'tkinter.filedialog',
    'tkinter.simpledialog',
    'tkinter.colorchooser',
    'tkinter.font',
    
    # ttkbootstrap والثيمات
    'ttkbootstrap',
    'ttkbootstrap.constants',
    'ttkbootstrap.themes',
    'ttkbootstrap.style',
    'ttkbootstrap.themes.standard',
    'ttkbootstrap.themes.dark',
    'ttkbootstrap.themes.light',
    
    # معالجة الصور
    'PIL',
    'PIL.Image',
    'PIL.ImageTk',
    'PIL.ImageDraw',
    'PIL.ImageFont',
    
    # قاعدة البيانات
    'sqlite3',
    
    # المكتبات الأساسية
    'datetime',
    'pathlib',
    'threading',
    'webbrowser',
    'subprocess',
    'typing',
    'dataclasses',
    'json',
    'csv',
    'os',
    'sys',
    're',
    'traceback',
    'logging',
    'configparser',
    'hashlib',
    'uuid',
    'base64',
    'urllib',
    'urllib.request',
    'urllib.parse',
    
    # ملفات Excel والتقارير
    'openpyxl',
    'openpyxl.styles',
    'openpyxl.utils',
    'xlsxwriter',
    'reportlab',
    'reportlab.pdfgen',
    'reportlab.lib',
    'reportlab.lib.pagesizes',
    'reportlab.lib.styles',
    'reportlab.platypus',
    
    # الرسوم البيانية
    'matplotlib',
    'matplotlib.pyplot',
    'matplotlib.backends',
    'matplotlib.backends.backend_tkagg',
    
    # معالجة البيانات
    'numpy',
    'pandas',
    'pandas.io',
    'pandas.io.excel',
    
    # الأمان والتشفير
    'bcrypt',
    'cryptography',
    'cryptography.fernet',
    'cryptography.hazmat',
    
    # الترجمة والتوطين
    'babel',
    'babel.dates',
    'babel.numbers',
    
    # أدوات إضافية
    'python-dotenv',
    'cerberus',
    'psutil',
    'fpdf2',
    'python-dateutil',
    'requests',
    
    # وحدات التطبيق
    'config',
    'database',
    'models',
    'auth_manager',
    'font_manager',
    'safe_window_manager',
    'ui',
    'ui.main_window',
    'ui.new_login_window',
    'ui.splash_screen',
    'utils',
    'utils.logger',
    'utils.backup_manager',
]

# تحليل الملف الرئيسي
a = Analysis(
    ['main.py'],
    pathex=[str(project_root)],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # ملفات الاختبار
        'test_*',
        'tests',
        'pytest',
        'unittest',
        
        # ملفات التطوير
        '__pycache__',
        '*.pyc',
        '*.pyo',
        '.git',
        '.gitignore',
        'README.md',
        'LICENSE',
        '.vscode',
        '.idea',
        
        # مكتبات غير مطلوبة
        'IPython',
        'jupyter',
        'notebook',
        'scipy',
        'sklearn',
        'tensorflow',
        'torch',
        'cv2',
        'selenium',
        
        # أدوات التطوير
        'black',
        'flake8',
        'mypy',
        'pylint',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# إزالة الملفات غير المرغوب فيها
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# إنشاء ملف تنفيذي واحد - محسن للسرعة
exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='نظام_إدارة_المخازن',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,  # تعطيل UPX لتسريع البناء
    console=False,  # إخفاء نافذة الكونسول
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/app_icon.ico' if os.path.exists('assets/app_icon.ico') else None,
    version_file=None,
)

# إنشاء مجلد التوزيع - محسن للسرعة
coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=False,  # تعطيل UPX لتسريع البناء
    name='نظام_إدارة_المخازن_مجلد',
)