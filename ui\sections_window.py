#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
شاشة إدارة الأقسام
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from typing import List, Optional
import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models import Section, Department
from database import db_manager

class SectionsWindow:
    """شاشة إدارة الأقسام"""
    
    def __init__(self, parent, main_window=None):
        self.parent = parent
        self.main_window = main_window
        self.current_section = None
        self.sections_data = []
        self.departments_data = []
        
        self.setup_window()
        self.create_widgets()
        self.load_data()
    
    def setup_window(self):
        """إعداد النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("إدارة الأقسام")
        self.window.geometry("1100x700")
        self.window.resizable(True, True)
        
        # توسيط النافذة
        self.center_window()
        
        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()
        self.window.lift()
        self.window.focus_force()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        x = (screen_width - 1100) // 2
        y = (screen_height - 700) // 2
        self.window.geometry(f"1100x700+{x}+{y}")
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.window)
        main_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)

        # العنوان
        self.create_header(main_frame)

        # نموذج الإدخال
        self.create_input_form(main_frame)

        # جدول البيانات
        self.create_data_table(main_frame)

        # شريط الحالة
        self.create_status_bar(main_frame)
    
    def create_header(self, parent):
        """إنشاء العنوان"""
        header_frame = ttk_bs.Frame(parent)
        header_frame.pack(fill=X, pady=(0, 20))

        # العنوان
        title_label = ttk_bs.Label(
            header_frame,
            text="📂 إدارة الأقسام",
            bootstyle="primary"
        )
        title_label.pack()

    def create_input_form(self, parent):
        """إنشاء نموذج الإدخال"""
        # إطار النموذج
        form_frame = ttk_bs.LabelFrame(parent, text="إضافة قسم جديد", padding=15)
        form_frame.pack(fill=X, pady=(0, 20))

        # إطار الحقول
        fields_frame = ttk_bs.Frame(form_frame)
        fields_frame.pack(fill=X)

        # متغيرات النموذج
        self.department_var = tk.StringVar()
        self.section_name_var = tk.StringVar()

        # قائمة الإدارات
        ttk_bs.Label(fields_frame, text="الإدارة:").pack(side=LEFT, padx=(0, 10))

        self.department_combo = ttk_bs.Combobox(
            fields_frame,
            textvariable=self.department_var,
            state="readonly",
            width=50
        )
        self.department_combo.pack(side=LEFT, padx=(0, 15))

        # حقل اسم القسم
        ttk_bs.Label(fields_frame, text="اسم القسم:").pack(side=LEFT, padx=(0, 10))

        name_entry = ttk_bs.Entry(
            fields_frame,
            textvariable=self.section_name_var,
            width=50
        )
        name_entry.pack(side=LEFT, padx=(0, 15))

        # زر الحفظ
        save_btn = ttk_bs.Button(
            fields_frame,
            text="💾 حفظ",
            command=self.save_section,
            bootstyle="success",
            width=12
        )
        save_btn.pack(side=LEFT, padx=(0, 10))

        # زر مسح
        clear_btn = ttk_bs.Button(
            fields_frame,
            text="🗑️ مسح",
            command=self.clear_form,
            bootstyle="secondary",
            width=12
        )
        clear_btn.pack(side=LEFT)
    
    def create_data_table(self, parent):
        """إنشاء جدول البيانات"""
        # إطار الجدول
        table_frame = ttk_bs.LabelFrame(parent, text="قائمة الأقسام", padding=10)
        table_frame.pack(fill=BOTH, expand=True, pady=(0, 10))

        # إنشاء Treeview
        columns = ("id", "department", "name", "status")
        self.tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)

        # تعيين عناوين الأعمدة
        self.tree.heading("id", text="المعرف")
        self.tree.heading("department", text="الإدارة")
        self.tree.heading("name", text="اسم القسم")
        self.tree.heading("status", text="الحالة")

        # تعيين عرض الأعمدة
        self.tree.column("id", width=100, anchor=CENTER)
        self.tree.column("department", width=200, anchor=E)
        self.tree.column("name", width=300, anchor=E)
        self.tree.column("status", width=150, anchor=CENTER)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)

        # تخطيط الجدول
        self.tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)

        # ربط الأحداث
        self.tree.bind("<<TreeviewSelect>>", self.on_item_select)
        self.tree.bind("<Button-3>", self.show_context_menu)  # النقر بالزر الأيمن
    
    def create_status_bar(self, parent):
        """إنشاء شريط الحالة"""
        self.status_bar = ttk_bs.Label(
            parent,
            text="جاهز",
            relief=SUNKEN,
            anchor=W
        )
        self.status_bar.pack(fill=X, side=BOTTOM)
    
    def load_data(self):
        """تحميل البيانات"""
        try:
            self.sections_data = Section.get_all(active_only=False)
            self.departments_data = Department.get_all(active_only=False)
            self.populate_table()
            self.populate_departments_combo()
            self.update_status(f"تم تحميل {len(self.sections_data)} قسم")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل البيانات: {e}")
            self.update_status("خطأ في تحميل البيانات")

    def populate_table(self):
        """ملء الجدول بالبيانات"""
        # مسح البيانات الموجودة
        for item in self.tree.get_children():
            self.tree.delete(item)

        # إضافة البيانات الجديدة
        for section in self.sections_data:
            # البحث عن اسم الإدارة
            department_name = ""
            if section.department_id:
                dept = next((d for d in self.departments_data if d.id == section.department_id), None)
                if dept:
                    department_name = dept.name

            status = "نشط" if section.is_active else "غير نشط"
            self.tree.insert("", "end", values=(
                section.id,
                department_name,
                section.name,
                status
            ))

    def populate_departments_combo(self):
        """ملء قائمة الإدارات المنسدلة"""
        dept_names = ["-- اختر الإدارة --"] + [dept.name for dept in self.departments_data if dept.is_active]
        self.department_combo['values'] = dept_names
        self.department_combo.set("-- اختر الإدارة --")
    
    def on_item_select(self, event):
        """معالج اختيار عنصر من الجدول"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            section_id = item['values'][0]
            self.current_section = next(
                (s for s in self.sections_data if s.id == section_id), None
            )

    def show_context_menu(self, event):
        """عرض القائمة المنبثقة"""
        # تحديد العنصر المحدد
        item = self.tree.identify_row(event.y)
        if item:
            self.tree.selection_set(item)
            self.on_item_select(event)

            # إنشاء القائمة المنبثقة
            context_menu = tk.Menu(self.window, tearoff=0)
            context_menu.add_command(label="✏️ تعديل القسم", command=self.edit_section)
            context_menu.add_command(label="🗑️ حذف القسم", command=self.delete_section)

            # عرض القائمة
            try:
                context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                context_menu.grab_release()

    def save_section(self):
        """حفظ قسم جديد"""
        department_name = self.department_var.get()
        section_name = self.section_name_var.get().strip()

        if department_name == "-- اختر الإدارة --":
            messagebox.showerror("خطأ", "يرجى اختيار الإدارة")
            return

        if not section_name:
            messagebox.showerror("خطأ", "يرجى إدخال اسم القسم")
            return

        try:
            # البحث عن معرف الإدارة
            department_id = None
            for dept in self.departments_data:
                if dept.name == department_name:
                    department_id = dept.id
                    break

            # إنشاء قسم جديد
            section = Section(name=section_name, department_id=department_id, is_active=True)

            if section.save():
                self.show_success_message("تم إضافة القسم بنجاح")
                self.clear_form()
                self.refresh_data()
            else:
                messagebox.showerror("خطأ", "فشل في إضافة القسم")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {e}")

    def clear_form(self):
        """مسح النموذج"""
        self.department_var.set("-- اختر الإدارة --")
        self.section_name_var.set("")
    
    def edit_section(self):
        """تعديل القسم المحدد"""
        if not self.current_section:
            messagebox.showwarning("تحذير", "يرجى اختيار قسم للتعديل")
            return

        # نافذة تعديل بسيطة
        dialog = SectionEditDialog(self.window, "تعديل القسم", self.current_section, self.departments_data)
        if dialog.result:
            self.current_section.name = dialog.result['name']
            self.current_section.department_id = dialog.result['department_id']
            self.current_section.is_active = dialog.result['is_active']

            if self.current_section.save():
                self.show_success_message("تم تحديث القسم بنجاح")
                self.refresh_data()
            else:
                messagebox.showerror("خطأ", "فشل في تحديث القسم")
    
    def delete_section(self):
        """حذف القسم المحدد"""
        if not self.current_section:
            messagebox.showwarning("تحذير", "يرجى اختيار قسم للحذف")
            return

        # التحقق من وجود بيانات مرتبطة
        if not self.check_section_can_be_deleted():
            return

        if messagebox.askyesno("تأكيد الحذف",
                              f"هل تريد حذف القسم '{self.current_section.name}' نهائياً؟\n"
                              "هذا الإجراء لا يمكن التراجع عنه وسيتم حذف القسم من قاعدة البيانات."):
            if self.current_section.delete():
                self.show_success_message("تم حذف القسم نهائياً بنجاح")
                self.refresh_data()
            else:
                messagebox.showerror("خطأ", "فشل في حذف القسم")

    def check_section_can_be_deleted(self):
        """التحقق من إمكانية حذف القسم"""
        try:
            section_id = self.current_section.id
            print(f"فحص إمكانية حذف القسم ID: {section_id}")

            # التحقق من وجود مستفيدين مرتبطين بالقسم
            try:
                beneficiaries_count = db_manager.fetch_one(
                    "SELECT COUNT(*) as count FROM beneficiaries WHERE section_id = ? AND is_active = 1",
                    (section_id,)
                )
                count = beneficiaries_count['count'] if beneficiaries_count else 0
                print(f"عدد المستفيدين المرتبطين: {count}")

                if count > 0:
                    messagebox.showerror(
                        "لا يمكن الحذف",
                        f"لا يمكن حذف القسم '{self.current_section.name}'\n"
                        f"يوجد {count} مستفيد مرتبط بهذا القسم\n\n"
                        "يرجى نقل أو حذف المستفيدين أولاً"
                    )
                    return False
            except Exception as e:
                print(f"خطأ في فحص المستفيدين: {e}")

            print("يمكن حذف القسم")
            return True

        except Exception as e:
            print(f"خطأ عام في التحقق من الحذف: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ أثناء التحقق من إمكانية الحذف: {e}")
            return False
    
    def refresh_data(self):
        """تحديث البيانات"""
        self.load_data()
    
    def update_status(self, message):
        """تحديث شريط الحالة"""
        self.status_bar.config(text=message)

    def show_success_message(self, message="تم بنجاح"):
        """عرض رسالة النجاح تختفي خلال 3 ثوان أو بالضغط"""
        # إنشاء نافذة رسالة مخصصة
        success_window = tk.Toplevel(self.window)
        success_window.title("✅ تم بنجاح")
        success_window.geometry("400x200")
        success_window.resizable(False, False)
        success_window.transient(self.window)
        success_window.grab_set()
        success_window.configure(bg='#d4edda')

        # توسيط النافذة
        success_window.update_idletasks()
        x = (success_window.winfo_screenwidth() - 400) // 2
        y = (success_window.winfo_screenheight() - 200) // 2
        success_window.geometry(f"400x200+{x}+{y}")

        # ربط الضغط في أي مكان لإغلاق النافذة
        def close_on_click(event=None):
            success_window.destroy()

        success_window.bind("<Button-1>", close_on_click)
        success_window.bind("<Key>", close_on_click)
        success_window.focus_set()

        # محتوى الرسالة
        frame = ttk_bs.Frame(success_window)
        frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=30)
        frame.bind("<Button-1>", close_on_click)

        # أيقونة النجاح
        success_icon = ttk_bs.Label(
            frame,
            text="✅",
            bootstyle="success"
        )
        success_icon.pack(pady=10)
        success_icon.bind("<Button-1>", close_on_click)

        # رسالة النجاح
        success_label = ttk_bs.Label(
            frame,
            text=message,
            bootstyle="success"
        )
        success_label.pack(pady=10)
        success_label.bind("<Button-1>", close_on_click)

        # رسالة إرشادية
        info_label = ttk_bs.Label(
            frame,
            text="اضغط في أي مكان للإغلاق",
            bootstyle="secondary"
        )
        info_label.pack(pady=5)
        info_label.bind("<Button-1>", close_on_click)

        # إغلاق تلقائي بعد 3 ثوان
        success_window.after(3000, close_on_click)


class SectionEditDialog:
    """نافذة حوار تعديل القسم"""

    def __init__(self, parent, title, section, departments_data):
        self.result = None
        self.section = section
        self.departments_data = departments_data

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x250")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.center_window()

        # إعداد المحتوى
        self.setup_dialog()

        # انتظار إغلاق النافذة
        self.dialog.wait_window()

    def center_window(self):
        """توسيط النافذة"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() - 500) // 2
        y = (self.dialog.winfo_screenheight() - 250) // 2
        self.dialog.geometry(f"500x250+{x}+{y}")

    def setup_dialog(self):
        """إعداد محتوى النافذة"""
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.dialog)
        main_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)

        # متغيرات النموذج
        self.department_var = tk.StringVar()
        self.name_var = tk.StringVar(value=self.section.name)
        self.is_active_var = tk.BooleanVar(value=self.section.is_active)

        # قائمة الإدارات
        ttk_bs.Label(main_frame, text="الإدارة:").pack(anchor=E, pady=(0, 5))
        dept_names = ["-- اختر الإدارة --"] + [dept.name for dept in self.departments_data if dept.is_active]
        self.dept_combo = ttk_bs.Combobox(main_frame, textvariable=self.department_var, values=dept_names, state="readonly", width=50)
        self.dept_combo.pack(fill=X, pady=(0, 15))

        # تعيين الإدارة الحالية
        if self.section.department_id:
            dept = next((d for d in self.departments_data if d.id == self.section.department_id), None)
            if dept:
                self.department_var.set(dept.name)
            else:
                self.department_var.set("-- اختر الإدارة --")
        else:
            self.department_var.set("-- اختر الإدارة --")

        # اسم القسم
        ttk_bs.Label(main_frame, text="اسم القسم:").pack(anchor=E, pady=(0, 5))
        ttk_bs.Entry(main_frame, textvariable=self.name_var, width=50).pack(fill=X, pady=(0, 15))

        # خانة اختيار الحالة
        ttk_bs.Checkbutton(
            main_frame,
            text="القسم نشط",
            variable=self.is_active_var,
            bootstyle="success"
        ).pack(anchor=E, pady=(0, 20))

        # الأزرار
        buttons_frame = ttk_bs.Frame(main_frame)
        buttons_frame.pack()

        ttk_bs.Button(
            buttons_frame,
            text="💾 حفظ",
            command=self.save_data,
            bootstyle="success",
            width=15
        ).pack(side=LEFT, padx=10)

        ttk_bs.Button(
            buttons_frame,
            text="❌ إلغاء",
            command=self.cancel,
            bootstyle="secondary",
            width=15
        ).pack(side=LEFT, padx=10)

    def save_data(self):
        """حفظ البيانات"""
        if not self.name_var.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم القسم")
            return

        if self.department_var.get() == "-- اختر الإدارة --":
            messagebox.showerror("خطأ", "يرجى اختيار الإدارة")
            return

        # البحث عن معرف الإدارة
        department_id = None
        dept_name = self.department_var.get()
        for dept in self.departments_data:
            if dept.name == dept_name:
                department_id = dept.id
                break

        self.result = {
            'name': self.name_var.get().strip(),
            'department_id': department_id,
            'is_active': self.is_active_var.get()
        }

        self.dialog.destroy()

    def cancel(self):
        """إلغاء العملية"""
        self.dialog.destroy()
