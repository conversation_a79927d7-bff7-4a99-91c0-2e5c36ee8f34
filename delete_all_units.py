#!/usr/bin/env python3
"""
حذف جميع الوحدات من قاعدة البيانات نهائياً
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from database import db_manager

def delete_all_units():
    """حذف جميع الوحدات وجميع البيانات المرتبطة بها"""
    try:
        print("🗑️ بدء عملية حذف جميع الوحدات...")
        
        # التحقق من الوحدات الموجودة أولاً
        units = db_manager.fetch_all("SELECT * FROM units")
        if not units:
            print("✅ لا توجد وحدات للحذف")
            return
        
        print(f"📋 سيتم حذف {len(units)} وحدة وجميع البيانات المرتبطة بها")
        
        # حذف البيانات المرتبطة أولاً (تسلسل الحذف مهم)
        
        # 1. حذف الأقسام المرتبطة بالإدارات التابعة للوحدات
        print("🔄 حذف الأقسام...")
        sections_deleted = db_manager.execute_query("""
            DELETE FROM sections 
            WHERE department_id IN (
                SELECT id FROM departments WHERE unit_id IN (SELECT id FROM units)
            )
        """)
        print(f"✅ تم حذف الأقسام")
        
        # 2. حذف الإدارات المرتبطة بالوحدات
        print("🔄 حذف الإدارات...")
        departments_deleted = db_manager.execute_query("""
            DELETE FROM departments WHERE unit_id IN (SELECT id FROM units)
        """)
        print(f"✅ تم حذف الإدارات")
        
        # 3. حذف المستفيدين المرتبطين بالوحدات
        print("🔄 حذف المستفيدين...")
        beneficiaries_deleted = db_manager.execute_query("""
            DELETE FROM beneficiaries WHERE unit_id IN (SELECT id FROM units)
        """)
        print(f"✅ تم حذف المستفيدين")
        
        # 4. حذف أي بيانات أخرى مرتبطة بالوحدات (إذا وجدت)
        # يمكن إضافة المزيد حسب هيكل قاعدة البيانات
        
        # 5. حذف جميع الوحدات نهائياً
        print("🔄 حذف جميع الوحدات...")
        units_deleted = db_manager.execute_query("DELETE FROM units")
        print(f"✅ تم حذف جميع الوحدات")
        
        # التحقق من النتيجة
        remaining_units = db_manager.fetch_all("SELECT * FROM units")
        if not remaining_units:
            print("\n🎉 تم حذف جميع الوحدات بنجاح!")
            print("📊 ملخص العملية:")
            print(f"   • الوحدات المحذوفة: {len(units)}")
            print("   • الأقسام المحذوفة: تم")
            print("   • الإدارات المحذوفة: تم") 
            print("   • المستفيدين المحذوفين: تم")
        else:
            print(f"⚠️ تحذير: لا تزال هناك {len(remaining_units)} وحدة موجودة")
            
    except Exception as e:
        print(f"❌ خطأ في حذف الوحدات: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("⚠️ تحذير: هذا الإجراء سيحذف جميع الوحدات وجميع البيانات المرتبطة بها نهائياً!")
    print("⚠️ لا يمكن التراجع عن هذا الإجراء!")
    
    # تأكيد إضافي
    confirm = input("\nهل أنت متأكد من المتابعة؟ اكتب 'نعم' للتأكيد: ")
    if confirm.strip().lower() in ['نعم', 'yes', 'y']:
        delete_all_units()
    else:
        print("❌ تم إلغاء العملية")