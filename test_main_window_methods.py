#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار طرق النافذة الرئيسية لفتح الشاشات
Test Main Window Methods for Opening Screens
"""

import sys
import os
import traceback

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_main_window_methods():
    """اختبار طرق النافذة الرئيسية"""
    print("🧪 اختبار طرق النافذة الرئيسية...")
    print("=" * 50)
    
    try:
        # استيراد المكتبات المطلوبة
        import tkinter as tk
        import ttkbootstrap as ttk_bs
        import ttkbootstrap.constants
        
        # إنشاء نافذة رئيسية
        root = ttk_bs.Window(themename="flatly")
        root.title("🧪 اختبار النافذة الرئيسية")
        root.geometry("800x600")
        
        # إنشاء كائن وهمي للمستخدم
        class MockUser:
            def __init__(self):
                self.username = "test_user"
                self.full_name = "مستخدم تجريبي"
                self.is_admin = True
                self.user_type = "admin"
        
        # استيراد النافذة الرئيسية
        from ui.main_window import MainWindow
        
        # إنشاء النافذة الرئيسية
        mock_user = MockUser()
        main_window = MainWindow(root, mock_user)
        
        print("✅ تم إنشاء النافذة الرئيسية بنجاح")
        
        # اختبار الطرق الثلاث
        def test_transactions():
            try:
                print("🔄 اختبار طريقة new_transaction()...")
                main_window.new_transaction()
                print("✅ نجح تشغيل new_transaction()")
                return True
            except Exception as e:
                print(f"❌ خطأ في new_transaction(): {e}")
                traceback.print_exc()
                return False
        
        def test_reports():
            try:
                print("🔄 اختبار طريقة show_reports()...")
                main_window.show_reports()
                print("✅ نجح تشغيل show_reports()")
                return True
            except Exception as e:
                print(f"❌ خطأ في show_reports(): {e}")
                traceback.print_exc()
                return False
        
        def test_inventory():
            try:
                print("🔄 اختبار طريقة show_inventory_dashboard()...")
                main_window.show_inventory_dashboard()
                print("✅ نجح تشغيل show_inventory_dashboard()")
                return True
            except Exception as e:
                print(f"❌ خطأ في show_inventory_dashboard(): {e}")
                traceback.print_exc()
                return False
        
        # إنشاء واجهة الاختبار
        title_label = ttk_bs.Label(
            root,
            text="🧪 اختبار طرق النافذة الرئيسية",
            font=("Arial", 16, "bold")
        )
        title_label.pack(pady=20)
        
        # إطار الأزرار
        buttons_frame = ttk_bs.LabelFrame(
            root,
            text="🎮 أزرار الاختبار",
            padding=20
        )
        buttons_frame.pack(pady=20, padx=20, fill="x")
        
        # أزرار الاختبار
        test_buttons = [
            ("🔄 اختبار عمليات الصرف", test_transactions, "primary"),
            ("📊 اختبار التقارير", test_reports, "info"),
            ("📦 اختبار الأصناف", test_inventory, "success")
        ]
        
        for text, command, style in test_buttons:
            btn = ttk_bs.Button(
                buttons_frame,
                text=text,
                command=command,
                bootstyle=style,
                width=25
            )
            btn.pack(pady=5)
        
        # دالة اختبار جميع الطرق
        def test_all():
            print("\n" + "="*50)
            print("🚀 بدء اختبار جميع الطرق...")
            print("="*50)
            
            results = []
            
            # اختبار عمليات الصرف
            results.append(("عمليات الصرف", test_transactions()))
            
            # اختبار التقارير
            results.append(("التقارير", test_reports()))
            
            # اختبار الأصناف
            results.append(("الأصناف", test_inventory()))
            
            # عرض النتائج
            print("\n" + "="*50)
            print("📋 نتائج الاختبار:")
            print("="*50)
            
            success_count = 0
            for name, success in results:
                status = "✅ نجح" if success else "❌ فشل"
                print(f"{status} - {name}")
                if success:
                    success_count += 1
            
            print(f"\n📊 النتيجة النهائية: {success_count}/{len(results)} طرق تعمل بنجاح")
            
            if success_count == len(results):
                print("🎉 جميع الطرق تعمل بشكل صحيح!")
                print("✅ المشكلة تم حلها!")
            else:
                print("⚠️ بعض الطرق تحتاج إلى إصلاح")
        
        # زر اختبار الكل
        test_all_btn = ttk_bs.Button(
            buttons_frame,
            text="🚀 اختبار جميع الطرق",
            command=test_all,
            bootstyle="warning",
            width=25
        )
        test_all_btn.pack(pady=15)
        
        # إطار النتائج
        results_frame = ttk_bs.LabelFrame(
            root,
            text="📋 النتائج",
            padding=10
        )
        results_frame.pack(pady=10, padx=20, fill="both", expand=True)
        
        results_text = tk.Text(
            results_frame,
            height=10,
            font=("Arial", 10),
            wrap=tk.WORD
        )
        results_text.pack(fill="both", expand=True)
        
        # إعادة توجيه المخرجات إلى النص
        import sys
        from io import StringIO
        
        class TextRedirector:
            def __init__(self, widget):
                self.widget = widget
                
            def write(self, str):
                self.widget.insert(tk.END, str)
                self.widget.see(tk.END)
                self.widget.update()
                
            def flush(self):
                pass
        
        # توجيه المخرجات
        sys.stdout = TextRedirector(results_text)
        
        print("✅ تم إنشاء نافذة الاختبار")
        print("💡 اضغط على الأزرار لاختبار طرق النافذة الرئيسية")
        
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    test_main_window_methods()
