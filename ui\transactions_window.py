#!/usr/bin/env python3
"""
شاشة عمليات الصرف
Transactions Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime, date
import threading

from config import APP_CONFIG, UI_CONFIG, get_message
from models import Transaction, Beneficiary, Item
from database import db_manager

class TransactionsWindow:
    """شاشة عمليات الصرف"""
    
    def __init__(self, parent, main_window, embedded=False):
        self.parent = parent
        self.main_window = main_window
        self.embedded = embedded
        self.transactions_window = None
        self.transactions_tree = None
        self.search_var = None
        self.status_var = None
        self.date_from_var = None
        self.date_to_var = None

        if self.embedded:
            self.setup_embedded_interface()
        else:
            self.setup_window()
        self.load_transactions()
    
    def setup_embedded_interface(self):
        """إعداد الواجهة المدمجة في الشاشة الرئيسية"""
        # مسح المحتوى الحالي
        self.main_window.clear_main_content()

        # تحديث شريط الحالة
        if hasattr(self.main_window, 'status_var'):
            self.main_window.status_var.set("عمليات الصرف")

        # إعداد المحتوى في الإطار الرئيسي
        self.setup_embedded_content()

        # تفعيل مفاتيح الاختصار العامة
        self.setup_shortcuts()

    def setup_window(self):
        """إعداد النافذة"""
        self.transactions_window = tk.Toplevel(self.parent)
        self.transactions_window.title("💳 عمليات الصرف")
        self.transactions_window.geometry("1300x750")
        self.transactions_window.resizable(True, True)

        # توسيط النافذة
        self.center_window()

        # تسجيل النافذة في مراقبة النشاط (معطل)
        # try:
        #     from activity_monitor import register_window_for_activity_monitoring
        #     register_window_for_activity_monitoring(self.transactions_window)
        # except Exception as e:
        #     print(f"تحذير: فشل في تسجيل النافذة في مراقبة النشاط: {e}")

        # إعداد المحتوى
        self.setup_content()

        # جعل النافذة في المقدمة
        self.transactions_window.lift()
        self.transactions_window.focus_force()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.transactions_window.update_idletasks()
        
        screen_width = self.transactions_window.winfo_screenwidth()
        screen_height = self.transactions_window.winfo_screenheight()
        
        window_width = 1300
        window_height = 750
        
        x = max(0, (screen_width - window_width) // 2)
        y = max(0, (screen_height - window_height) // 2)
        
        self.transactions_window.geometry(f"{window_width}x{window_height}+{x}+{y}")
    
    def setup_embedded_content(self):
        """إعداد المحتوى المدمج في الشاشة الرئيسية"""
        # شريط العنوان والأدوات
        self.create_embedded_header(self.main_window.main_frame)

        # شريط البحث والفلاتر
        self.create_embedded_filters(self.main_window.main_frame)

        # جدول العمليات
        self.create_embedded_transactions_table(self.main_window.main_frame)

        # شريط الحالة
        self.create_embedded_status_bar(self.main_window.main_frame)

    def setup_content(self):
        """إعداد محتوى النافذة"""
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.transactions_window)
        main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)

        # شريط العنوان والأدوات
        self.create_header(main_frame)

        # شريط البحث والفلاتر
        self.create_filters(main_frame)

        # جدول العمليات
        self.create_transactions_table(main_frame)

        # شريط الحالة
        self.create_status_bar(main_frame)
    
    def create_header(self, parent):
        """إنشاء شريط العنوان والأدوات"""
        header_frame = ttk_bs.Frame(parent)
        header_frame.pack(fill=X, pady=(0, 10))
        
        # العنوان
        title_label = ttk_bs.Label(
            header_frame,
            text="💳 عمليات الصرف",
            bootstyle="primary"
        )
        title_label.pack(side=LEFT)
        
        # أزرار الأدوات
        tools_frame = ttk_bs.Frame(header_frame)
        tools_frame.pack(side=RIGHT)
        
        # زر عملية جديدة
        new_btn = ttk_bs.Button(
            tools_frame,
            text="➕ عملية جديدة",
            command=self.new_transaction,
            bootstyle="success",
            width=20
        )
        new_btn.pack(side=RIGHT, padx=5)
        
        # زر تحديث
        refresh_btn = ttk_bs.Button(
            tools_frame,
            text="🔄 تحديث",
            command=self.load_transactions,
            bootstyle="outline-primary",
            width=15
        )
        refresh_btn.pack(side=RIGHT, padx=5)
        
        # زر تصدير
        export_btn = ttk_bs.Button(
            tools_frame,
            text="📤 تصدير",
            command=self.export_transactions,
            bootstyle="outline-info",
            width=15
        )
        export_btn.pack(side=RIGHT, padx=5)
    
    def create_filters(self, parent):
        """إنشاء شريط البحث والفلاتر"""
        filters_frame = ttk_bs.LabelFrame(parent, text="🔍 البحث والفلاتر", bootstyle="info")
        filters_frame.pack(fill=X, pady=(0, 10))
        
        # الصف الأول - البحث والحالة
        row1_frame = ttk_bs.Frame(filters_frame)
        row1_frame.pack(fill=X, padx=10, pady=5)
        
        # البحث
        ttk_bs.Label(row1_frame, text="البحث:").pack(side=LEFT, padx=(0, 5))
        self.search_var = tk.StringVar()
        search_entry = ttk_bs.Entry(
            row1_frame,
            textvariable=self.search_var,
            width=30
        )
        search_entry.pack(side=LEFT, padx=(0, 20))
        search_entry.bind('<KeyRelease>', lambda e: self.filter_transactions())
        
        # الصف الثاني - فلتر التاريخ
        row2_frame = ttk_bs.Frame(filters_frame)
        row2_frame.pack(fill=X, padx=10, pady=5)
        
        # من تاريخ
        ttk_bs.Label(row2_frame, text="من تاريخ:").pack(side=LEFT, padx=(0, 5))
        self.date_from_var = tk.StringVar()
        date_from_entry = ttk_bs.Entry(
            row2_frame,
            textvariable=self.date_from_var,
            width=15
        )
        date_from_entry.pack(side=LEFT, padx=(0, 20))
        date_from_entry.bind('<KeyRelease>', lambda e: self.filter_transactions())
        
        # إلى تاريخ
        ttk_bs.Label(row2_frame, text="إلى تاريخ:").pack(side=LEFT, padx=(0, 5))
        self.date_to_var = tk.StringVar()
        date_to_entry = ttk_bs.Entry(
            row2_frame,
            textvariable=self.date_to_var,
            width=15
        )
        date_to_entry.pack(side=LEFT, padx=(0, 20))
        date_to_entry.bind('<KeyRelease>', lambda e: self.filter_transactions())
        
        # زر مسح الفلاتر
        clear_btn = ttk_bs.Button(
            row2_frame,
            text="🗑️ مسح الفلاتر",
            command=self.clear_filters,
            bootstyle="outline-warning",
            width=18
        )
        clear_btn.pack(side=LEFT, padx=20)

    def create_embedded_header(self, parent):
        """إنشاء شريط العنوان والأدوات للعرض المدمج"""
        header_frame = ttk_bs.Frame(parent)
        header_frame.pack(fill=X, pady=10)

        # العنوان
        title_label = ttk_bs.Label(
            header_frame,
            text="💳 عمليات الصرف",
            bootstyle="primary"
        )
        title_label.pack(side=LEFT)

        # أزرار الأدوات
        tools_frame = ttk_bs.Frame(header_frame)
        tools_frame.pack(side=RIGHT)

        # زر عملية جديدة
        new_btn = ttk_bs.Button(
            tools_frame,
            text="➕ عملية جديدة",
            command=self.new_transaction,
            bootstyle="success",
            width=20
        )
        new_btn.pack(side=RIGHT, padx=5)

        # زر تحديث
        refresh_btn = ttk_bs.Button(
            tools_frame,
            text="🔄 تحديث",
            command=self.load_transactions,
            bootstyle="outline-primary",
            width=15
        )
        refresh_btn.pack(side=RIGHT, padx=5)

        # زر تصدير
        export_btn = ttk_bs.Button(
            tools_frame,
            text="📤 تصدير",
            command=self.export_transactions,
            bootstyle="outline-info",
            width=15
        )
        export_btn.pack(side=RIGHT, padx=5)

    def create_embedded_filters(self, parent):
        """إنشاء شريط البحث والفلاتر للعرض المدمج"""
        filters_frame = ttk_bs.LabelFrame(parent, text="🔍 البحث والفلاتر", bootstyle="info")
        filters_frame.pack(fill=X, pady=(0, 10))

        # الصف الأول - البحث
        row1_frame = ttk_bs.Frame(filters_frame)
        row1_frame.pack(fill=X, padx=10, pady=5)

        # البحث
        ttk_bs.Label(row1_frame, text="البحث:").pack(side=LEFT, padx=(0, 5))
        self.search_var = tk.StringVar()
        search_entry = ttk_bs.Entry(
            row1_frame,
            textvariable=self.search_var,
            width=30
        )
        search_entry.pack(side=LEFT, padx=(0, 20))
        search_entry.bind('<KeyRelease>', lambda e: self.filter_transactions())

        # زر مسح الفلاتر
        clear_btn = ttk_bs.Button(
            row1_frame,
            text="🗑️ مسح الفلاتر",
            command=self.clear_filters,
            bootstyle="outline-warning",
            width=18
        )
        clear_btn.pack(side=LEFT, padx=20)

    def create_embedded_transactions_table(self, parent):
        """إنشاء جدول العمليات للعرض المدمج"""
        table_frame = ttk_bs.LabelFrame(parent, text="📋 قائمة العمليات", bootstyle="primary")
        table_frame.pack(fill=BOTH, expand=True, pady=(0, 10))

        # إنشاء Treeview مع الأعمدة المطلوبة فقط
        columns = ("id", "date", "beneficiary", "total_items", "user")
        self.transactions_tree = ttk.Treeview(
            table_frame,
            columns=columns,
            show="headings",
            height=15
        )

        # تعيين عناوين الأعمدة
        headings = {
            "id": "رقم العملية",
            "date": "تاريخ العملية",
            "beneficiary": "المستفيد",
            "total_items": "عدد الأصناف",
            "user": "مدخل البيانات"
        }

        for col, heading in headings.items():
            self.transactions_tree.heading(col, text=heading)
            self.transactions_tree.column(col, width=150, anchor="center")

        # تعيين عرض أعمدة محددة
        self.transactions_tree.column("id", width=120)
        self.transactions_tree.column("date", width=150)
        self.transactions_tree.column("beneficiary", width=200)
        self.transactions_tree.column("total_items", width=120)
        self.transactions_tree.column("user", width=180)

        # شريط التمرير
        scrollbar_y = ttk.Scrollbar(table_frame, orient=VERTICAL, command=self.transactions_tree.yview)
        self.transactions_tree.configure(yscrollcommand=scrollbar_y.set)

        # تخطيط الجدول
        self.transactions_tree.pack(side=LEFT, fill=BOTH, expand=True, padx=(10, 0), pady=10)
        scrollbar_y.pack(side=RIGHT, fill=Y, pady=10)

        # ربط الأحداث
        self.transactions_tree.bind('<Double-1>', self.on_transaction_double_click)
        self.transactions_tree.bind('<Button-3>', self.show_embedded_context_menu)

    def create_embedded_status_bar(self, parent):
        """إنشاء شريط الحالة للعرض المدمج"""
        status_frame = ttk_bs.Frame(parent)
        status_frame.pack(fill=X)

        self.status_var = tk.StringVar(value="جاري تحميل البيانات...")
        status_label = ttk_bs.Label(
            status_frame,
            textvariable=self.status_var,
            bootstyle="secondary"
        )
        status_label.pack(side=LEFT)

    def create_transactions_table(self, parent):
        """إنشاء جدول العمليات"""
        table_frame = ttk_bs.LabelFrame(parent, text="📋 قائمة العمليات", bootstyle="primary")
        table_frame.pack(fill=BOTH, expand=True, pady=(0, 10))
        
        # إنشاء Treeview
        columns = ("id", "date", "beneficiary", "total_items", "status", "notes", "user", "created_at")
        self.transactions_tree = ttk.Treeview(
            table_frame,
            columns=columns,
            show="headings",
            height=15
        )
        
        # تعيين عناوين الأعمدة
        headings = {
            "id": "رقم العملية",
            "date": "تاريخ العملية",
            "beneficiary": "المستفيد",
            "total_items": "عدد الأصناف",
            "status": "الحالة",
            "notes": "ملاحظات",
            "user": "المستخدم",
            "created_at": "تاريخ الإنشاء"
        }
        
        for col, heading in headings.items():
            self.transactions_tree.heading(col, text=heading)
            self.transactions_tree.column(col, width=120, anchor="center")
        
        # تعيين عرض أعمدة محددة
        self.transactions_tree.column("id", width=100)
        self.transactions_tree.column("date", width=120)
        self.transactions_tree.column("beneficiary", width=200)
        self.transactions_tree.column("total_items", width=100)
        self.transactions_tree.column("status", width=100)
        self.transactions_tree.column("notes", width=250)
        self.transactions_tree.column("user", width=150)
        self.transactions_tree.column("created_at", width=150)
        
        # شريط التمرير
        scrollbar_y = ttk.Scrollbar(table_frame, orient=VERTICAL, command=self.transactions_tree.yview)
        scrollbar_x = ttk.Scrollbar(table_frame, orient=HORIZONTAL, command=self.transactions_tree.xview)
        self.transactions_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        # تخطيط الجدول
        self.transactions_tree.pack(side=LEFT, fill=BOTH, expand=True, padx=(10, 0), pady=10)
        scrollbar_y.pack(side=RIGHT, fill=Y, pady=10)
        scrollbar_x.pack(side=BOTTOM, fill=X, padx=10)
        
        # ربط الأحداث
        self.transactions_tree.bind('<Double-1>', self.on_transaction_double_click)
        self.transactions_tree.bind('<Button-3>', self.show_context_menu)
    
    def create_status_bar(self, parent):
        """إنشاء شريط الحالة"""
        status_frame = ttk_bs.Frame(parent)
        status_frame.pack(fill=X)
        
        self.status_var = tk.StringVar(value="جاري تحميل البيانات...")
        status_label = ttk_bs.Label(
            status_frame,
            textvariable=self.status_var,
            bootstyle="secondary"
        )
        status_label.pack(side=LEFT)
    
    def load_transactions(self):
        """تحميل عمليات الصرف"""
        try:
            # مسح البيانات الحالية
            for item in self.transactions_tree.get_children():
                self.transactions_tree.delete(item)

            self.status_var.set("جاري تحميل البيانات...")

            # التحقق من وجود جدول العمليات
            try:
                db_manager.execute_query("SELECT COUNT(*) FROM transactions")
            except:
                # إنشاء جدول العمليات إذا لم يكن موجوداً
                db_manager.execute_query("""
                    CREATE TABLE IF NOT EXISTS transactions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        transaction_number TEXT UNIQUE NOT NULL,
                        beneficiary_id INTEGER NOT NULL,
                        transaction_date DATE NOT NULL,
                        transaction_type TEXT DEFAULT 'صرف',
                        status TEXT DEFAULT 'مكتمل',
                        notes TEXT,
                        total_amount REAL DEFAULT 0,
                        user_id INTEGER,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """)

                # إنشاء جدول تفاصيل العمليات
                db_manager.execute_query("""
                    CREATE TABLE IF NOT EXISTS transaction_items (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        transaction_id INTEGER NOT NULL,
                        item_id INTEGER NOT NULL,
                        quantity REAL NOT NULL,
                        unit_price REAL DEFAULT 0,
                        total_price REAL DEFAULT 0,
                        notes TEXT,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (transaction_id) REFERENCES transactions(id) ON DELETE CASCADE
                    )
                """)

            # تحميل البيانات من قاعدة البيانات
            query = """
                SELECT t.id, t.transaction_date, b.name as beneficiary_name,
                       COUNT(ti.id) as total_items, t.status, t.notes,
                       u.full_name as user_name, t.created_at
                FROM transactions t
                LEFT JOIN beneficiaries b ON t.beneficiary_id = b.id
                LEFT JOIN transaction_items ti ON t.id = ti.transaction_id
                LEFT JOIN users u ON t.user_id = u.id
                GROUP BY t.id, t.transaction_date, b.name, t.status, t.notes, u.full_name, t.created_at
                ORDER BY t.transaction_date DESC, t.id DESC
            """

            transactions = db_manager.fetch_all(query)
            
            # إضافة البيانات للجدول
            for transaction in transactions:
                # تحويل sqlite3.Row إلى dict للوصول الآمن
                try:
                    transaction_dict = dict(transaction)
                except:
                    transaction_dict = {}

                # تنسيق التاريخ
                transaction_date = transaction_dict.get('transaction_date', '')
                if transaction_date:
                    try:
                        date_obj = datetime.fromisoformat(transaction_date)
                        formatted_date = date_obj.strftime('%Y-%m-%d')
                    except:
                        formatted_date = transaction_date
                else:
                    formatted_date = ''

                created_at = transaction_dict.get('created_at', '')
                if created_at:
                    try:
                        date_obj = datetime.fromisoformat(created_at)
                        formatted_created = date_obj.strftime('%Y-%m-%d %H:%M')
                    except:
                        formatted_created = created_at
                else:
                    formatted_created = ''

                # تنسيق الحالة
                status = transaction_dict.get('status', '')
                status_display = {
                    'pending': 'معلق',
                    'completed': 'مكتمل',
                    'cancelled': 'ملغي'
                }.get(status, status)
                
                if self.embedded:
                    # عرض مبسط للشاشة الرئيسية
                    self.transactions_tree.insert('', 'end', values=(
                        transaction_dict.get('id', ''),
                        formatted_date,
                        transaction_dict.get('beneficiary_name', ''),
                        transaction_dict.get('total_items', 0),
                        transaction_dict.get('user_name', '')
                    ))
                else:
                    # عرض كامل للنافذة المنفصلة
                    self.transactions_tree.insert('', 'end', values=(
                        transaction_dict.get('id', ''),
                        formatted_date,
                        transaction_dict.get('beneficiary_name', ''),
                        transaction_dict.get('total_items', 0),
                        status_display,
                        transaction_dict.get('notes', ''),
                        transaction_dict.get('user_name', ''),
                        formatted_created
                    ))
            
            # تحديث شريط الحالة
            count = len(transactions) if transactions else 0
            if count == 0:
                self.status_var.set("لا توجد عمليات صرف")
            else:
                self.status_var.set(f"تم تحميل {count} عملية")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل عمليات الصرف: {e}")
            self.status_var.set("خطأ في تحميل البيانات")
    
    def filter_transactions(self):
        """فلترة العمليات"""
        # هذه دالة مبسطة - يمكن تطويرها لاحقاً
        self.load_transactions()
    
    def clear_filters(self):
        """مسح جميع الفلاتر"""
        self.search_var.set("")
        self.date_from_var.set("")
        self.date_to_var.set("")
        self.load_transactions()
    
    def on_transaction_double_click(self, event):
        """معالج النقر المزدوج على عملية"""
        selection = self.transactions_tree.selection()
        if selection:
            item = self.transactions_tree.item(selection[0])
            values = item['values']
            
            # عرض تفاصيل العملية
            self.show_transaction_details(values[0])
    
    def show_transaction_details(self, transaction_id):
        """عرض تفاصيل العملية"""
        try:
            # فتح نافذة تفاصيل العملية الجديدة
            from ui.transaction_details_window import TransactionDetailsWindow
            parent_window = self.parent if self.embedded else self.transactions_window
            TransactionDetailsWindow(parent_window, transaction_id, self.main_window)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في عرض تفاصيل العملية: {e}")
    
    def show_embedded_context_menu(self, event):
        """عرض القائمة السياقية للعرض المدمج"""
        selection = self.transactions_tree.selection()
        if selection:
            context_menu = tk.Menu(self.parent, tearoff=0)
            context_menu.add_command(label="تفاصيل عملية الصرف", command=lambda: self.on_transaction_double_click(None))
            context_menu.add_command(label="تعديل عمليات الصرف", command=self.edit_transaction)

            try:
                context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                context_menu.grab_release()

    def show_context_menu(self, event):
        """عرض القائمة السياقية"""
        selection = self.transactions_tree.selection()
        if selection:
            context_menu = tk.Menu(self.transactions_window, tearoff=0)
            context_menu.add_command(label="عرض التفاصيل", command=lambda: self.on_transaction_double_click(None))
            context_menu.add_command(label="طباعة", command=self.print_transaction)
            context_menu.add_separator()
            context_menu.add_command(label="حذف", command=self.delete_transaction)

            try:
                context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                context_menu.grab_release()
    
    def new_transaction(self):
        """إنشاء عملية صرف جديدة"""
        try:
            from ui.new_transaction_window import NewTransactionWindow
            parent_window = self.parent if self.embedded else self.transactions_window
            NewTransactionWindow(parent_window, self.main_window)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح شاشة عملية صرف جديدة: {e}")

    def edit_transaction(self):
        """تعديل عملية الصرف"""
        selection = self.transactions_tree.selection()
        if selection:
            item = self.transactions_tree.item(selection[0])
            values = item['values']
            transaction_id = values[0]

            try:
                # فتح نافذة تعديل العملية الجديدة
                from ui.edit_transaction_window import EditTransactionWindow
                parent_window = self.parent if self.embedded else self.transactions_window
                EditTransactionWindow(parent_window, transaction_id, self.main_window, None)
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في فتح نافذة التعديل: {e}")
        else:
            messagebox.showwarning("تحذير", "يرجى اختيار عملية صرف للتعديل")

    def print_transaction(self):
        """طباعة العملية"""
        messagebox.showinfo("قريباً", "طباعة العملية قيد التطوير")

    def delete_transaction(self):
        """حذف العملية مع إرجاع الكميات للمخزون"""
        selection = self.transactions_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار عملية صرف للحذف")
            return

        item = self.transactions_tree.item(selection[0])
        values = item['values']
        transaction_id = values[0]

        try:
            # فتح نافذة تفاصيل العملية للحذف
            from ui.transaction_details_window import TransactionDetailsWindow
            parent_window = self.parent if self.embedded else self.transactions_window
            details_window = TransactionDetailsWindow(parent_window, transaction_id, self.main_window)

            # تنفيذ الحذف مباشرة
            details_window.delete_transaction()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حذف العملية: {e}")

    def export_transactions(self):
        """تصدير العمليات"""
        messagebox.showinfo("قريباً", "تصدير عمليات الصرف قيد التطوير")
    def setup_shortcuts(self):
        """إعداد مفاتيح الاختصار العامة"""
        try:
            # إنشاء معالج السياق
            self.context_handler = ContextHandler()
            
            # تعيين دوال العمليات (يمكن تخصيصها حسب النافذة)
            self.context_handler.set_save_callback(self.shortcut_save)
            self.context_handler.set_delete_callback(self.shortcut_delete)
            self.context_handler.set_copy_callback(self.shortcut_copy)
            self.context_handler.set_paste_callback(self.shortcut_paste)
            
            # تفعيل مفاتيح الاختصار
            window = getattr(self, 'window', None) or getattr(self, 'parent', None)
            if window:
                self.global_shortcuts = GlobalShortcuts(window, self.context_handler)
        except Exception as e:
            print(f"خطأ في إعداد مفاتيح الاختصار: {e}")
    
    def shortcut_save(self):
        """عملية الحفظ (F1)"""
        try:
            if hasattr(self, 'save_data'):
                self.save_data()
            elif hasattr(self, 'save_changes'):
                self.save_changes()
            elif hasattr(self, 'save'):
                self.save()
            else:
                print("عملية الحفظ غير متاحة")
        except Exception as e:
            print(f"خطأ في عملية الحفظ: {e}")
    
    def shortcut_delete(self):
        """عملية الحذف (F2)"""
        try:
            if hasattr(self, 'delete_selected'):
                self.delete_selected()
            elif hasattr(self, 'delete_item'):
                self.delete_item()
            elif hasattr(self, 'delete'):
                self.delete()
            else:
                print("عملية الحذف غير متاحة")
        except Exception as e:
            print(f"خطأ في عملية الحذف: {e}")
    
    def shortcut_copy(self):
        """عملية النسخ (F3)"""
        try:
            if hasattr(self, 'copy_data'):
                self.copy_data()
            else:
                # نسخ عامة للبيانات المحددة
                import pyperclip
                pyperclip.copy("تم النسخ من النافذة")
                print("تم نسخ البيانات")
        except Exception as e:
            print(f"خطأ في عملية النسخ: {e}")
    
    def shortcut_paste(self):
        """عملية اللصق (F4)"""
        try:
            if hasattr(self, 'paste_data'):
                self.paste_data()
            else:
                import pyperclip
                clipboard_text = pyperclip.paste()
                if clipboard_text:
                    print(f"تم لصق: {clipboard_text[:50]}")
                else:
                    print("لا توجد بيانات في الحافظة")
        except Exception as e:
            print(f"خطأ في عملية اللصق: {e}")

# اختبار النافذة
if __name__ == "__main__":
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    # إنشاء نافذة الاختبار
    window = TransactionsWindow(root, None)
    root.mainloop()
