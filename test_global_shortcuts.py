#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مفاتيح الاختصار العامة
Test Global Shortcuts
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ui.global_shortcuts import GlobalShortcuts, ContextHandler

class TestWindow:
    """نافذة اختبار مفاتيح الاختصار"""
    
    def __init__(self):
        self.window = tk.Tk()
        self.window.title("اختبار مفاتيح الاختصار العامة")
        self.window.geometry("800x600")
        
        # متغيرات الاختبار
        self.test_data = []
        self.selected_item = None
        
        self.setup_interface()
        self.setup_shortcuts()
    
    def setup_interface(self):
        """إعداد واجهة الاختبار"""
        # العنوان
        title_label = tk.Label(
            self.window,
            text="🔧 اختبار مفاتيح الاختصار العامة",
            font=("Arial", 16, "bold"),
            fg="blue"
        )
        title_label.pack(pady=10)
        
        # تعليمات
        instructions = tk.Label(
            self.window,
            text="استخدم مفاتيح الاختصار التالية:\nF1 - الحفظ | F2 - الحذف | F3 - النسخ | F4 - اللصق",
            font=("Arial", 12),
            justify="center"
        )
        instructions.pack(pady=10)
        
        # إطار البيانات
        data_frame = tk.LabelFrame(self.window, text="البيانات", font=("Arial", 12))
        data_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # حقل إدخال
        input_frame = tk.Frame(data_frame)
        input_frame.pack(fill="x", padx=10, pady=5)
        
        tk.Label(input_frame, text="إدخال بيانات:", font=("Arial", 10)).pack(side="left")
        self.entry_var = tk.StringVar()
        self.entry = tk.Entry(input_frame, textvariable=self.entry_var, font=("Arial", 10))
        self.entry.pack(side="left", fill="x", expand=True, padx=(10, 0))
        
        # أزرار العمليات
        buttons_frame = tk.Frame(data_frame)
        buttons_frame.pack(fill="x", padx=10, pady=5)
        
        tk.Button(
            buttons_frame,
            text="إضافة (F1)",
            command=self.add_item,
            bg="#28a745",
            fg="white",
            font=("Arial", 10)
        ).pack(side="left", padx=5)
        
        tk.Button(
            buttons_frame,
            text="حذف (F2)",
            command=self.delete_item,
            bg="#dc3545",
            fg="white",
            font=("Arial", 10)
        ).pack(side="left", padx=5)
        
        tk.Button(
            buttons_frame,
            text="نسخ (F3)",
            command=self.copy_item,
            bg="#17a2b8",
            fg="white",
            font=("Arial", 10)
        ).pack(side="left", padx=5)
        
        tk.Button(
            buttons_frame,
            text="لصق (F4)",
            command=self.paste_item,
            bg="#ffc107",
            fg="black",
            font=("Arial", 10)
        ).pack(side="left", padx=5)
        
        # قائمة البيانات
        list_frame = tk.Frame(data_frame)
        list_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        # Treeview للبيانات
        columns = ("ID", "البيانات", "الوقت")
        self.tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=150)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط العناصر
        self.tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # ربط أحداث التحديد
        self.tree.bind("<<TreeviewSelect>>", self.on_item_select)
        
        # منطقة الرسائل
        self.message_var = tk.StringVar()
        self.message_label = tk.Label(
            self.window,
            textvariable=self.message_var,
            font=("Arial", 10),
            fg="green"
        )
        self.message_label.pack(pady=5)
        
        # إضافة بعض البيانات التجريبية
        self.add_sample_data()
    
    def setup_shortcuts(self):
        """إعداد مفاتيح الاختصار"""
        # إنشاء معالج السياق
        self.context_handler = ContextHandler()
        
        # تعيين دوال العمليات
        self.context_handler.set_save_callback(self.add_item)
        self.context_handler.set_delete_callback(self.delete_item)
        self.context_handler.set_copy_callback(self.copy_item)
        self.context_handler.set_paste_callback(self.paste_item)
        
        # تفعيل مفاتيح الاختصار
        self.global_shortcuts = GlobalShortcuts(self.window, self.context_handler)
        
        self.show_message("تم تفعيل مفاتيح الاختصار: F1-F4")
    
    def add_sample_data(self):
        """إضافة بيانات تجريبية"""
        sample_items = [
            "عنصر تجريبي 1",
            "عنصر تجريبي 2", 
            "عنصر تجريبي 3"
        ]
        
        for item in sample_items:
            self.test_data.append({
                'id': len(self.test_data) + 1,
                'data': item,
                'time': self.get_current_time()
            })
        
        self.refresh_tree()
    
    def add_item(self):
        """إضافة عنصر جديد (F1)"""
        data = self.entry_var.get().strip()
        if not data:
            data = f"عنصر جديد {len(self.test_data) + 1}"
        
        new_item = {
            'id': len(self.test_data) + 1,
            'data': data,
            'time': self.get_current_time()
        }
        
        self.test_data.append(new_item)
        self.entry_var.set("")
        self.refresh_tree()
        self.show_message(f"تم إضافة: {data}")
    
    def delete_item(self):
        """حذف العنصر المحدد (F2)"""
        if not self.selected_item:
            self.show_message("لا يوجد عنصر محدد للحذف", "red")
            return
        
        # البحث عن العنصر وحذفه
        for i, item in enumerate(self.test_data):
            if item['id'] == self.selected_item['id']:
                deleted_data = item['data']
                del self.test_data[i]
                break
        
        self.selected_item = None
        self.refresh_tree()
        self.show_message(f"تم حذف: {deleted_data}")
    
    def copy_item(self):
        """نسخ العنصر المحدد (F3)"""
        if not self.selected_item:
            self.show_message("لا يوجد عنصر محدد للنسخ", "red")
            return
        
        try:
            import pyperclip
            copy_text = f"ID: {self.selected_item['id']}\nالبيانات: {self.selected_item['data']}\nالوقت: {self.selected_item['time']}"
            pyperclip.copy(copy_text)
            self.show_message(f"تم نسخ: {self.selected_item['data']}")
        except ImportError:
            self.show_message("مكتبة pyperclip غير متاحة", "red")
    
    def paste_item(self):
        """لصق البيانات (F4)"""
        try:
            import pyperclip
            clipboard_text = pyperclip.paste()
            
            if clipboard_text:
                # استخدام النص المنسوخ كبيانات جديدة
                self.entry_var.set(clipboard_text[:50])  # أول 50 حرف
                self.show_message(f"تم لصق البيانات في حقل الإدخال")
            else:
                self.show_message("لا توجد بيانات في الحافظة", "red")
        except ImportError:
            self.show_message("مكتبة pyperclip غير متاحة", "red")
    
    def on_item_select(self, event):
        """عند تحديد عنصر في القائمة"""
        selection = self.tree.selection()
        if selection:
            item_id = self.tree.item(selection[0])['values'][0]
            # البحث عن العنصر في البيانات
            for item in self.test_data:
                if item['id'] == item_id:
                    self.selected_item = item
                    break
            self.show_message(f"تم تحديد: {self.selected_item['data']}")
    
    def refresh_tree(self):
        """تحديث عرض البيانات"""
        # مسح البيانات الحالية
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # إضافة البيانات الجديدة
        for item in self.test_data:
            self.tree.insert("", "end", values=(item['id'], item['data'], item['time']))
    
    def show_message(self, message, color="green"):
        """عرض رسالة"""
        self.message_var.set(message)
        self.message_label.config(fg=color)
        
        # إخفاء الرسالة بعد 3 ثوانٍ
        self.window.after(3000, lambda: self.message_var.set(""))
    
    def get_current_time(self):
        """الحصول على الوقت الحالي"""
        from datetime import datetime
        return datetime.now().strftime("%H:%M:%S")
    
    def run(self):
        """تشغيل النافذة"""
        self.window.mainloop()

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار مفاتيح الاختصار العامة")
    
    # إنشاء وتشغيل نافذة الاختبار
    test_window = TestWindow()
    test_window.run()

if __name__ == "__main__":
    main()
