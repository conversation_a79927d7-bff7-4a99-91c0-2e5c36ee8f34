#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار محاكاة مشكلة الأرقام العشرية عند النقل بين الأجهزة
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models import Beneficiary
from database import db_manager

def simulate_decimal_issue():
    """محاكاة مشكلة الأرقام العشرية"""
    print("🔍 محاكاة مشكلة الأرقام العشرية...")
    
    # إنشاء مستفيد جديد بأرقام مختلفة
    test_beneficiary = Beneficiary()
    test_beneficiary.name = "مستفيد تجريبي"
    test_beneficiary.number = "123456.0"  # محاكاة رقم عشري
    test_beneficiary.is_active = True
    test_beneficiary.data_entry_user_id = 1
    
    print(f"📝 إنشاء مستفيد برقم: {test_beneficiary.number}")
    
    # حفظ المستفيد
    if test_beneficiary.save():
        print("✅ تم حفظ المستفيد بنجاح")
        
        # جلب المستفيد من قاعدة البيانات
        saved_beneficiary = Beneficiary.get_by_id(test_beneficiary.id)
        if saved_beneficiary:
            print(f"📊 الرقم المحفوظ: {saved_beneficiary.number}")
            print(f"📊 نوع البيانات: {type(saved_beneficiary.number)}")
            
            # اختبار العرض في الجدول (محاكاة)
            print("\n🖥️ محاكاة عرض في الجدول:")
            display_number = saved_beneficiary.number or ""
            print(f"العرض الأصلي: {display_number}")
            
            # تطبيق التنظيف
            if saved_beneficiary.number:
                try:
                    if '.' in str(saved_beneficiary.number):
                        clean_number = str(int(float(saved_beneficiary.number)))
                        print(f"العرض المنظف: {clean_number}")
                        print("✅ تم إصلاح المشكلة!")
                    else:
                        print(f"العرض: {str(saved_beneficiary.number)}")
                        print("✅ لا توجد مشكلة")
                except (ValueError, TypeError):
                    print(f"العرض: {str(saved_beneficiary.number)}")
                    print("⚠️ خطأ في التحويل")
        
        # حذف المستفيد التجريبي
        saved_beneficiary.delete()
        print("\n🗑️ تم حذف المستفيد التجريبي")
    else:
        print("❌ فشل في حفظ المستفيد")

def test_various_number_formats():
    """اختبار تنسيقات مختلفة للأرقام"""
    print("\n🧪 اختبار تنسيقات مختلفة للأرقام...")
    
    test_numbers = [
        "123456",      # رقم عادي
        "123456.0",    # رقم بنقطة عشرية
        "123456.00",   # رقم بنقطتين عشريتين
        "789.5",       # رقم بجزء عشري
        "999.999",     # رقم بأجزاء عشرية متعددة
    ]
    
    for number in test_numbers:
        print(f"\n📋 اختبار الرقم: {number}")
        
        # إنشاء مستفيد مؤقت
        temp_beneficiary = Beneficiary()
        temp_beneficiary.name = f"اختبار {number}"
        temp_beneficiary.number = number
        temp_beneficiary.is_active = True
        temp_beneficiary.data_entry_user_id = 1
        
        if temp_beneficiary.save():
            # جلب من قاعدة البيانات
            saved = Beneficiary.get_by_id(temp_beneficiary.id)
            if saved:
                print(f"  المحفوظ: {saved.number}")
                
                # تطبيق التنظيف
                cleaned = saved._clean_number(saved.number)
                print(f"  المنظف: {cleaned}")
                
                # حذف المؤقت
                saved.delete()
            
def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار مشكلة الأرقام العشرية")
    print("=" * 50)
    
    # محاكاة المشكلة
    simulate_decimal_issue()
    
    # اختبار تنسيقات مختلفة
    test_various_number_formats()
    
    print("\n✅ انتهى الاختبار")

if __name__ == "__main__":
    main()
