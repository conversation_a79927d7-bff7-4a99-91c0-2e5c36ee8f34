@echo off
chcp 65001 >nul
echo 🧹 تنظيف سريع للمشروع
echo ========================

echo.
echo 🔍 فحص المجلدات الكبيرة...

if exist ".venv" (
    echo 📁 وجد مجلد .venv - سيتم حذفه
    rmdir /s /q ".venv"
    echo ✅ تم حذف .venv
)

if exist "build" (
    echo 📁 وجد مجلد build - سيتم حذفه
    rmdir /s /q "build"
    echo ✅ تم حذف build
)

if exist "dist" (
    echo 📁 وجد مجلد dist - سيتم حذفه
    rmdir /s /q "dist"
    echo ✅ تم حذف dist
)

echo.
echo 🧹 حذف ملفات Python المؤقتة...

for /r %%i in (__pycache__) do (
    if exist "%%i" (
        rmdir /s /q "%%i"
        echo ✅ تم حذف %%i
    )
)

for /r %%i in (*.pyc) do (
    if exist "%%i" (
        del "%%i"
        echo ✅ تم حذف %%i
    )
)

echo.
echo 🎉 تم التنظيف السريع بنجاح!
echo 🚀 البرنامج يجب أن يكون أسرع الآن
echo.
pause