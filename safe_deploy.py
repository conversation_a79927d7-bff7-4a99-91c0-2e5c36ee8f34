#!/usr/bin/env python3
"""
نشر آمن للبرنامج
Safe Deploy Script
"""

import os
import sys
import shutil
import time
from pathlib import Path
from datetime import datetime

def print_step(message):
    """طباعة خطوة"""
    print(f"\n🔄 {message}")
    print("=" * 50)

def print_success(message):
    """طباعة نجاح"""
    print(f"✅ {message}")

def print_error(message):
    """طباعة خطأ"""
    print(f"❌ {message}")

def print_info(message):
    """طباعة معلومات"""
    print(f"ℹ️ {message}")

def print_warning(message):
    """طباعة تحذير"""
    print(f"⚠️ {message}")

def force_remove_file(file_path):
    """إزالة ملف بالقوة"""
    try:
        if file_path.exists():
            # محاولة تغيير صلاحيات الملف
            os.chmod(file_path, 0o777)
            file_path.unlink()
            return True
    except Exception as e:
        print_warning(f"تعذر مسح {file_path.name}: {e}")
        return False
    return True

def force_remove_directory(dir_path):
    """إزالة مجلد بالقوة"""
    try:
        if dir_path.exists():
            # محاولة تغيير صلاحيات جميع الملفات
            for root, dirs, files in os.walk(dir_path):
                for file in files:
                    file_path = Path(root) / file
                    try:
                        os.chmod(file_path, 0o777)
                    except:
                        pass
                for dir in dirs:
                    dir_path_inner = Path(root) / dir
                    try:
                        os.chmod(dir_path_inner, 0o777)
                    except:
                        pass
            
            # محاولة مسح المجلد
            shutil.rmtree(dir_path)
            return True
    except Exception as e:
        print_warning(f"تعذر مسح المجلد {dir_path.name}: {e}")
        return False
    return True

def main():
    """نشر البرنامج بطريقة آمنة"""
    print("🚀 نشر آمن لنظام إدارة المخازن")
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # المسارات
    source_path = Path("dist/نظام_إدارة_المخازن_مجلد")
    target_base = Path("E:/desktop_stores_app/dist")
    target_path = target_base / "نظام_إدارة_المخازن_مجلد"
    
    print_step("التحقق من الملفات المصدر")
    
    # التحقق من وجود الملف المصدر
    if not source_path.exists():
        print_error("المجلد المصدر غير موجود!")
        print_info("يجب تشغيل البناء أولاً")
        return False
    
    exe_file = source_path / "نظام_إدارة_المخازن.exe"
    if not exe_file.exists():
        print_error("الملف التنفيذي غير موجود!")
        return False
    
    file_size = exe_file.stat().st_size / (1024 * 1024)
    print_success(f"الملف المصدر موجود - الحجم: {file_size:.2f} MB")
    
    print_step("إنشاء المجلد الهدف")
    
    # إنشاء المجلد الهدف إذا لم يكن موجود
    try:
        target_base.mkdir(parents=True, exist_ok=True)
        print_success("المجلد الهدف جاهز")
    except Exception as e:
        print_error(f"فشل في إنشاء المجلد الهدف: {e}")
        return False
    
    print_step("إنشاء نسخة احتياطية")
    
    # إنشاء نسخة احتياطية إذا كان المجلد الهدف موجود
    if target_path.exists():
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = target_base / f"backup_{timestamp}"
        
        try:
            print_info(f"نسخ النسخة الحالية إلى: {backup_path}")
            shutil.copytree(target_path, backup_path)
            print_success("تم إنشاء النسخة الاحتياطية")
        except Exception as e:
            print_warning(f"تعذر إنشاء النسخة الاحتياطية: {e}")
            print_info("سيتم المتابعة بدون نسخة احتياطية")
    else:
        print_info("لا توجد نسخة سابقة")
    
    print_step("مسح النسخة القديمة بطريقة آمنة")
    
    # مسح النسخة القديمة بطريقة آمنة
    if target_path.exists():
        print_info("محاولة مسح النسخة القديمة...")
        
        # محاولة إنهاء أي عمليات قد تستخدم الملفات
        print_info("انتظار لإنهاء العمليات المحتملة...")
        time.sleep(2)
        
        # مسح بالقوة
        if force_remove_directory(target_path):
            print_success("تم مسح النسخة القديمة")
        else:
            # إذا فشل المسح، جرب إعادة تسمية المجلد
            try:
                old_name = target_path.parent / f"old_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                target_path.rename(old_name)
                print_warning(f"تم إعادة تسمية المجلد القديم إلى: {old_name.name}")
                print_info("يمكنك مسحه يدوياً لاحقاً")
            except Exception as e:
                print_error(f"فشل في التعامل مع النسخة القديمة: {e}")
                print_info("سيتم إنشاء مجلد جديد باسم مختلف")
                target_path = target_base / f"نظام_إدارة_المخازن_مجلد_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    print_step("نسخ النسخة الجديدة")
    
    # نسخ النسخة الجديدة
    try:
        print_info(f"نسخ من: {source_path}")
        print_info(f"إلى: {target_path}")
        
        shutil.copytree(source_path, target_path)
        print_success("تم نسخ النسخة الجديدة بنجاح")
        
        # التحقق من النسخ
        new_exe = target_path / "نظام_إدارة_المخازن.exe"
        if new_exe.exists():
            new_size = new_exe.stat().st_size / (1024 * 1024)
            print_success(f"الملف التنفيذي الجديد: {new_size:.2f} MB")
        
    except Exception as e:
        print_error(f"فشل في نسخ النسخة الجديدة: {e}")
        return False
    
    print_step("تنظيف النسخ الاحتياطية القديمة")
    
    # الاحتفاظ بآخر 3 نسخ احتياطية فقط
    try:
        backup_folders = []
        old_folders = []
        
        for item in target_base.iterdir():
            if item.is_dir():
                if item.name.startswith("backup_"):
                    backup_folders.append(item)
                elif item.name.startswith("old_"):
                    old_folders.append(item)
        
        # ترتيب حسب التاريخ (الأحدث أولاً)
        backup_folders.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        old_folders.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        
        # مسح النسخ الزائدة
        folders_to_remove = backup_folders[3:] + old_folders[2:]  # الاحتفاظ بآخر 3 نسخ احتياطية و 2 مجلدات قديمة
        
        for old_folder in folders_to_remove:
            if force_remove_directory(old_folder):
                print_info(f"تم مسح: {old_folder.name}")
            else:
                print_warning(f"تعذر مسح: {old_folder.name}")
        
        if not folders_to_remove:
            print_info("لا توجد مجلدات زائدة للمسح")
        else:
            print_success(f"تم تنظيف {len(folders_to_remove)} مجلد قديم")
            
    except Exception as e:
        print_warning(f"خطأ في تنظيف المجلدات القديمة: {e}")
    
    print_step("ملخص العملية")
    
    # طباعة الملخص
    print_success("تم نشر البرنامج بنجاح!")
    print(f"📁 المسار النهائي: {target_path}")
    print(f"📏 حجم البرنامج: {file_size:.2f} MB")
    print(f"🕒 تاريخ النشر: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # عرض محتويات المجلد الهدف
    print(f"\n📋 محتويات المجلد:")
    try:
        for item in target_path.iterdir():
            if item.is_file():
                size = item.stat().st_size / (1024 * 1024)
                print(f"   📄 {item.name} ({size:.2f} MB)")
            else:
                print(f"   📁 {item.name}/")
    except Exception as e:
        print_error(f"فشل في عرض المحتويات: {e}")
    
    print(f"\n🎯 البرنامج جاهز للاستخدام من المسار:")
    print(f"   {target_path / 'نظام_إدارة_المخازن.exe'}")
    
    # عرض جميع المجلدات في المسار الهدف
    print(f"\n📂 جميع المجلدات في {target_base}:")
    try:
        for item in target_base.iterdir():
            if item.is_dir():
                print(f"   📁 {item.name}")
    except Exception as e:
        print_error(f"فشل في عرض المجلدات: {e}")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        input(f"\n{'✅ نجح النشر' if success else '❌ فشل النشر'} - اضغط Enter للخروج...")
    except KeyboardInterrupt:
        print("\n⚠️ تم إلغاء العملية")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")