#!/usr/bin/env python3
"""
تنظيف النسخ الاحتياطية القديمة
Cleanup Old Backups
"""

import shutil
from pathlib import Path
from datetime import datetime

def main():
    """تنظيف النسخ الاحتياطية القديمة"""
    print("🧹 تنظيف النسخ الاحتياطية القديمة")
    print("=" * 50)
    
    dist_path = Path("E:/desktop_stores_app/dist")
    
    if not dist_path.exists():
        print("❌ مجلد dist غير موجود")
        return False
    
    # البحث عن النسخ الاحتياطية
    backup_folders = []
    for item in dist_path.iterdir():
        if item.is_dir() and item.name.startswith("backup_"):
            backup_folders.append(item)
    
    if not backup_folders:
        print("ℹ️ لا توجد نسخ احتياطية للتنظيف")
        return True
    
    print(f"📊 تم العثور على {len(backup_folders)} نسخة احتياطية")
    
    # ترتيب حسب التاريخ (الأحدث أولاً)
    backup_folders.sort(key=lambda x: x.stat().st_mtime, reverse=True)
    
    # عرض النسخ الاحتياطية
    print("\n📋 النسخ الاحتياطية الموجودة:")
    for i, folder in enumerate(backup_folders):
        mod_time = datetime.fromtimestamp(folder.stat().st_mtime)
        status = "✅ سيتم الاحتفاظ بها" if i < 1 else "🗑️ سيتم مسحها"
        print(f"   {i+1}. {folder.name} - {mod_time.strftime('%Y-%m-%d %H:%M:%S')} - {status}")
    
    # مسح النسخ الزائدة (الاحتفاظ بآخر نسخة واحدة فقط)
    folders_to_remove = backup_folders[1:]
    
    if not folders_to_remove:
        print("\n✅ لا توجد نسخ زائدة للمسح")
        return True
    
    print(f"\n🗑️ سيتم مسح {len(folders_to_remove)} نسخة احتياطية قديمة...")
    
    removed_count = 0
    for folder in folders_to_remove:
        try:
            shutil.rmtree(folder)
            print(f"✅ تم مسح: {folder.name}")
            removed_count += 1
        except Exception as e:
            print(f"❌ فشل في مسح {folder.name}: {e}")
    
    print(f"\n🎉 تم مسح {removed_count} من {len(folders_to_remove)} نسخة احتياطية")
    
    # عرض الوضع النهائي
    print(f"\n📂 الوضع النهائي في {dist_path}:")
    for item in dist_path.iterdir():
        if item.is_dir():
            print(f"   📁 {item.name}")
        else:
            size = item.stat().st_size / (1024 * 1024)
            print(f"   📄 {item.name} ({size:.2f} MB)")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        input(f"\n{'✅ تم التنظيف' if success else '❌ فشل التنظيف'} - اضغط Enter للخروج...")
    except KeyboardInterrupt:
        print("\n⚠️ تم إلغاء العملية")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")