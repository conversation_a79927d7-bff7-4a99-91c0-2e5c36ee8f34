#!/usr/bin/env python3
"""
بناء سريع للبرنامج
Quick Build Script
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
from datetime import datetime

def main():
    """بناء سريع للبرنامج"""
    print("🚀 بناء سريع لنظام إدارة المخازن")
    print("=" * 50)
    
    # 1. مسح النسخ القديمة
    print("🗑️ مسح النسخ القديمة...")
    for folder in ["dist", "build"]:
        if Path(folder).exists():
            shutil.rmtree(folder)
            print(f"✅ تم مسح {folder}")
    
    # 2. تحديث التاريخ في main.py
    print("📅 تحديث التاريخ...")
    main_file = Path("main.py")
    if main_file.exists():
        content = main_file.read_text(encoding='utf-8')
        current_date = datetime.now().strftime("%Y-%m-%d")
        content = content.replace(
            "تاريخ التحديث: 2025-06-15",
            f"تاريخ التحديث: {current_date}"
        )
        main_file.write_text(content, encoding='utf-8')
        print(f"✅ تم تحديث التاريخ إلى: {current_date}")
    
    # 3. بناء البرنامج
    print("🔨 بناء البرنامج...")
    try:
        result = subprocess.run([
            'pyinstaller', 
            '--clean',
            '--noconfirm',
            'desktop_stores_app.spec'
        ], check=True, capture_output=True, text=True)
        
        print("✅ تم بناء البرنامج بنجاح!")
        
        # التحقق من النتيجة
        exe_path = Path("dist/نظام_إدارة_المخازن_مجلد/نظام_إدارة_المخازن.exe")
        if exe_path.exists():
            file_size = exe_path.stat().st_size / (1024 * 1024)
            print(f"📁 المسار: {exe_path}")
            print(f"📏 الحجم: {file_size:.2f} MB")
            print("🎯 البرنامج جاهز للاستخدام!")
        else:
            print("⚠️ الملف التنفيذي غير موجود في المسار المتوقع")
            
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في بناء البرنامج: {e}")
        if e.stderr:
            print(f"تفاصيل الخطأ: {e.stderr}")
        return False
    
    # 4. تنظيف الملفات المؤقتة
    print("🧹 تنظيف الملفات المؤقتة...")
    if Path("build").exists():
        shutil.rmtree("build")
        print("✅ تم مسح مجلد build")
    
    print("\n🎉 تم الانتهاء من البناء السريع!")
    return True

if __name__ == "__main__":
    try:
        success = main()
        input(f"\n{'✅ نجح' if success else '❌ فشل'} - اضغط Enter للخروج...")
    except KeyboardInterrupt:
        print("\n⚠️ تم إلغاء العملية")
    except Exception as e:
        print(f"\n❌ خطأ: {e}")
        input("اضغط Enter للخروج...")