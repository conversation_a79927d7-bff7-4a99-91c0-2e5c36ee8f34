#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة عرض الأرقام العامة كأرقام عشرية
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models import Beneficiary
from database import db_manager

def test_number_cleaning():
    """اختبار تنظيف الأرقام العامة"""
    print("🧪 اختبار تنظيف الأرقام العامة...")
    
    # اختبار الدالة المساعدة
    beneficiary = Beneficiary()
    
    # اختبار أرقام مختلفة
    test_cases = [
        ("123", "123"),
        ("123.0", "123"),
        ("456.00", "456"),
        ("789.5", "789"),
        ("abc", "abc"),
        ("", None),
        (None, None),
        (123, "123"),
        (123.0, "123"),
        (456.789, "456")
    ]
    
    print("\n📋 نتائج الاختبار:")
    for input_val, expected in test_cases:
        result = beneficiary._clean_number(input_val)
        status = "✅" if result == expected else "❌"
        print(f"{status} المدخل: {input_val} -> النتيجة: {result} (المتوقع: {expected})")

def test_database_numbers():
    """اختبار الأرقام في قاعدة البيانات"""
    print("\n🗄️ اختبار الأرقام في قاعدة البيانات...")
    
    try:
        # جلب عينة من المستفيدين
        beneficiaries = Beneficiary.get_all()
        
        print(f"\n📊 تم العثور على {len(beneficiaries)} مستفيد")
        
        # عرض أول 5 مستفيدين مع أرقامهم
        for i, beneficiary in enumerate(beneficiaries[:5]):
            if beneficiary.number:
                print(f"المستفيد {i+1}: {beneficiary.name}")
                print(f"  الرقم الأصلي: {beneficiary.number} (نوع: {type(beneficiary.number)})")
                
                # اختبار التنظيف
                cleaned = beneficiary._clean_number(beneficiary.number)
                print(f"  الرقم المنظف: {cleaned}")
                
                # فحص وجود نقطة عشرية
                if '.' in str(beneficiary.number):
                    print(f"  ⚠️ يحتوي على نقطة عشرية!")
                else:
                    print(f"  ✅ لا يحتوي على نقطة عشرية")
                print()
                
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")

def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار إصلاح مشكلة الأرقام العشرية")
    print("=" * 50)
    
    # اختبار تنظيف الأرقام
    test_number_cleaning()
    
    # اختبار قاعدة البيانات
    test_database_numbers()
    
    print("\n✅ انتهى الاختبار")

if __name__ == "__main__":
    main()
