#!/usr/bin/env python3
"""
سكريبت تحديث وبناء البرنامج
Update and Build Script
"""

import os
import sys
import shutil
import subprocess
import time
from pathlib import Path
from datetime import datetime

def print_step(step_name, description=""):
    """طباعة خطوة مع تنسيق جميل"""
    print(f"\n{'='*60}")
    print(f"🔄 {step_name}")
    if description:
        print(f"📝 {description}")
    print(f"{'='*60}")

def print_success(message):
    """طباعة رسالة نجاح"""
    print(f"✅ {message}")

def print_error(message):
    """طباعة رسالة خطأ"""
    print(f"❌ {message}")

def print_warning(message):
    """طباعة رسالة تحذير"""
    print(f"⚠️ {message}")

def print_info(message):
    """طباعة رسالة معلومات"""
    print(f"ℹ️ {message}")

def check_requirements():
    """التحقق من المتطلبات"""
    print_step("التحقق من المتطلبات", "فحص البيئة والأدوات المطلوبة")
    
    # التحقق من Python
    python_version = sys.version_info
    if python_version < (3, 8):
        print_error(f"يتطلب Python 3.8 أو أحدث. الإصدار الحالي: {python_version}")
        return False
    print_success(f"Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # التحقق من PyInstaller
    try:
        result = subprocess.run(['pyinstaller', '--version'], 
                              capture_output=True, text=True, check=True)
        print_success(f"PyInstaller {result.stdout.strip()}")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print_warning("PyInstaller غير مثبت. سيتم تثبيته...")
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyinstaller'], 
                         check=True)
            print_success("تم تثبيت PyInstaller بنجاح")
        except subprocess.CalledProcessError:
            print_error("فشل في تثبيت PyInstaller")
            return False
    
    # التحقق من الملفات الأساسية
    required_files = ['main.py', 'desktop_stores_app.spec']
    for file in required_files:
        if not Path(file).exists():
            print_error(f"الملف المطلوب غير موجود: {file}")
            return False
        print_success(f"الملف موجود: {file}")
    
    return True

def backup_old_version():
    """إنشاء نسخة احتياطية من النسخة القديمة"""
    print_step("إنشاء نسخة احتياطية", "حفظ النسخة القديمة قبل التحديث")
    
    dist_path = Path("dist")
    if not dist_path.exists():
        print_info("لا توجد نسخة سابقة للنسخ الاحتياطي")
        return True
    
    # إنشاء مجلد النسخ الاحتياطية
    backup_dir = Path("backups_builds")
    backup_dir.mkdir(exist_ok=True)
    
    # إنشاء اسم النسخة الاحتياطية مع التاريخ والوقت
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_name = f"backup_build_{timestamp}"
    backup_path = backup_dir / backup_name
    
    try:
        print_info(f"نسخ المجلد dist إلى {backup_path}")
        shutil.copytree(dist_path, backup_path)
        print_success(f"تم إنشاء النسخة الاحتياطية: {backup_path}")
        return True
    except Exception as e:
        print_error(f"فشل في إنشاء النسخة الاحتياطية: {e}")
        return False

def clean_old_builds():
    """مسح النسخ القديمة"""
    print_step("تنظيف النسخ القديمة", "مسح المجلدات والملفات القديمة")
    
    # قائمة المجلدات والملفات المراد مسحها
    items_to_clean = [
        "dist",
        "build", 
        "__pycache__",
        "*.pyc",
        "*.pyo"
    ]
    
    for item in items_to_clean:
        if "*" in item:
            # مسح الملفات بنمط معين
            import glob
            files = glob.glob(f"**/{item}", recursive=True)
            for file in files:
                try:
                    os.remove(file)
                    print_info(f"تم مسح الملف: {file}")
                except Exception as e:
                    print_warning(f"تعذر مسح الملف {file}: {e}")
        else:
            # مسح المجلدات
            item_path = Path(item)
            if item_path.exists():
                try:
                    if item_path.is_dir():
                        shutil.rmtree(item_path)
                        print_success(f"تم مسح المجلد: {item}")
                    else:
                        item_path.unlink()
                        print_success(f"تم مسح الملف: {item}")
                except Exception as e:
                    print_error(f"فشل في مسح {item}: {e}")
                    return False
            else:
                print_info(f"العنصر غير موجود: {item}")
    
    return True

def install_dependencies():
    """تثبيت المكتبات المطلوبة"""
    print_step("تثبيت المكتبات", "تحديث وتثبيت جميع المكتبات المطلوبة")
    
    # قائمة المكتبات الأساسية
    required_packages = [
        'ttkbootstrap',
        'pillow',
        'bcrypt',
        'reportlab',
        'openpyxl',
        'pandas',
        'matplotlib',
        'requests',
        'python-dateutil',
        'psutil',
        'fpdf2',
        'xlsxwriter'
    ]
    
    print_info("تحديث pip...")
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'], 
                      check=True, capture_output=True)
        print_success("تم تحديث pip")
    except subprocess.CalledProcessError as e:
        print_warning(f"تعذر تحديث pip: {e}")
    
    # تثبيت المكتبات
    for package in required_packages:
        try:
            print_info(f"تثبيت {package}...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', '--upgrade', package], 
                          check=True, capture_output=True)
            print_success(f"تم تثبيت {package}")
        except subprocess.CalledProcessError as e:
            print_error(f"فشل في تثبيت {package}: {e}")
            return False
    
    return True

def update_version_info():
    """تحديث معلومات الإصدار"""
    print_step("تحديث معلومات الإصدار", "تحديث رقم الإصدار والتاريخ")
    
    try:
        # قراءة ملف main.py
        main_file = Path("main.py")
        if main_file.exists():
            content = main_file.read_text(encoding='utf-8')
            
            # تحديث التاريخ
            current_date = datetime.now().strftime("%Y-%m-%d")
            content = content.replace(
                "تاريخ التحديث: 2025-06-15",
                f"تاريخ التحديث: {current_date}"
            )
            
            # كتابة الملف المحدث
            main_file.write_text(content, encoding='utf-8')
            print_success(f"تم تحديث التاريخ إلى: {current_date}")
        
        return True
    except Exception as e:
        print_error(f"فشل في تحديث معلومات الإصدار: {e}")
        return False

def build_executable():
    """بناء الملف التنفيذي"""
    print_step("بناء الملف التنفيذي", "إنشاء ملف exe من الكود المصدري")
    
    try:
        # تشغيل PyInstaller
        print_info("بدء عملية البناء...")
        print_info("هذا قد يستغرق عدة دقائق...")
        
        result = subprocess.run([
            'pyinstaller', 
            '--clean',  # تنظيف الملفات المؤقتة
            '--noconfirm',  # عدم طلب التأكيد
            'desktop_stores_app.spec'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print_success("تم بناء الملف التنفيذي بنجاح!")
            
            # التحقق من وجود الملفات المبنية
            exe_path = Path("dist/نظام_إدارة_المخازن_مجلد/نظام_إدارة_المخازن.exe")
            if exe_path.exists():
                file_size = exe_path.stat().st_size / (1024 * 1024)  # بالميجابايت
                print_success(f"الملف التنفيذي: {exe_path}")
                print_success(f"حجم الملف: {file_size:.2f} MB")
            else:
                print_warning("الملف التنفيذي غير موجود في المسار المتوقع")
            
            return True
        else:
            print_error("فشل في بناء الملف التنفيذي")
            print_error(f"رسالة الخطأ: {result.stderr}")
            return False
            
    except Exception as e:
        print_error(f"خطأ في عملية البناء: {e}")
        return False

def test_executable():
    """اختبار الملف التنفيذي"""
    print_step("اختبار الملف التنفيذي", "التحقق من عمل البرنامج")
    
    exe_path = Path("dist/نظام_إدارة_المخازن_مجلد/نظام_إدارة_المخازن.exe")
    
    if not exe_path.exists():
        print_error("الملف التنفيذي غير موجود")
        return False
    
    try:
        print_info("تشغيل اختبار سريع للملف التنفيذي...")
        
        # تشغيل البرنامج لمدة قصيرة للتأكد من عمله
        process = subprocess.Popen([str(exe_path)], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE)
        
        # انتظار لثانيتين ثم إنهاء العملية
        time.sleep(2)
        process.terminate()
        
        print_success("الملف التنفيذي يعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print_error(f"فشل في اختبار الملف التنفيذي: {e}")
        return False

def create_shortcut():
    """إنشاء اختصار على سطح المكتب"""
    print_step("إنشاء اختصار", "إنشاء اختصار على سطح المكتب")
    
    try:
        import winshell
        from win32com.client import Dispatch
        
        desktop = winshell.desktop()
        exe_path = Path("dist/نظام_إدارة_المخازن_مجلد/نظام_إدارة_المخازن.exe").absolute()
        
        if exe_path.exists():
            shortcut_path = Path(desktop) / "نظام إدارة المخازن.lnk"
            
            shell = Dispatch('WScript.Shell')
            shortcut = shell.CreateShortCut(str(shortcut_path))
            shortcut.Targetpath = str(exe_path)
            shortcut.WorkingDirectory = str(exe_path.parent)
            shortcut.IconLocation = str(exe_path)
            shortcut.save()
            
            print_success(f"تم إنشاء الاختصار: {shortcut_path}")
        else:
            print_error("الملف التنفيذي غير موجود")
            return False
            
    except ImportError:
        print_warning("مكتبات إنشاء الاختصار غير متوفرة")
        print_info("يمكنك إنشاء اختصار يدوياً من الملف التنفيذي")
    except Exception as e:
        print_error(f"فشل في إنشاء الاختصار: {e}")
        return False
    
    return True

def cleanup_temp_files():
    """تنظيف الملفات المؤقتة"""
    print_step("تنظيف الملفات المؤقتة", "مسح الملفات المؤقتة المتبقية")
    
    temp_patterns = [
        "build",
        "*.log",
        "__pycache__",
        "*.pyc",
        "*.pyo"
    ]
    
    for pattern in temp_patterns:
        if "*" in pattern:
            import glob
            files = glob.glob(f"**/{pattern}", recursive=True)
            for file in files:
                try:
                    os.remove(file)
                    print_info(f"تم مسح: {file}")
                except:
                    pass
        else:
            path = Path(pattern)
            if path.exists():
                try:
                    if path.is_dir():
                        shutil.rmtree(path)
                    else:
                        path.unlink()
                    print_info(f"تم مسح: {pattern}")
                except:
                    pass
    
    print_success("تم تنظيف الملفات المؤقتة")
    return True

def print_summary():
    """طباعة ملخص العملية"""
    print_step("ملخص العملية", "تفاصيل البناء والنتائج")
    
    exe_path = Path("dist/نظام_إدارة_المخازن_مجلد/نظام_إدارة_المخازن.exe")
    
    if exe_path.exists():
        file_size = exe_path.stat().st_size / (1024 * 1024)
        creation_time = datetime.fromtimestamp(exe_path.stat().st_mtime)
        
        print_success("تم إنشاء البرنامج بنجاح!")
        print(f"📁 المسار: {exe_path.absolute()}")
        print(f"📏 الحجم: {file_size:.2f} MB")
        print(f"🕒 تاريخ الإنشاء: {creation_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🎯 جاهز للاستخدام!")
        
        # إرشادات الاستخدام
        print(f"\n{'='*60}")
        print("📋 إرشادات الاستخدام:")
        print("1. يمكنك تشغيل البرنامج من المسار أعلاه")
        print("2. يمكنك نسخ المجلد كاملاً إلى أي مكان آخر")
        print("3. البرنامج لا يحتاج تثبيت - يعمل مباشرة")
        print("4. تأكد من وجود جميع الملفات في نفس المجلد")
        print(f"{'='*60}")
    else:
        print_error("فشل في إنشاء البرنامج!")

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء عملية تحديث وبناء البرنامج")
    print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # تسلسل العمليات
    steps = [
        ("التحقق من المتطلبات", check_requirements),
        ("إنشاء نسخة احتياطية", backup_old_version),
        ("تنظيف النسخ القديمة", clean_old_builds),
        ("تثبيت المكتبات", install_dependencies),
        ("تحديث معلومات الإصدار", update_version_info),
        ("بناء الملف التنفيذي", build_executable),
        ("اختبار الملف التنفيذي", test_executable),
        ("إنشاء اختصار", create_shortcut),
        ("تنظيف الملفات المؤقتة", cleanup_temp_files)
    ]
    
    # تنفيذ الخطوات
    for step_name, step_function in steps:
        if not step_function():
            print_error(f"فشل في خطوة: {step_name}")
            print_error("تم إيقاف العملية")
            return False
    
    # طباعة الملخص
    print_summary()
    
    print_success("تمت عملية التحديث والبناء بنجاح! 🎉")
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            input("\n✅ اضغط Enter للخروج...")
        else:
            input("\n❌ حدث خطأ. اضغط Enter للخروج...")
    except KeyboardInterrupt:
        print("\n⚠️ تم إلغاء العملية بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")