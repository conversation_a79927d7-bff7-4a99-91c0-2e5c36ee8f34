#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الشاشات الإضافية - المستخدمين والإدارات والجدول التنظيمي والمستفيدين
Test Additional Windows - Users, Departments, Organizational Chart, Beneficiaries
"""

import sys
import os
import traceback

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_additional_windows():
    """اختبار الشاشات الإضافية"""
    print("🧪 بدء اختبار الشاشات الإضافية...")
    print("=" * 60)
    
    # قائمة الشاشات للاختبار
    windows_to_test = [
        ("ui.users_management_window", "UsersManagementWindow", "المستخدمين"),
        ("ui.departments_window", "DepartmentsWindow", "الإدارات"),
        ("ui.organizational_chart_window", "OrganizationalChartWindow", "الجدول التنظيمي"),
        ("ui.beneficiaries_window", "BeneficiariesWindow", "المستفيدين")
    ]
    
    results = []
    
    for module_name, class_name, display_name in windows_to_test:
        try:
            print(f"🔄 اختبار استيراد {display_name}...")
            
            # محاولة استيراد الوحدة
            module = __import__(module_name, fromlist=[class_name])
            
            # محاولة الحصول على الكلاس
            window_class = getattr(module, class_name)
            
            print(f"✅ نجح استيراد {display_name}")
            results.append((display_name, True, None))
            
        except Exception as e:
            print(f"❌ فشل استيراد {display_name}: {e}")
            print(f"   التفاصيل: {traceback.format_exc()}")
            results.append((display_name, False, str(e)))
    
    # عرض النتائج النهائية
    print("\n" + "=" * 60)
    print("📋 نتائج الاختبار:")
    print("=" * 60)
    
    success_count = 0
    failed_windows = []
    
    for display_name, success, error in results:
        if success:
            print(f"✅ {display_name} - يعمل بشكل صحيح")
            success_count += 1
        else:
            print(f"❌ {display_name} - خطأ: {error}")
            failed_windows.append(display_name)
    
    print(f"\n📊 النتيجة النهائية: {success_count}/{len(results)} شاشات تعمل")
    
    if success_count == len(results):
        print("🎉 جميع الشاشات تستورد بنجاح!")
    else:
        print("⚠️ الشاشات التالية تحتاج إلى إصلاح:")
        for window in failed_windows:
            print(f"   - {window}")
        
        print("\n💡 سيتم إصلاح هذه الأخطاء...")
        return failed_windows
    
    return []

def check_file_exists():
    """التحقق من وجود الملفات"""
    print("\n📁 التحقق من وجود ملفات الشاشات...")
    
    files_to_check = [
        "ui/users_management_window.py",
        "ui/departments_window.py",
        "ui/organizational_chart_window.py",
        "ui/beneficiaries_window.py"
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"✅ {file_path} - موجود")
        else:
            print(f"❌ {file_path} - غير موجود")

if __name__ == "__main__":
    try:
        # التحقق من وجود الملفات أولاً
        check_file_exists()
        
        # اختبار استيراد الشاشات
        failed_windows = test_additional_windows()
        
        print("\n🏁 انتهى الاختبار")
        
        if failed_windows:
            print(f"\n🔧 يجب إصلاح {len(failed_windows)} شاشات")
        else:
            print("\n🎉 جميع الشاشات تعمل بشكل صحيح!")
        
    except Exception as e:
        print(f"❌ خطأ عام في الاختبار: {e}")
        traceback.print_exc()
