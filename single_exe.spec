# -*- mode: python ; coding: utf-8 -*-
# ملف إنشاء EXE واحد مستقل - محسن ومبسط

import os
import sys
from pathlib import Path

# تحديد مسار المشروع
project_root = Path.cwd()
sys.path.insert(0, str(project_root))

# تحديد الملفات والمجلدات المطلوبة فقط
datas = []

# إضافة المجلدات الأساسية فقط
essential_dirs = ['ui', 'utils', 'assets']
for dir_name in essential_dirs:
    if os.path.exists(dir_name):
        datas.append((dir_name, dir_name))

# إضافة ملفات قاعدة البيانات والإعدادات
import glob
for pattern in ['*.db', '*.sqlite', 'settings.json']:
    for file in glob.glob(pattern):
        datas.append((file, '.'))

# المكتبات المخفية الأساسية فقط
hiddenimports = [
    # واجهة المستخدم
    'tkinter', 'tkinter.ttk', 'tkinter.messagebox', 'tkinter.filedialog',
    'ttkbootstrap', 'ttkbootstrap.constants',
    
    # معالجة الصور
    'PIL', 'PIL.Image', 'PIL.ImageTk',
    
    # قاعدة البيانات
    'sqlite3',
    
    # المكتبات الأساسية
    'datetime', 'pathlib', 'threading', 'json', 'logging',
    'hashlib', 'uuid', 'base64',
    
    # التقارير
    'openpyxl', 'reportlab', 'pandas',
    
    # الأمان
    'bcrypt',
    
    # وحدات التطبيق
    'config', 'database', 'models', 'auth_manager',
    'font_manager', 'safe_window_manager',
]

# تحليل الملف الرئيسي
a = Analysis(
    ['main.py'],
    pathex=[str(project_root)],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # استبعاد الملفات غير المطلوبة لتقليل الحجم
        'test_*', 'tests', 'pytest', 'unittest',
        '__pycache__', '*.pyc', '*.pyo',
        '.git', '.vscode', '.idea',
        'IPython', 'jupyter', 'matplotlib', 'numpy', 'scipy',
        'tensorflow', 'torch', 'cv2', 'selenium',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# إنشاء ملف تنفيذي واحد مستقل
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='نظام_إدارة_المخازن',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/app_icon.ico' if os.path.exists('assets/app_icon.ico') else None,
)
