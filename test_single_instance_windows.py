#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام منع تكرار النوافذ
Test Single Instance Window System
"""

import sys
import os
import traceback

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_single_instance_system():
    """اختبار نظام منع تكرار النوافذ"""
    print("🧪 بدء اختبار نظام منع تكرار النوافذ...")
    print("=" * 70)
    
    try:
        # استيراد النافذة الرئيسية
        from ui.main_window import MainWindow
        
        # فحص وجود النظام
        required_methods = [
            'window_instances',
            'register_window',
            'unregister_window', 
            'show_existing_window_or_create_new'
        ]
        
        print("🔍 فحص وجود مكونات النظام...")
        
        missing_components = []
        existing_components = []
        
        for component in required_methods:
            if hasattr(MainWindow, component):
                existing_components.append(component)
                print(f"✅ {component} - موجود")
            else:
                missing_components.append(component)
                print(f"❌ {component} - مفقود")
        
        print(f"\n📊 النتيجة: {len(existing_components)}/{len(required_methods)} مكونات موجودة")
        
        if missing_components:
            print(f"⚠️ المكونات المفقودة: {missing_components}")
            return False
        
        # فحص الطرق المحدثة
        print("\n" + "=" * 70)
        print("🔧 فحص الطرق المحدثة...")
        print("=" * 70)
        
        updated_methods = [
            'show_transactions',
            'manage_users',
            'manage_departments', 
            'manage_organizational_chart',
            'show_beneficiaries',
            'show_items',
            'show_reports'
        ]
        
        methods_with_system = []
        methods_without_system = []
        
        for method_name in updated_methods:
            if hasattr(MainWindow, method_name):
                # فحص محتوى الطريقة للبحث عن show_existing_window_or_create_new
                method = getattr(MainWindow, method_name)
                method_source = ""
                try:
                    import inspect
                    method_source = inspect.getsource(method)
                except:
                    pass
                
                if 'show_existing_window_or_create_new' in method_source:
                    methods_with_system.append(method_name)
                    print(f"✅ {method_name} - يستخدم النظام الجديد")
                else:
                    methods_without_system.append(method_name)
                    print(f"❌ {method_name} - لا يستخدم النظام الجديد")
            else:
                methods_without_system.append(method_name)
                print(f"❌ {method_name} - الطريقة غير موجودة")
        
        print(f"\n📊 النتيجة: {len(methods_with_system)}/{len(updated_methods)} طرق تستخدم النظام")
        
        # تقييم النتيجة النهائية
        print("\n" + "=" * 70)
        print("📋 التقييم النهائي:")
        print("=" * 70)
        
        system_percentage = (len(existing_components) / len(required_methods)) * 100
        methods_percentage = (len(methods_with_system) / len(updated_methods)) * 100
        
        overall_percentage = (system_percentage + methods_percentage) / 2
        
        print(f"🔧 مكونات النظام: {system_percentage:.1f}%")
        print(f"📱 الطرق المحدثة: {methods_percentage:.1f}%")
        print(f"🎯 النتيجة الإجمالية: {overall_percentage:.1f}%")
        
        if overall_percentage >= 90:
            print("🎉 ممتاز! النظام مطبق بشكل صحيح")
            print("✅ منع تكرار النوافذ يعمل بكفاءة")
            return True
        elif overall_percentage >= 70:
            print("🟡 جيد! النظام يعمل مع بعض النقص")
            print(f"⚠️ يجب تحديث {len(methods_without_system)} طرق")
            return False
        else:
            print("🔴 يحتاج إلى مزيد من العمل")
            print(f"❌ يجب إصلاح {len(missing_components)} مكونات و {len(methods_without_system)} طرق")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        traceback.print_exc()
        return False

def test_window_properties():
    """اختبار خصائص النوافذ"""
    print("\n" + "=" * 70)
    print("🔍 فحص خصائص النوافذ...")
    print("=" * 70)
    
    window_classes = [
        ("ui.transactions_window", "TransactionsWindow", "transactions_window"),
        ("ui.users_management_window", "UsersManagementWindow", "users_window"),
        ("ui.departments_window", "DepartmentsWindow", "window"),
        ("ui.organizational_chart_window", "OrganizationalChartWindow", "window"),
        ("ui.beneficiaries_window", "BeneficiariesWindow", "window"),
        ("ui.inventory_window", "InventoryWindow", "window"),
        ("ui.reports_window", "ReportsWindow", "reports_window")
    ]
    
    correct_properties = []
    incorrect_properties = []
    
    for module_name, class_name, expected_property in window_classes:
        try:
            # استيراد الوحدة
            module = __import__(module_name, fromlist=[class_name])
            window_class = getattr(module, class_name)
            
            # فحص الخاصية (هذا فحص تقريبي)
            print(f"📝 {class_name}: خاصية النافذة المتوقعة = {expected_property}")
            correct_properties.append(class_name)
            
        except Exception as e:
            print(f"❌ {class_name}: خطأ في الاستيراد - {e}")
            incorrect_properties.append(class_name)
    
    print(f"\n📊 النتيجة: {len(correct_properties)}/{len(window_classes)} كلاسات تم فحصها")
    
    return len(incorrect_properties) == 0

if __name__ == "__main__":
    try:
        print("🚀 بدء الاختبار الشامل لنظام منع تكرار النوافذ")
        print("=" * 70)
        
        # اختبار النظام
        system_ok = test_single_instance_system()
        
        # اختبار خصائص النوافذ
        properties_ok = test_window_properties()
        
        print("\n" + "=" * 70)
        print("🏁 النتيجة النهائية")
        print("=" * 70)
        
        if system_ok and properties_ok:
            print("🎉 النظام يعمل بشكل مثالي!")
            print("✅ منع تكرار النوافذ مطبق بنجاح")
            print("✅ جميع الشاشات ستظهر مرة واحدة فقط")
        elif system_ok:
            print("🟡 النظام يعمل مع بعض التحذيرات")
            print("⚠️ قد تحتاج بعض الشاشات إلى تعديل طفيف")
        else:
            print("🔴 النظام يحتاج إلى مزيد من العمل")
            print("❌ بعض الشاشات قد تفتح أكثر من مرة")
        
    except Exception as e:
        print(f"❌ خطأ عام في الاختبار: {e}")
        traceback.print_exc()
