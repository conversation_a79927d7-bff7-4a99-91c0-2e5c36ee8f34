#!/usr/bin/env python3
"""
نشر البرنامج إلى المسار المطلوب
Deploy to Target Path
"""

import os
import sys
import shutil
from pathlib import Path
from datetime import datetime

def print_step(message):
    """طباعة خطوة"""
    print(f"\n🔄 {message}")
    print("=" * 50)

def print_success(message):
    """طباعة نجاح"""
    print(f"✅ {message}")

def print_error(message):
    """طباعة خطأ"""
    print(f"❌ {message}")

def print_info(message):
    """طباعة معلومات"""
    print(f"ℹ️ {message}")

def main():
    """نشر البرنامج"""
    print("🚀 نشر نظام إدارة المخازن")
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # المسارات
    source_path = Path("dist/نظام_إدارة_المخازن_مجلد")
    target_base = Path("E:/desktop_stores_app/dist")
    target_path = target_base / "نظام_إدارة_المخازن_مجلد"
    
    print_step("التحقق من الملفات المصدر")
    
    # التحقق من وجود الملف المصدر
    if not source_path.exists():
        print_error("المجلد المصدر غير موجود!")
        print_info("يجب تشغيل البناء أولاً")
        return False
    
    exe_file = source_path / "نظام_إدارة_المخازن.exe"
    if not exe_file.exists():
        print_error("الملف التنفيذي غير موجود!")
        return False
    
    file_size = exe_file.stat().st_size / (1024 * 1024)
    print_success(f"الملف المصدر موجود - الحجم: {file_size:.2f} MB")
    
    print_step("إنشاء نسخة احتياطية من النسخة الحالية")
    
    # إنشاء نسخة احتياطية إذا كان المجلد الهدف موجود
    if target_path.exists():
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = target_base / f"backup_{timestamp}"
        
        try:
            print_info(f"نسخ النسخة الحالية إلى: {backup_path}")
            shutil.copytree(target_path, backup_path)
            print_success("تم إنشاء النسخة الاحتياطية")
        except Exception as e:
            print_error(f"فشل في إنشاء النسخة الاحتياطية: {e}")
            return False
    else:
        print_info("لا توجد نسخة سابقة")
    
    print_step("مسح النسخة القديمة")
    
    # مسح النسخة القديمة
    if target_path.exists():
        try:
            print_info("مسح النسخة القديمة...")
            shutil.rmtree(target_path)
            print_success("تم مسح النسخة القديمة")
        except Exception as e:
            print_error(f"فشل في مسح النسخة القديمة: {e}")
            return False
    
    print_step("نسخ النسخة الجديدة")
    
    # إنشاء المجلد الهدف إذا لم يكن موجود
    target_base.mkdir(parents=True, exist_ok=True)
    
    # نسخ النسخة الجديدة
    try:
        print_info(f"نسخ من: {source_path}")
        print_info(f"إلى: {target_path}")
        
        shutil.copytree(source_path, target_path)
        print_success("تم نسخ النسخة الجديدة بنجاح")
        
        # التحقق من النسخ
        new_exe = target_path / "نظام_إدارة_المخازن.exe"
        if new_exe.exists():
            new_size = new_exe.stat().st_size / (1024 * 1024)
            print_success(f"الملف التنفيذي الجديد: {new_size:.2f} MB")
        
    except Exception as e:
        print_error(f"فشل في نسخ النسخة الجديدة: {e}")
        return False
    
    print_step("تنظيف النسخ الاحتياطية القديمة")
    
    # الاحتفاظ بآخر 3 نسخ احتياطية فقط
    try:
        backup_folders = []
        for item in target_base.iterdir():
            if item.is_dir() and item.name.startswith("backup_"):
                backup_folders.append(item)
        
        # ترتيب حسب التاريخ (الأحدث أولاً)
        backup_folders.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        
        # مسح النسخ الزائدة (الاحتفاظ بآخر 3)
        for old_backup in backup_folders[3:]:
            try:
                shutil.rmtree(old_backup)
                print_info(f"تم مسح النسخة الاحتياطية القديمة: {old_backup.name}")
            except Exception as e:
                print_error(f"فشل في مسح {old_backup.name}: {e}")
        
        if len(backup_folders) <= 3:
            print_info("لا توجد نسخ احتياطية زائدة للمسح")
        else:
            print_success(f"تم مسح {len(backup_folders) - 3} نسخة احتياطية قديمة")
            
    except Exception as e:
        print_error(f"خطأ في تنظيف النسخ الاحتياطية: {e}")
    
    print_step("ملخص العملية")
    
    # طباعة الملخص
    print_success("تم نشر البرنامج بنجاح!")
    print(f"📁 المسار النهائي: {target_path}")
    print(f"📏 حجم البرنامج: {file_size:.2f} MB")
    print(f"🕒 تاريخ النشر: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # عرض محتويات المجلد الهدف
    print(f"\n📋 محتويات المجلد:")
    try:
        for item in target_path.iterdir():
            if item.is_file():
                size = item.stat().st_size / (1024 * 1024)
                print(f"   📄 {item.name} ({size:.2f} MB)")
            else:
                print(f"   📁 {item.name}/")
    except Exception as e:
        print_error(f"فشل في عرض المحتويات: {e}")
    
    print(f"\n🎯 البرنامج جاهز للاستخدام من المسار:")
    print(f"   {target_path / 'نظام_إدارة_المخازن.exe'}")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        input(f"\n{'✅ نجح النشر' if success else '❌ فشل النشر'} - اضغط Enter للخروج...")
    except KeyboardInterrupt:
        print("\n⚠️ تم إلغاء العملية")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")