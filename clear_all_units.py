#!/usr/bin/env python3
"""
حذف جميع الوحدات من قاعدة البيانات
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from database import db_manager

def clear_all_units():
    """حذف جميع الوحدات من قاعدة البيانات"""
    try:
        print("🗑️ بدء حذف جميع الوحدات...")
        
        # حذف جميع الوحدات
        result = db_manager.execute_query("DELETE FROM units")
        
        if result:
            print("✅ تم حذف جميع الوحدات بنجاح!")
            
            # التحقق من النتيجة
            count = db_manager.fetch_one("SELECT COUNT(*) as count FROM units")
            print(f"📊 عدد الوحدات المتبقية: {count['count'] if count else 0}")
            
        else:
            print("❌ فشل في حذف الوحدات")
            
    except Exception as e:
        print(f"❌ خطأ في حذف الوحدات: {e}")

if __name__ == "__main__":
    clear_all_units()