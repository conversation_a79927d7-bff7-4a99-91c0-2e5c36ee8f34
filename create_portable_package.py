#!/usr/bin/env python3
"""
إنشاء حزمة محمولة كاملة للبرنامج
Create Complete Portable Package
"""

import os
import shutil
import zipfile
from pathlib import Path
from datetime import datetime

def create_portable_package():
    """إنشاء حزمة محمولة كاملة"""
    
    print("🎯 إنشاء حزمة محمولة كاملة لنظام إدارة المخازن")
    print("=" * 60)
    
    # إنشاء مجلد الحزمة
    package_name = f"نظام_إدارة_المخازن_محمول_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    package_dir = Path("dist") / package_name
    
    # حذف المجلد إذا كان موجوداً
    if package_dir.exists():
        shutil.rmtree(package_dir)
    
    # إنشاء المجلد
    package_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"📁 إنشاء مجلد الحزمة: {package_dir}")
    
    # نسخ ملف EXE
    exe_file = Path("dist") / "نظام_إدارة_المخازن_كامل.exe"
    if exe_file.exists():
        shutil.copy2(exe_file, package_dir / "نظام_إدارة_المخازن.exe")
        print("✅ تم نسخ ملف البرنامج الرئيسي")
    else:
        print("❌ ملف EXE غير موجود!")
        return False
    
    # نسخ الملفات الأساسية
    essential_files = [
        "stores_management.db",
        "settings.json",
        "requirements.txt"
    ]
    
    for file_name in essential_files:
        file_path = Path(file_name)
        if file_path.exists():
            shutil.copy2(file_path, package_dir / file_name)
            print(f"✅ تم نسخ: {file_name}")
    
    # نسخ المجلدات الأساسية
    essential_dirs = [
        "data",
        "reports", 
        "backups",
        "logs",
        "assets"
    ]
    
    for dir_name in essential_dirs:
        dir_path = Path(dir_name)
        if dir_path.exists():
            shutil.copytree(dir_path, package_dir / dir_name, dirs_exist_ok=True)
            print(f"✅ تم نسخ مجلد: {dir_name}")
    
    # إنشاء ملف README
    readme_content = """# نظام إدارة المخازن - النسخة المحمولة

## كيفية الاستخدام:
1. قم بتشغيل ملف "نظام_إدارة_المخازن.exe"
2. البرنامج جاهز للاستخدام مباشرة
3. جميع البيانات محفوظة في مجلد البرنامج

## الملفات المهمة:
- نظام_إدارة_المخازن.exe: البرنامج الرئيسي
- stores_management.db: قاعدة البيانات
- settings.json: إعدادات البرنامج
- data/: بيانات النظام
- reports/: التقارير المُنشأة
- backups/: النسخ الاحتياطية
- logs/: ملفات السجلات

## متطلبات النظام:
- Windows 10 أو أحدث
- لا يتطلب تثبيت Python أو أي مكتبات إضافية

## الدعم الفني:
- جميع المكتبات والمتطلبات مدمجة في البرنامج
- يعمل على أي جهاز Windows بدون تثبيت إضافي
- يمكن نسخه على فلاشة USB للاستخدام المحمول

تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
    
    with open(package_dir / "README.txt", "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    print("✅ تم إنشاء ملف README.txt")
    
    # إنشاء ملف تشغيل سريع
    run_script = """@echo off
echo تشغيل نظام إدارة المخازن...
echo Starting Stores Management System...
start "" "نظام_إدارة_المخازن.exe"
"""
    
    with open(package_dir / "تشغيل_البرنامج.bat", "w", encoding="utf-8") as f:
        f.write(run_script)
    
    print("✅ تم إنشاء ملف التشغيل السريع")
    
    # حساب حجم الحزمة
    total_size = 0
    file_count = 0
    
    for root, dirs, files in os.walk(package_dir):
        for file in files:
            file_path = os.path.join(root, file)
            total_size += os.path.getsize(file_path)
            file_count += 1
    
    size_mb = total_size / (1024 * 1024)
    
    print("\n" + "=" * 60)
    print("🎉 تم إنشاء الحزمة المحمولة بنجاح!")
    print(f"📁 المجلد: {package_dir}")
    print(f"📊 عدد الملفات: {file_count}")
    print(f"💾 الحجم الإجمالي: {size_mb:.2f} MB")
    print("=" * 60)
    
    # إنشاء ملف ZIP للحزمة
    zip_file = package_dir.with_suffix('.zip')
    
    print(f"\n📦 إنشاء ملف مضغوط: {zip_file.name}")
    
    with zipfile.ZipFile(zip_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(package_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_name = os.path.relpath(file_path, package_dir.parent)
                zipf.write(file_path, arc_name)
    
    zip_size = os.path.getsize(zip_file) / (1024 * 1024)
    print(f"✅ تم إنشاء الملف المضغوط بحجم: {zip_size:.2f} MB")
    
    print("\n🚀 الحزمة جاهزة للتوزيع!")
    print("يمكنك نسخ المجلد أو الملف المضغوط إلى أي جهاز آخر واستخدامه مباشرة")
    
    return True

if __name__ == "__main__":
    create_portable_package()