"""
مكون رسالة النجاح التلقائية - تطبيق إدارة المخازن
Auto Success Message Component - Desktop Stores Management System
"""

import tkinter as tk
from tkinter import ttk


class AutoSuccessMessage:
    """مكون رسالة النجاح التلقائية"""
    
    @staticmethod
    def show(parent, message, duration=3000, position="center"):
        """
        عرض رسالة نجاح تلقائية
        
        Args:
            parent: النافذة الأب
            message: نص الرسالة
            duration: مدة العرض بالميلي ثانية (افتراضي 3 ثوانٍ)
            position: موضع الرسالة ("center", "top", "bottom")
        """
        # إنشاء نافذة الرسالة
        success_window = tk.Toplevel(parent)
        success_window.title("")
        success_window.resizable(False, False)
        success_window.configure(bg='#d4edda')
        
        # تحديد أبعاد النافذة
        window_width = 500
        window_height = 180
        
        # إعداد النافذة أولاً
        success_window.geometry(f"{window_width}x{window_height}")
        
        # إزالة شريط العنوان بعد إعداد الحجم
        success_window.overrideredirect(True)
        
        # فرض تحديث النافذة
        success_window.update_idletasks()
        success_window.update()
        
        # الحصول على أبعاد الشاشة الفعلية
        screen_width = success_window.winfo_screenwidth()
        screen_height = success_window.winfo_screenheight()
        
        # حساب الموضع للتوسيط المثالي مع هامش أمان
        center_x = (screen_width - window_width) // 2
        center_y = (screen_height - window_height) // 2
        
        # تطبيق الموضع حسب الخيار المحدد
        if position == "top":
            x = center_x
            y = 80
        elif position == "bottom":
            x = center_x
            y = screen_height - window_height - 80
        else:  # center - الافتراضي
            x = center_x
            y = center_y
        
        # التأكد من أن النافذة داخل حدود الشاشة
        x = max(10, min(x, screen_width - window_width - 10))
        y = max(10, min(y, screen_height - window_height - 10))
        
        # تطبيق الموضع النهائي
        success_window.geometry(f"{window_width}x{window_height}+{x}+{y}")
        
        # تحديث إضافي للتأكد من الموضع
        success_window.update_idletasks()
        success_window.update()
        
        # تأكيد إضافي للموضع (في حالة وجود مشاكل في النظام)
        success_window.after(10, lambda: success_window.geometry(f"{window_width}x{window_height}+{x}+{y}"))
        
        # جعل النافذة في المقدمة
        success_window.lift()
        success_window.attributes('-topmost', True)
        
        # إطار خارجي للظل (أكثر وضوحاً)
        shadow_frame = tk.Frame(success_window, bg='#333333', relief='flat')
        shadow_frame.place(x=4, y=4, relwidth=1, relheight=1)
        
        # إطار الرسالة الرئيسي مع حدود قوية
        message_frame = tk.Frame(
            success_window, 
            bg='#d4edda', 
            relief='solid', 
            bd=4,
            highlightbackground='#28a745',
            highlightthickness=2
        )
        message_frame.place(x=0, y=0, relwidth=1, relheight=1)
        
        # إطار المحتوى الداخلي مع مساحة أكبر
        content_frame = tk.Frame(message_frame, bg='#d4edda')
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # أيقونة النجاح أكبر وأوضح
        icon_label = tk.Label(
            content_frame,
            text="✅",
            font=("Arial", 32, "bold"),
            bg='#d4edda',
            fg='#155724'
        )
        icon_label.pack(pady=(0, 15))
        
        # نص الرسالة واضح ومقروء
        text_label = tk.Label(
            content_frame,
            text=message,
            font=("Arial", 13, "bold"),
            bg='#d4edda',
            fg='#155724',
            wraplength=450,
            justify='center'
        )
        text_label.pack(pady=(0, 15))
        
        # نص التعليمات
        instruction_label = tk.Label(
            content_frame,
            text="انقر في أي مكان أو انتظر لإغلاق الرسالة",
            font=("Arial", 11),
            bg='#d4edda',
            fg='#6c757d',
            justify='center'
        )
        instruction_label.pack(pady=(0, 5))
        
        # متغير لتتبع ما إذا كانت النافذة مغلقة
        window_closed = [False]
        
        def close_success_window():
            """إغلاق نافذة النجاح"""
            if not window_closed[0]:
                window_closed[0] = True
                try:
                    success_window.destroy()
                except:
                    pass
        
        # ربط النقر بإغلاق النافذة
        def on_click(event=None):
            close_success_window()
        
        # ربط الأحداث لجميع العناصر
        for widget in [success_window, shadow_frame, message_frame, content_frame, icon_label, text_label, instruction_label]:
            widget.bind("<Button-1>", on_click)
        
        # ربط مفاتيح الإغلاق
        success_window.bind("<Escape>", on_click)
        success_window.bind("<Return>", on_click)
        success_window.bind("<space>", on_click)
        
        # إغلاق تلقائي بعد المدة المحددة
        success_window.after(duration, close_success_window)
        
        # التركيز على النافذة لتلقي أحداث لوحة المفاتيح
        success_window.focus_set()
        
        return success_window


class AutoErrorMessage:
    """مكون رسالة الخطأ التلقائية"""
    
    @staticmethod
    def show(parent, message, duration=5000, position="center"):
        """
        عرض رسالة خطأ تلقائية
        
        Args:
            parent: النافذة الأب
            message: نص الرسالة
            duration: مدة العرض بالميلي ثانية (افتراضي 5 ثوانٍ)
            position: موضع الرسالة ("center", "top", "bottom")
        """
        # إنشاء نافذة الرسالة
        error_window = tk.Toplevel(parent)
        error_window.title("")
        error_window.geometry("400x120")
        error_window.resizable(False, False)
        error_window.configure(bg='#f8d7da')
        
        # إزالة شريط العنوان
        error_window.overrideredirect(True)
        
        # تحديد موضع النافذة
        error_window.update_idletasks()
        screen_width = error_window.winfo_screenwidth()
        screen_height = error_window.winfo_screenheight()
        window_width = 400
        window_height = 120
        
        x = (screen_width - window_width) // 2
        
        if position == "top":
            y = 100
        elif position == "bottom":
            y = screen_height - window_height - 100
        else:  # center
            y = (screen_height - window_height) // 2
        
        error_window.geometry(f"{window_width}x{window_height}+{x}+{y}")
        
        # جعل النافذة في المقدمة
        error_window.lift()
        error_window.attributes('-topmost', True)
        
        # إطار الرسالة مع حدود
        message_frame = tk.Frame(
            error_window, 
            bg='#f8d7da', 
            relief='solid', 
            bd=2
        )
        message_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # أيقونة الخطأ
        icon_label = tk.Label(
            message_frame,
            text="❌",
            font=("Arial", 24),
            bg='#f8d7da',
            fg='#721c24'
        )
        icon_label.pack(pady=(15, 5))
        
        # نص الرسالة
        text_label = tk.Label(
            message_frame,
            text=message,
            font=("Arial", 12, "bold"),
            bg='#f8d7da',
            fg='#721c24',
            wraplength=350
        )
        text_label.pack(pady=(0, 5))
        
        # نص التعليمات
        instruction_label = tk.Label(
            message_frame,
            text="انقر في أي مكان أو انتظر لإغلاق الرسالة",
            font=("Arial", 9),
            bg='#f8d7da',
            fg='#6c757d'
        )
        instruction_label.pack(pady=(0, 10))
        
        # متغير لتتبع ما إذا كانت النافذة مغلقة
        window_closed = [False]
        
        def close_error_window():
            """إغلاق نافذة الخطأ"""
            if not window_closed[0]:
                window_closed[0] = True
                try:
                    error_window.destroy()
                except:
                    pass
        
        # ربط النقر بإغلاق النافذة
        def on_click(event=None):
            close_error_window()
        
        # ربط الأحداث لجميع العناصر
        for widget in [error_window, message_frame, icon_label, text_label, instruction_label]:
            widget.bind("<Button-1>", on_click)
        
        # ربط مفاتيح الإغلاق
        error_window.bind("<Escape>", on_click)
        error_window.bind("<Return>", on_click)
        error_window.bind("<space>", on_click)
        
        # إغلاق تلقائي بعد المدة المحددة
        error_window.after(duration, close_error_window)
        
        # التركيز على النافذة لتلقي أحداث لوحة المفاتيح
        error_window.focus_set()
        
        return error_window


class AutoWarningMessage:
    """مكون رسالة التحذير التلقائية"""
    
    @staticmethod
    def show(parent, message, duration=4000, position="center"):
        """
        عرض رسالة تحذير تلقائية
        
        Args:
            parent: النافذة الأب
            message: نص الرسالة
            duration: مدة العرض بالميلي ثانية (افتراضي 4 ثوانٍ)
            position: موضع الرسالة ("center", "top", "bottom")
        """
        # إنشاء نافذة الرسالة
        warning_window = tk.Toplevel(parent)
        warning_window.title("")
        warning_window.geometry("400x120")
        warning_window.resizable(False, False)
        warning_window.configure(bg='#fff3cd')
        
        # إزالة شريط العنوان
        warning_window.overrideredirect(True)
        
        # تحديد موضع النافذة
        warning_window.update_idletasks()
        screen_width = warning_window.winfo_screenwidth()
        screen_height = warning_window.winfo_screenheight()
        window_width = 400
        window_height = 120
        
        x = (screen_width - window_width) // 2
        
        if position == "top":
            y = 100
        elif position == "bottom":
            y = screen_height - window_height - 100
        else:  # center
            y = (screen_height - window_height) // 2
        
        warning_window.geometry(f"{window_width}x{window_height}+{x}+{y}")
        
        # جعل النافذة في المقدمة
        warning_window.lift()
        warning_window.attributes('-topmost', True)
        
        # إطار الرسالة مع حدود
        message_frame = tk.Frame(
            warning_window, 
            bg='#fff3cd', 
            relief='solid', 
            bd=2
        )
        message_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # أيقونة التحذير
        icon_label = tk.Label(
            message_frame,
            text="⚠️",
            font=("Arial", 24),
            bg='#fff3cd',
            fg='#856404'
        )
        icon_label.pack(pady=(15, 5))
        
        # نص الرسالة
        text_label = tk.Label(
            message_frame,
            text=message,
            font=("Arial", 12, "bold"),
            bg='#fff3cd',
            fg='#856404',
            wraplength=350
        )
        text_label.pack(pady=(0, 5))
        
        # نص التعليمات
        instruction_label = tk.Label(
            message_frame,
            text="انقر في أي مكان أو انتظر لإغلاق الرسالة",
            font=("Arial", 9),
            bg='#fff3cd',
            fg='#6c757d'
        )
        instruction_label.pack(pady=(0, 10))
        
        # متغير لتتبع ما إذا كانت النافذة مغلقة
        window_closed = [False]
        
        def close_warning_window():
            """إغلاق نافذة التحذير"""
            if not window_closed[0]:
                window_closed[0] = True
                try:
                    warning_window.destroy()
                except:
                    pass
        
        # ربط النقر بإغلاق النافذة
        def on_click(event=None):
            close_warning_window()
        
        # ربط الأحداث لجميع العناصر
        for widget in [warning_window, message_frame, icon_label, text_label, instruction_label]:
            widget.bind("<Button-1>", on_click)
        
        # ربط مفاتيح الإغلاق
        warning_window.bind("<Escape>", on_click)
        warning_window.bind("<Return>", on_click)
        warning_window.bind("<space>", on_click)
        
        # إغلاق تلقائي بعد المدة المحددة
        warning_window.after(duration, close_warning_window)
        
        # التركيز على النافذة لتلقي أحداث لوحة المفاتيح
        warning_window.focus_set()
        
        return warning_window