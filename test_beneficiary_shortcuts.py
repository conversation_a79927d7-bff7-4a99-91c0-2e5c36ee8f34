#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مفاتيح الاختصار في نافذة إضافة المستفيد
Test Beneficiary Shortcuts
"""

import tkinter as tk
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ui.beneficiaries_window import AddBeneficiaryWindow

class TestBeneficiaryShortcuts:
    """اختبار مفاتيح الاختصار في نافذة المستفيد"""
    
    def __init__(self):
        self.root = ttk_bs.Window(themename="flatly")
        self.root.title("🧪 اختبار مفاتيح الاختصار - نافذة المستفيد")
        self.root.geometry("600x400")
        
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة الاختبار"""
        # العنوان
        title_label = ttk_bs.Label(
            self.root,
            text="🧪 اختبار مفاتيح الاختصار - نافذة إضافة المستفيد",
            font=("Arial", 16, "bold"),
            bootstyle="primary"
        )
        title_label.pack(pady=20)
        
        # معلومات مفاتيح الاختصار
        info_frame = ttk_bs.LabelFrame(
            self.root,
            text="⌨️ مفاتيح الاختصار المتاحة",
            padding=20
        )
        info_frame.pack(pady=10, padx=20, fill="x")
        
        shortcuts_info = [
            "F1 - حفظ المستفيد",
            "F2 - مسح النموذج",
            "F3 - نسخ البيانات",
            "F4 - لصق البيانات"
        ]
        
        for shortcut in shortcuts_info:
            label = ttk_bs.Label(
                info_frame,
                text=f"• {shortcut}",
                font=("Arial", 12)
            )
            label.pack(anchor="w", pady=2)
        
        # أزرار الاختبار
        buttons_frame = ttk_bs.Frame(self.root)
        buttons_frame.pack(pady=20)
        
        # زر فتح نافذة إضافة مستفيد
        open_add_btn = ttk_bs.Button(
            buttons_frame,
            text="➕ فتح نافذة إضافة مستفيد",
            command=self.open_add_beneficiary_window,
            bootstyle="success",
            width=25
        )
        open_add_btn.pack(side="left", padx=10)
        
        # زر فتح نافذة تعديل مستفيد
        open_edit_btn = ttk_bs.Button(
            buttons_frame,
            text="✏️ فتح نافذة تعديل مستفيد",
            command=self.open_edit_beneficiary_window,
            bootstyle="warning",
            width=25
        )
        open_edit_btn.pack(side="left", padx=10)
        
        # منطقة الرسائل
        self.messages_frame = ttk_bs.LabelFrame(
            self.root,
            text="📝 رسائل الاختبار",
            padding=10
        )
        self.messages_frame.pack(pady=10, padx=20, fill="both", expand=True)
        
        self.messages_text = tk.Text(
            self.messages_frame,
            height=8,
            font=("Arial", 10),
            wrap="word"
        )
        self.messages_text.pack(fill="both", expand=True)
        
        # شريط التمرير
        scrollbar = ttk_bs.Scrollbar(self.messages_text)
        scrollbar.pack(side="right", fill="y")
        self.messages_text.config(yscrollcommand=scrollbar.set)
        scrollbar.config(command=self.messages_text.yview)
        
        # رسالة ترحيب
        self.add_message("🎉 مرحباً بك في اختبار مفاتيح الاختصار!")
        self.add_message("📋 اضغط على أحد الأزرار لفتح نافذة المستفيد واختبار مفاتيح الاختصار")
        
    def add_message(self, message):
        """إضافة رسالة إلى منطقة الرسائل"""
        self.messages_text.insert("end", f"{message}\n")
        self.messages_text.see("end")
        
    def open_add_beneficiary_window(self):
        """فتح نافذة إضافة مستفيد جديد"""
        try:
            self.add_message("🔄 فتح نافذة إضافة مستفيد جديد...")
            
            # إنشاء كائن وهمي للنافذة الرئيسية
            class MockBeneficiariesWindow:
                def refresh_data(self):
                    pass
            
            mock_window = MockBeneficiariesWindow()
            
            # فتح نافذة إضافة المستفيد
            add_window = AddBeneficiaryWindow(
                parent=self.root,
                beneficiaries_window=mock_window,
                edit_mode=False
            )
            
            self.add_message("✅ تم فتح نافذة إضافة المستفيد بنجاح!")
            self.add_message("⌨️ جرب الآن مفاتيح الاختصار F1-F4")
            
        except Exception as e:
            self.add_message(f"❌ خطأ في فتح نافذة إضافة المستفيد: {e}")
            
    def open_edit_beneficiary_window(self):
        """فتح نافذة تعديل مستفيد"""
        try:
            self.add_message("🔄 فتح نافذة تعديل مستفيد...")
            
            # إنشاء كائن وهمي للنافذة الرئيسية
            class MockBeneficiariesWindow:
                def refresh_data(self):
                    pass
            
            mock_window = MockBeneficiariesWindow()
            
            # بيانات وهمية للمستفيد
            class MockBeneficiary:
                def __init__(self):
                    self.id = 1
                    self.name = "أحمد محمد علي"
                    self.general_number = "12345"
                    self.category = "ضابط"
                    self.rank = "نقيب"
                    self.unit_id = 1
                    self.department_id = 1
                    self.section_id = 1
                    self.data_entry_by = "admin"
            
            mock_beneficiary = MockBeneficiary()
            
            # فتح نافذة تعديل المستفيد
            edit_window = AddBeneficiaryWindow(
                parent=self.root,
                beneficiaries_window=mock_window,
                edit_mode=True,
                beneficiary_data=mock_beneficiary
            )
            
            self.add_message("✅ تم فتح نافذة تعديل المستفيد بنجاح!")
            self.add_message("⌨️ جرب الآن مفاتيح الاختصار F1-F4")
            
        except Exception as e:
            self.add_message(f"❌ خطأ في فتح نافذة تعديل المستفيد: {e}")
    
    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

def main():
    """الدالة الرئيسية"""
    print("🧪 بدء اختبار مفاتيح الاختصار في نافذة المستفيد...")
    
    try:
        app = TestBeneficiaryShortcuts()
        app.run()
    except Exception as e:
        print(f"❌ خطأ في تشغيل الاختبار: {e}")

if __name__ == "__main__":
    main()
