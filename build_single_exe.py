#!/usr/bin/env python3
"""
سكريبت بناء ملف EXE واحد مستقل
Build Single EXE File Script

الاستخدام:
python build_single_exe.py
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
import time

def print_status(message, status="INFO"):
    """طباعة رسالة حالة ملونة"""
    colors = {
        "INFO": "\033[94m",     # أزرق
        "SUCCESS": "\033[92m",  # أخضر
        "WARNING": "\033[93m",  # أصفر
        "ERROR": "\033[91m",    # أحمر
        "RESET": "\033[0m"      # إعادة تعيين
    }
    
    color = colors.get(status, colors["INFO"])
    reset = colors["RESET"]
    print(f"{color}[{status}] {message}{reset}")

def check_requirements():
    """التحقق من المتطلبات"""
    print_status("التحقق من المتطلبات...")
    
    # التحقق من PyInstaller
    try:
        import PyInstaller
        print_status(f"PyInstaller موجود - الإصدار: {PyInstaller.__version__}", "SUCCESS")
    except ImportError:
        print_status("PyInstaller غير مثبت. جاري التثبيت...", "WARNING")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
            print_status("تم تثبيت PyInstaller بنجاح", "SUCCESS")
        except subprocess.CalledProcessError:
            print_status("فشل في تثبيت PyInstaller", "ERROR")
            return False
    
    # التحقق من الملف الرئيسي
    if not Path("main.py").exists():
        print_status("ملف main.py غير موجود", "ERROR")
        return False
    
    print_status("جميع المتطلبات متوفرة", "SUCCESS")
    return True

def clean_build_dirs():
    """تنظيف مجلدات البناء السابقة"""
    print_status("تنظيف مجلدات البناء السابقة...")
    
    dirs_to_clean = ["build", "dist", "__pycache__"]
    
    for dir_name in dirs_to_clean:
        if Path(dir_name).exists():
            try:
                shutil.rmtree(dir_name)
                print_status(f"تم حذف مجلد {dir_name}", "SUCCESS")
            except Exception as e:
                print_status(f"تعذر حذف مجلد {dir_name}: {e}", "WARNING")

def create_optimized_spec():
    """إنشاء ملف .spec محسن لملف exe واحد"""
    print_status("إنشاء ملف .spec محسن...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-
# ملف إنشاء EXE واحد مستقل - محسن ومبسط

import os
import sys
from pathlib import Path

# تحديد مسار المشروع
project_root = Path.cwd()
sys.path.insert(0, str(project_root))

# تحديد الملفات والمجلدات المطلوبة فقط
datas = []

# إضافة المجلدات الأساسية فقط
essential_dirs = ['ui', 'utils', 'assets']
for dir_name in essential_dirs:
    if os.path.exists(dir_name):
        datas.append((dir_name, dir_name))

# إضافة ملفات قاعدة البيانات والإعدادات
import glob
for pattern in ['*.db', '*.sqlite', 'settings.json']:
    for file in glob.glob(pattern):
        datas.append((file, '.'))

# المكتبات المخفية الأساسية فقط
hiddenimports = [
    # واجهة المستخدم
    'tkinter', 'tkinter.ttk', 'tkinter.messagebox', 'tkinter.filedialog',
    'ttkbootstrap', 'ttkbootstrap.constants',
    
    # معالجة الصور
    'PIL', 'PIL.Image', 'PIL.ImageTk',
    
    # قاعدة البيانات
    'sqlite3',
    
    # المكتبات الأساسية
    'datetime', 'pathlib', 'threading', 'json', 'logging',
    'hashlib', 'uuid', 'base64',
    
    # التقارير
    'openpyxl', 'reportlab', 'pandas',
    
    # الأمان
    'bcrypt',
    
    # وحدات التطبيق
    'config', 'database', 'models', 'auth_manager',
    'font_manager', 'safe_window_manager',
]

# تحليل الملف الرئيسي
a = Analysis(
    ['main.py'],
    pathex=[str(project_root)],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # استبعاد الملفات غير المطلوبة لتقليل الحجم
        'test_*', 'tests', 'pytest', 'unittest',
        '__pycache__', '*.pyc', '*.pyo',
        '.git', '.vscode', '.idea',
        'IPython', 'jupyter', 'matplotlib', 'numpy', 'scipy',
        'tensorflow', 'torch', 'cv2', 'selenium',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# إنشاء ملف تنفيذي واحد مستقل
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='نظام_إدارة_المخازن',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/app_icon.ico' if os.path.exists('assets/app_icon.ico') else None,
)
'''
    
    with open("single_exe.spec", "w", encoding="utf-8") as f:
        f.write(spec_content)
    
    print_status("تم إنشاء ملف single_exe.spec", "SUCCESS")

def build_exe():
    """بناء ملف EXE"""
    print_status("بدء عملية بناء ملف EXE...")
    start_time = time.time()
    
    try:
        # تشغيل PyInstaller
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", "single_exe.spec"]
        
        print_status(f"تشغيل الأمر: {' '.join(cmd)}")
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            encoding='utf-8'
        )
        
        if result.returncode == 0:
            elapsed_time = time.time() - start_time
            print_status(f"تم بناء ملف EXE بنجاح في {elapsed_time:.1f} ثانية", "SUCCESS")
            return True
        else:
            print_status("فشل في بناء ملف EXE", "ERROR")
            print_status(f"رسالة الخطأ: {result.stderr}", "ERROR")
            return False
            
    except Exception as e:
        print_status(f"خطأ أثناء البناء: {e}", "ERROR")
        return False

def check_output():
    """التحقق من الملف المُنشأ"""
    print_status("التحقق من الملف المُنشأ...")
    
    dist_dir = Path("dist")
    if not dist_dir.exists():
        print_status("مجلد dist غير موجود", "ERROR")
        return False
    
    exe_files = list(dist_dir.glob("*.exe"))
    
    if not exe_files:
        print_status("لم يتم العثور على ملف exe", "ERROR")
        return False
    
    exe_file = exe_files[0]
    file_size = exe_file.stat().st_size / (1024 * 1024)  # بالميجابايت
    
    print_status(f"تم إنشاء الملف: {exe_file.name}", "SUCCESS")
    print_status(f"حجم الملف: {file_size:.1f} ميجابايت", "INFO")
    print_status(f"مسار الملف: {exe_file.absolute()}", "INFO")
    
    # التحقق من أن هناك ملف واحد فقط في dist
    all_files = list(dist_dir.iterdir())
    if len(all_files) == 1:
        print_status("✓ تم إنشاء ملف exe واحد فقط كما هو مطلوب", "SUCCESS")
    else:
        print_status(f"تحذير: يوجد {len(all_files)} ملف في مجلد dist", "WARNING")
        for file in all_files:
            print_status(f"  - {file.name}", "INFO")
    
    return True

def main():
    """الدالة الرئيسية"""
    print_status("=== بناء ملف EXE واحد مستقل ===", "INFO")
    print_status("نظام إدارة المخازن والمستودعات", "INFO")
    print_status("=" * 50, "INFO")
    
    # التحقق من المتطلبات
    if not check_requirements():
        print_status("فشل في التحقق من المتطلبات", "ERROR")
        return False
    
    # تنظيف مجلدات البناء
    clean_build_dirs()
    
    # إنشاء ملف .spec محسن
    create_optimized_spec()
    
    # بناء ملف EXE
    if not build_exe():
        print_status("فشل في بناء ملف EXE", "ERROR")
        return False
    
    # التحقق من النتيجة
    if not check_output():
        print_status("فشل في التحقق من الملف المُنشأ", "ERROR")
        return False
    
    print_status("=" * 50, "SUCCESS")
    print_status("تم إنشاء ملف EXE واحد مستقل بنجاح!", "SUCCESS")
    print_status("يمكنك العثور على الملف في مجلد dist/", "SUCCESS")
    print_status("=" * 50, "SUCCESS")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)