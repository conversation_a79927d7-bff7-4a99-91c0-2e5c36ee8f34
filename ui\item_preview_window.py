#!/usr/bin/env python3
"""
شاشة معاينة الصنف
Item Preview Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime

class ItemPreviewWindow:
    def __init__(self, parent, item_data, refresh_callback=None):
        print(f"🚀 إنشاء شاشة معاينة الصنف...")
        print(f"📊 البيانات المستلمة: {item_data}")

        self.parent = parent
        self.item_data = item_data
        self.refresh_callback = refresh_callback
        self.window = None

        try:
            self.create_window()
            self.load_item_movements()

            # بدء التحديث التلقائي
            self.start_auto_refresh()
            print("✅ تم إنشاء شاشة معاينة الصنف بنجاح")
        except Exception as e:
            print(f"❌ خطأ في إنشاء شاشة معاينة الصنف: {e}")
            import traceback
            traceback.print_exc()
            raise

    def create_window(self):
        """إنشاء النافذة"""
        try:
            # إنشاء النافذة باستخدام مدير النوافذ الآمن
            try:
                from safe_window_manager import create_safe_toplevel

                self.window = create_safe_toplevel(
                    self.parent,
                    title="بيانات الصنف"
                )

                if not self.window:
                    raise Exception("فشل في إنشاء نافذة آمنة")

            except Exception as e:
                print(f"تحذير: فشل في استخدام مدير النوافذ الآمن: {e}")
                # إنشاء النافذة بالطريقة التقليدية كبديل
                self.window = tk.Toplevel(self.parent)
                self.window.title("بيانات الصنف")

            self.window.geometry("1300x700")
            self.window.resizable(False, False)

            # توسيط النافذة
            self.center_window()

            # تعيين النافذة كنافذة فرعية
            self.window.transient(self.parent)
            self.window.grab_set()

            # إنشاء المحتوى
            self.create_content()

        except Exception as e:
            print(f"❌ خطأ في إنشاء النافذة: {e}")
            raise

    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = 1300
        height = 700
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")

    def create_content(self):
        """إنشاء محتوى النافذة"""
        # إطار رئيسي
        main_frame = ttk_bs.Frame(self.window)
        main_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)
        
        # شريط العنوان
        self.create_header(main_frame)
        
        # المحتوى الرئيسي
        content_frame = ttk_bs.Frame(main_frame)
        content_frame.pack(fill=BOTH, expand=True, pady=(20, 0))
        
        # الجانب الأيمن - المعلومات الأساسية
        self.create_basic_info(content_frame)
        
        # الجانب الأيسر - حركة الصنف
        self.create_movements_section(content_frame)
        
        # أزرار التحكم
        self.create_control_buttons(main_frame)

    def create_header(self, parent):
        """إنشاء شريط العنوان"""
        header_frame = ttk_bs.Frame(parent)
        header_frame.pack(fill=X, pady=(0, 20))
        
        # زر العودة للقائمة
        back_btn = ttk_bs.Button(
            header_frame,
            text="← العودة للقائمة",
            style="secondary.TButton",
            width=22
        )
        back_btn.pack(side=LEFT)
        
        # العنوان
        title_label = ttk_bs.Label(
            header_frame,
            text="📋 بيانات الصنف",
            font=("Arial", 18, "bold")
        )
        title_label.pack(side=RIGHT)

    def create_basic_info(self, parent):
        """إنشاء قسم المعلومات الأساسية"""
        # إطار المعلومات الأساسية
        info_frame = ttk_bs.LabelFrame(
            parent,
            text="ℹ️ المعلومات الأساسية",
            padding=20
        )
        info_frame.pack(side=RIGHT, fill=Y, padx=(10, 0))
        
        # حساب الكمية الحالية الفعلية
        current_qty = self.calculate_current_quantity()

        # بيانات الصنف
        info_data = [
            ("رقم الصنف", self.item_data.get('item_number', '')),
            ("اسم الصنف", self.item_data.get('item_name', '')),
            ("نوع العهدة", self.item_data.get('custody_type', '')),
            ("التصنيف", self.item_data.get('classification', '')),
            ("وحدة الصرف", self.item_data.get('unit', '')),
            ("الكمية الحالية", f"{int(current_qty)} عدد"),
            ("مدخل البيانات", "مدير النظام"),  # يمكن تحديثه من قاعدة البيانات
            ("تاريخ الإدخال", datetime.now().strftime("%Y-%m-%d"))  # يمكن تحديثه من قاعدة البيانات
        ]

        # حفظ مرجع لتسمية الكمية الحالية للتحديث
        self.current_qty_labels = []
        
        for i, (label, value) in enumerate(info_data):
            # إطار الحقل
            field_frame = ttk_bs.Frame(info_frame)
            field_frame.pack(fill=X, pady=8)
            
            # التسمية
            label_widget = ttk_bs.Label(
                field_frame,
                text=label,
                font=("Arial", 12),
                width=15,
                anchor=E
            )
            label_widget.pack(side=RIGHT, padx=(0, 10))
            
            # القيمة
            value_widget = ttk_bs.Label(
                field_frame,
                text=str(value),
                font=("Arial", 12, "bold"),
                foreground="#2c3e50",
                width=20,
                anchor=W
            )
            value_widget.pack(side=RIGHT)

            # حفظ مرجع لتسمية الكمية الحالية
            if label == "الكمية الحالية":
                self.current_qty_info_label = value_widget

    def create_movements_section(self, parent):
        """إنشاء قسم حركة الصنف"""
        # إطار حركة الصنف
        movements_frame = ttk_bs.LabelFrame(
            parent,
            text="🔄 حركة الصنف",
            padding=20
        )
        movements_frame.pack(side=LEFT, fill=BOTH, expand=True)
        
        # جدول الحركات
        self.create_movements_table(movements_frame)
        
        # ملخص الحركات
        self.create_movements_summary(movements_frame)

    def create_movements_table(self, parent):
        """إنشاء جدول الحركات"""
        # إطار الجدول
        table_frame = ttk_bs.Frame(parent)
        table_frame.pack(fill=BOTH, expand=True, pady=(0, 20))
        
        # أعمدة الجدول
        columns = ("date", "movement_type", "quantity", "reference", "user")
        column_names = ("التاريخ", "نوع الحركة", "الكمية", "المرجع", "مدخل البيانات")
        
        # إنشاء Treeview بطريقة آمنة
        try:
            from safe_window_manager import create_safe_treeview

            self.movements_tree = create_safe_treeview(
                table_frame,
                columns=columns,
                show="headings",
                height=8
            )

            if not self.movements_tree:
                raise Exception("فشل في إنشاء Treeview آمن")

        except Exception as e:
            print(f"تحذير: فشل في استخدام Treeview آمن: {e}")
            # إنشاء Treeview بالطريقة التقليدية كبديل
            self.movements_tree = ttk_bs.Treeview(
                table_frame,
                columns=columns,
                show="headings",
                height=8
            )
        
        # تعيين عناوين الأعمدة
        for col, name in zip(columns, column_names):
            self.movements_tree.heading(col, text=name, anchor=CENTER)
            self.movements_tree.column(col, width=120, anchor=CENTER)
        
        # شريط التمرير
        scrollbar = ttk_bs.Scrollbar(table_frame, orient=VERTICAL, command=self.movements_tree.yview)
        self.movements_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.movements_tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)

        # ربط أحداث الجدول
        self.movements_tree.bind("<Double-1>", self.on_movement_double_click)
        self.movements_tree.bind("<Button-3>", self.show_movement_context_menu)

    def create_movements_summary(self, parent):
        """إنشاء ملخص الحركات"""
        summary_frame = ttk_bs.Frame(parent)
        summary_frame.pack(fill=X, pady=(10, 0))
        
        # المجموع الفعلي
        total_frame = ttk_bs.Frame(summary_frame)
        total_frame.pack(side=RIGHT)

        # حساب الكميات الفعلية من جدول حركات المخزون
        current_qty = self.calculate_current_quantity()
        entered_qty = int(self.get_entered_quantity())
        dispensed_qty = int(self.get_dispensed_quantity())

        # إضافة
        add_label = ttk_bs.Label(
            total_frame,
            text=f"إضافة: {entered_qty} عدد",
            font=("Arial", 10, "bold"),
            foreground="#27ae60"
        )
        add_label.pack(side=RIGHT, padx=10)

        # صرف
        dispense_label = ttk_bs.Label(
            total_frame,
            text=f"صرف: {dispensed_qty} عدد",
            font=("Arial", 10, "bold"),
            foreground="#e74c3c"
        )
        dispense_label.pack(side=RIGHT, padx=10)

        # المجموع الفعلي - الرقم (على اليسار)
        self.total_value_label = ttk_bs.Label(
            total_frame,
            text=f"{int(current_qty)}",
            font=("Arial", 14, "bold"),
            foreground="#1e3a8a"  # أزرق غامق
        )
        self.total_value_label.pack(side=LEFT, padx=(5, 0))

        # المجموع الفعلي - التسمية (على اليمين)
        total_label = ttk_bs.Label(
            total_frame,
            text="المجموع الفعلي:",
            font=("Arial", 12, "bold")
        )
        total_label.pack(side=LEFT, padx=(0, 5))

        # حفظ مراجع للتحديث
        self.add_label = add_label
        self.dispense_label = dispense_label
        
        # زر عرض جميع الحركات
        view_all_btn = ttk_bs.Button(
            summary_frame,
            text="📊 عرض جميع الحركات",
            style="info.TButton",
            width=22,
            command=self.show_all_movements
        )
        view_all_btn.pack(side=LEFT)

    def create_control_buttons(self, parent):
        """إنشاء أزرار التحكم"""
        buttons_frame = ttk_bs.Frame(parent)
        buttons_frame.pack(fill=X, pady=(20, 0))

        # زر إضافة حركة مخزون
        add_movement_btn = ttk_bs.Button(
            buttons_frame,
            text="➕ إضافة حركة مخزون",
            style="success.TButton",
            width=22,
            command=self.add_movement
        )
        add_movement_btn.pack(side=LEFT, padx=(0, 10))

        # زر إغلاق
        close_btn = ttk_bs.Button(
            buttons_frame,
            text="إغلاق",
            style="secondary.TButton",
            width=15,
            command=self.window.destroy
        )
        close_btn.pack(side=RIGHT)

    def load_item_movements(self):
        """تحميل حركات الصنف من قاعدة البيانات"""
        try:
            # مسح البيانات الحالية
            for item in self.movements_tree.get_children():
                self.movements_tree.delete(item)

            # مسح قاموس معرفات الحركات
            if hasattr(self, 'movement_ids'):
                self.movement_ids.clear()

            # الحصول على رقم الصنف
            item_number = self.item_data.get('item_number', '')
            if not item_number:
                print("⚠️ لا يوجد رقم صنف لتحميل الحركات")
                return

            # تحميل حركات الصنف من قاعدة البيانات
            from database import db_manager
            from models import User

            query = """
                SELECT
                    im.id,
                    im.movement_date,
                    im.movement_type,
                    im.quantity,
                    im.organization_type,
                    im.organization_name,
                    im.notes,
                    im.user_id,
                    COALESCE(u.full_name, u.username, 'مدخل البيانات') as user_name,
                    u.username as username,
                    u.full_name as full_name
                FROM inventory_movements_new im
                LEFT JOIN users u ON im.user_id = u.id
                WHERE im.item_number = ? AND im.is_active = 1
                ORDER BY im.movement_date DESC, im.id DESC
            """

            movements = db_manager.fetch_all(query, (item_number,))

            if not movements:
                # إضافة رسالة عدم وجود حركات
                self.movements_tree.insert("", "end", values=(
                    "-", "لا توجد حركات", "-", "-", "-"
                ), tags=("no_data",))
                self.movements_tree.tag_configure("no_data", background="#f8f9fa", foreground="#6c757d")
                return

            # إضافة البيانات للجدول
            for movement in movements:
                try:
                    # تحويل sqlite3.Row إلى dict
                    movement_dict = dict(movement)

                    # تنسيق التاريخ
                    movement_date = movement_dict.get('movement_date')
                    if movement_date:
                        if isinstance(movement_date, str):
                            # تحويل التاريخ من نص إلى تاريخ
                            from datetime import datetime
                            try:
                                date_obj = datetime.fromisoformat(movement_date.replace('Z', '+00:00'))
                                formatted_date = date_obj.strftime('%Y-%m-%d')
                            except:
                                formatted_date = movement_date[:10] if len(movement_date) >= 10 else movement_date
                        elif hasattr(movement_date, 'strftime'):
                            # التأكد من أن الكائن له دالة strftime قبل استدعائها
                            try:
                                formatted_date = movement_date.strftime('%Y-%m-%d')
                            except:
                                formatted_date = str(movement_date)[:10]
                        else:
                            # إذا لم يكن نص أو تاريخ، تحويله إلى نص
                            formatted_date = str(movement_date)[:10] if str(movement_date) else "-"
                    else:
                        formatted_date = "-"

                    # تنسيق نوع الحركة
                    movement_type = movement_dict.get('movement_type') or "-"

                    # تنسيق الكمية
                    quantity = movement_dict.get('quantity')
                    if quantity is not None:
                        quantity_text = f"{int(quantity)} عدد"
                    else:
                        quantity_text = "-"

                    # تنسيق المرجع (الهيئة/الإدارة/القسم)
                    reference = "-"
                    if movement_dict.get('organization_type') and movement_dict.get('organization_name'):
                        reference = f"{movement_dict.get('organization_type')}: {movement_dict.get('organization_name')}"
                    elif movement_dict.get('notes'):
                        notes = movement_dict.get('notes')
                        reference = notes[:50] + "..." if len(notes) > 50 else notes

                    # اسم المستخدم - محاولة الحصول على الاسم الصحيح
                    user_name = self.get_user_display_name(movement_dict)

                    # تحديد لون الصف حسب نوع الحركة
                    tags = ()
                    if "إضافة" in movement_type:
                        tags = ("addition",)
                    elif "صرف" in movement_type:
                        tags = ("dispensing",)

                    # إضافة الصف للجدول مع تخزين معرف الحركة
                    item_id = self.movements_tree.insert("", "end", values=(
                        formatted_date,
                        movement_type,
                        quantity_text,
                        reference,
                        user_name
                    ), tags=tags)

                    # تخزين معرف الحركة في قاموس منفصل
                    if not hasattr(self, 'movement_ids'):
                        self.movement_ids = {}
                    self.movement_ids[item_id] = movement_dict.get('id', '')

                except Exception as e:
                    print(f"⚠️ خطأ في معالجة حركة: {e}")
                    continue

            # تعيين ألوان الصفوف
            self.movements_tree.tag_configure("addition", background="#d5f4e6")
            self.movements_tree.tag_configure("dispensing", background="#ffeaa7")

            print(f"✅ تم تحميل {len(movements)} حركة للصنف {item_number}")

        except Exception as e:
            print(f"خطأ في تحميل حركات الصنف: {e}")
            import traceback
            traceback.print_exc()

    def get_user_display_name(self, movement_dict):
        """الحصول على اسم المستخدم الصحيح للعرض"""
        try:
            # أولاً: محاولة الحصول على الاسم من الاستعلام المباشر
            user_name = movement_dict.get('user_name')
            if user_name and user_name.strip():
                return user_name.strip()

            # ثانياً: محاولة الحصول على الاسم من معرف المستخدم
            user_id = movement_dict.get('user_id')
            if user_id:
                from database import db_manager
                user_query = "SELECT full_name, username FROM users WHERE id = ?"
                user_result = db_manager.fetch_one(user_query, (user_id,))
                if user_result:
                    full_name = user_result.get('full_name')
                    username = user_result.get('username')

                    # إرجاع الاسم الكامل إذا كان متوفراً، وإلا اسم المستخدم
                    if full_name and full_name.strip():
                        return full_name.strip()
                    elif username and username.strip():
                        return username.strip()

            # ثالثاً: محاولة الحصول على المستخدم الحالي من النظام
            try:
                # البحث عن المستخدم الحالي المسجل دخوله
                current_user_query = """
                    SELECT full_name, username FROM users
                    WHERE is_active = 1
                    ORDER BY last_login DESC
                    LIMIT 1
                """
                current_user = db_manager.fetch_one(current_user_query)
                if current_user:
                    full_name = current_user.get('full_name')
                    username = current_user.get('username')

                    if full_name and full_name.strip():
                        return full_name.strip()
                    elif username and username.strip():
                        return username.strip()
            except Exception as e:
                print(f"خطأ في الحصول على المستخدم الحالي: {e}")

            # رابعاً: محاولة الحصول على اسم المستخدم من متغيرات النظام
            try:
                import os
                system_user = os.getenv('USERNAME') or os.getenv('USER')
                if system_user:
                    return f"مستخدم النظام ({system_user})"
            except:
                pass

            # أخيراً: إرجاع قيمة افتراضية
            return "مدخل البيانات"

        except Exception as e:
            print(f"خطأ في الحصول على اسم المستخدم: {e}")
            return "مدخل البيانات"

    def show_all_movements(self):
        """عرض جميع حركات الصنف"""
        messagebox.showinfo("عرض الحركات", "سيتم فتح شاشة تفصيلية لجميع حركات الصنف")

    def add_movement(self):
        """إضافة حركة مخزون"""
        try:
            # الحصول على رقم واسم الصنف من البيانات
            item_number = self.item_data.get('item_number', '')
            item_name = self.item_data.get('item_name', '')

            if not item_number:
                messagebox.showwarning("تحذير", "لا يمكن تحديد رقم الصنف")
                return

            # فتح شاشة إضافة حركة مخزون للصنف المحدد
            from ui.add_inventory_movement_window import AddInventoryMovementWindow
            movement_window = AddInventoryMovementWindow(
                self.window,  # استخدام نافذة المعاينة كـ parent
                self,  # تمرير هذه النافذة كـ main_window للتحديث
                item_number,
                item_name
            )

            # تسجيل هذه النافذة للتحديث التلقائي
            from ui.add_inventory_movement_window import register_inventory_window
            register_inventory_window(self)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح شاشة إضافة حركة المخزون: {e}")
            print(f"خطأ في فتح شاشة الحركة: {e}")
            import traceback
            traceback.print_exc()

    def calculate_current_quantity(self):
        """حساب الكمية الحالية الفعلية من جدول حركات المخزون"""
        try:
            from database import db_manager

            item_number = self.item_data.get('item_number', '')
            if not item_number:
                return 0

            # حساب إجمالي الكميات المدخلة
            total_in = db_manager.fetch_one("""
                SELECT COALESCE(SUM(quantity), 0) FROM inventory_movements_new
                WHERE item_number = ? AND movement_type = 'إضافة' AND is_active = 1
            """, (item_number,))
            total_in = total_in[0] if total_in else 0

            # حساب إجمالي الكميات المصروفة
            total_out = db_manager.fetch_one("""
                SELECT COALESCE(SUM(quantity), 0) FROM inventory_movements_new
                WHERE item_number = ? AND movement_type = 'صرف' AND is_active = 1
            """, (item_number,))
            total_out = total_out[0] if total_out else 0

            # الكمية الحالية = المدخلة - المصروفة
            current_qty = total_in - total_out

            print(f"📊 حساب الكمية للصنف {item_number}: مدخلة={total_in}, مصروفة={total_out}, حالية={current_qty}")

            return max(0, current_qty)  # تأكد من عدم كون الكمية سالبة

        except Exception as e:
            print(f"خطأ في حساب الكمية الحالية: {e}")
            return 0

    def refresh_data(self):
        """تحديث البيانات"""
        self.load_item_movements()
        # تحديث عرض الكمية الحالية
        self.update_current_quantity_display()
        print(f"🔄 تم تحديث بيانات الصنف {self.item_data.get('item_number', 'غير محدد')}")

    def update_current_quantity_display(self):
        """تحديث عرض الكمية الحالية في المعلومات الأساسية"""
        try:
            # حساب الكميات المحدثة
            current_qty = self.calculate_current_quantity()
            entered_qty = self.get_entered_quantity()
            dispensed_qty = self.get_dispensed_quantity()

            # تحديث المجموع الفعلي
            if hasattr(self, 'total_value_label'):
                self.total_value_label.config(text=f"{int(current_qty)}")

            # تحديث كمية الإضافة
            if hasattr(self, 'add_label'):
                self.add_label.config(text=f"إضافة: {int(entered_qty)} عدد")

            # تحديث كمية الصرف
            if hasattr(self, 'dispense_label'):
                self.dispense_label.config(text=f"صرف: {int(dispensed_qty)} عدد")

            # تحديث الكمية الحالية في قسم المعلومات الأساسية
            if hasattr(self, 'current_qty_info_label'):
                self.current_qty_info_label.config(text=f"{int(current_qty)} عدد")

            print(f"✅ تم تحديث عرض الكميات: حالية={current_qty}, مدخلة={entered_qty}, مصروفة={dispensed_qty}")

        except Exception as e:
            print(f"خطأ في تحديث عرض الكمية الحالية: {e}")
            import traceback
            traceback.print_exc()

    def get_entered_quantity(self):
        """حساب الكمية المدخلة من جدول حركات المخزون"""
        try:
            from database import db_manager

            item_number = self.item_data.get('item_number', '')
            if not item_number:
                return 0

            # حساب إجمالي الكميات المدخلة
            total_in = db_manager.fetch_one("""
                SELECT COALESCE(SUM(quantity), 0) FROM inventory_movements_new
                WHERE item_number = ? AND movement_type = 'إضافة' AND is_active = 1
            """, (item_number,))

            return total_in[0] if total_in else 0

        except Exception as e:
            print(f"خطأ في حساب الكمية المدخلة: {e}")
            return 0

    def get_dispensed_quantity(self):
        """حساب الكمية المصروفة من جدول حركات المخزون"""
        try:
            from database import db_manager

            item_number = self.item_data.get('item_number', '')
            if not item_number:
                return 0

            # حساب إجمالي الكميات المصروفة
            total_out = db_manager.fetch_one("""
                SELECT COALESCE(SUM(quantity), 0) FROM inventory_movements_new
                WHERE item_number = ? AND movement_type = 'صرف' AND is_active = 1
            """, (item_number,))

            return total_out[0] if total_out else 0

        except Exception as e:
            print(f"خطأ في حساب الكمية المصروفة: {e}")
            return 0

    def load_items_data(self):
        """دالة للتوافق مع نظام التحديث التلقائي"""
        self.refresh_data()

    def auto_refresh(self):
        """تحديث تلقائي كل 5 ثوان"""
        try:
            # التحقق من صحة النافذة قبل التحديث
            if not hasattr(self, 'window') or not self.window:
                return

            if not hasattr(self.window, 'winfo_exists'):
                return

            if not self.window.winfo_exists():
                return

            self.refresh_data()

            # جدولة التحديث التالي مع فحص إضافي
            try:
                self.window.after(5000, self.auto_refresh)
            except Exception as e:
                print(f"تحذير في جدولة التحديث: {e}")

        except Exception as e:
            print(f"خطأ في التحديث التلقائي: {e}")

    def start_auto_refresh(self):
        """بدء التحديث التلقائي"""
        try:
            if hasattr(self, 'window') and self.window and hasattr(self.window, 'after'):
                self.window.after(1000, self.auto_refresh)  # بدء بعد ثانية واحدة
        except Exception as e:
            print(f"تحذير في بدء التحديث التلقائي: {e}")

    def on_movement_double_click(self, event):
        """التعامل مع النقر المزدوج على حركة"""
        self.edit_selected_movement()

    def show_movement_context_menu(self, event):
        """عرض القائمة المنبثقة للحركات"""
        selection = self.movements_tree.selection()
        if selection:
            # التحقق من أن الصف ليس رسالة "لا توجد حركات"
            item = self.movements_tree.item(selection[0])
            values = item['values']
            if len(values) > 1 and values[1] != "لا توجد حركات":
                context_menu = tk.Menu(self.window, tearoff=0)
                context_menu.add_command(label="📝 تعديل الحركة", command=self.edit_selected_movement)
                context_menu.add_command(label="🗑️ حذف الحركة", command=self.delete_selected_movement)

                try:
                    context_menu.tk_popup(event.x_root, event.y_root)
                finally:
                    context_menu.grab_release()

    def edit_selected_movement(self):
        """تعديل الحركة المحددة"""
        selection = self.movements_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار حركة للتعديل")
            return

        try:
            # الحصول على معرف الحركة
            item = self.movements_tree.item(selection[0])
            item_id = selection[0]
            movement_id = getattr(self, 'movement_ids', {}).get(item_id, '')

            if not movement_id:
                messagebox.showwarning("تحذير", "لا يمكن تحديد معرف الحركة")
                return

            # التحقق من أن الصف ليس رسالة "لا توجد حركات"
            values = item['values']
            if len(values) > 1 and values[1] == "لا توجد حركات":
                messagebox.showwarning("تحذير", "لا يمكن تعديل هذا العنصر")
                return

            # فتح شاشة تعديل الحركة
            from ui.edit_inventory_movement_window import EditInventoryMovementWindow
            EditInventoryMovementWindow(self.window, self, movement_id)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح شاشة تعديل الحركة: {e}")
            print(f"خطأ في تعديل الحركة: {e}")
            import traceback
            traceback.print_exc()

    def delete_selected_movement(self):
        """حذف الحركة المحددة"""
        selection = self.movements_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار حركة للحذف")
            return

        try:
            # الحصول على معرف الحركة
            item_id = selection[0]
            movement_id = getattr(self, 'movement_ids', {}).get(item_id, '')

            if not movement_id:
                messagebox.showwarning("تحذير", "لا يمكن تحديد معرف الحركة")
                return

            # تأكيد الحذف
            if messagebox.askyesno("تأكيد الحذف", "هل تريد حذف هذه الحركة نهائياً؟"):
                from database import db_manager

                # حذف الحركة من قاعدة البيانات
                delete_query = "DELETE FROM inventory_movements_new WHERE id = ?"
                if db_manager.execute_query(delete_query, (movement_id,)):
                    messagebox.showinfo("نجح", "تم حذف الحركة بنجاح")

                    # تحديث الجدول
                    self.refresh_data()

                    # تحديث النوافذ الأخرى
                    if self.refresh_callback:
                        self.refresh_callback()
                else:
                    messagebox.showerror("خطأ", "فشل في حذف الحركة")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حذف الحركة: {e}")
            print(f"خطأ في حذف الحركة: {e}")
            import traceback
            traceback.print_exc()
