"""
شاشة إدارة الأصناف - تطبيق إدارة المخازن
Items Management Window - Desktop Stores Management System
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog, filedialog
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime, date
from typing import List, Optional
import pandas as pd
import os
from pathlib import Path

from config import APP_CONFIG, UI_CONFIG, get_message
from models import Item, Category, OrganizationalChart, AddedItem

class InventoryWindow:
    """نافذة إدارة الأصناف"""

    def __init__(self, parent, main_window):
        self.parent = parent
        self.main_window = main_window
        self.items_data = []
        self.filtered_data = []
        self.current_item = None

        # متغيرات البحث والفلترة
        self.search_var = tk.StringVar()
        self.category_filter_var = tk.StringVar()
        self.status_filter_var = tk.StringVar()
        self.stock_filter_var = tk.StringVar()

        # ربط أحداث البحث
        self.search_var.trace('w', self.on_search_change)
        self.category_filter_var.trace('w', self.on_filter_change)
        self.status_filter_var.trace('w', self.on_filter_change)
        self.stock_filter_var.trace('w', self.on_filter_change)

        self.setup_inventory_interface()
        self.load_items_data()

        # تفعيل مفاتيح الاختصار العامة
        self.setup_shortcuts()

    def setup_inventory_interface(self):
        """إعداد واجهة الأصناف"""
        # مسح المحتوى الحالي
        self.main_window.clear_main_content()

        # تحديث شريط الحالة
        if hasattr(self.main_window, 'status_var'):
            self.main_window.status_var.set("إدارة الأصناف")

        # العنوان الرئيسي
        header_frame = ttk_bs.Frame(self.main_window.main_frame)
        header_frame.pack(fill=X, pady=10)

        ttk_bs.Label(
            header_frame,
            text="📦 إدارة الأصناف",
            bootstyle="primary"
        ).pack(side=LEFT)

        # أزرار العمليات
        buttons_frame = ttk_bs.Frame(header_frame)
        buttons_frame.pack(side=RIGHT)

        ttk_bs.Button(
            buttons_frame,
            text="➕ إضافة صنف",
            command=self.add_item,
            bootstyle="success",
            width=18
        ).pack(side=LEFT, padx=2)

        ttk_bs.Button(
            buttons_frame,
            text="✏️ تعديل",
            command=self.edit_item,
            bootstyle="warning",
            width=15
        ).pack(side=LEFT, padx=2)

        ttk_bs.Button(
            buttons_frame,
            text="🗑️ حذف",
            command=self.delete_item,
            bootstyle="danger",
            width=18
        ).pack(side=LEFT, padx=2)

        ttk_bs.Button(
            buttons_frame,
            text="📊 تقرير",
            command=self.generate_report,
            bootstyle="info",
            width=15
        ).pack(side=LEFT, padx=2)

        ttk_bs.Button(
            buttons_frame,
            text="➕ إضافة حركة",
            command=self.add_movement_for_selected,
            bootstyle="info",
            width=20
        ).pack(side=LEFT, padx=2)

        ttk_bs.Button(
            buttons_frame,
            text="🔄 تحديث",
            command=self.refresh_data,
            bootstyle="secondary",
            width=15
        ).pack(side=LEFT, padx=2)

        ttk_bs.Button(
            buttons_frame,
            text="🗑️ حذف جميع البيانات",
            command=self.delete_all_data,
            bootstyle="danger",
            width=25
        ).pack(side=LEFT, padx=2)

        ttk_bs.Button(
            buttons_frame,
            text="📥 استيراد إكسل",
            command=self.import_excel,
            bootstyle="primary",
            width=18
        ).pack(side=LEFT, padx=2)

        ttk_bs.Button(
            buttons_frame,
            text="📋 نموذج البيانات",
            command=self.download_template,
            bootstyle="light",
            width=18
        ).pack(side=LEFT, padx=2)

        ttk_bs.Button(
            buttons_frame,
            text="📤 تصدير إكسل",
            command=self.export_excel,
            bootstyle="success",
            width=18
        ).pack(side=LEFT, padx=2)

        # شريط البحث والفلترة
        self.create_search_filter_bar()

        # جدول الأصناف
        self.create_items_table()

        # شريط المعلومات
        self.create_info_bar()

        # تحميل البيانات عند فتح النافذة
        self.load_items_data()
        print("✅ تم تحميل شاشة إدارة الأصناف")

        # تسجيل النافذة في النظام العام
        try:
            from ui.add_inventory_movement_window import register_inventory_window
            register_inventory_window(self)
        except Exception as e:
            print(f"⚠️ خطأ في تسجيل نافذة إدارة الأصناف: {e}")

    def create_search_filter_bar(self):
        """إنشاء شريط البحث والفلترة"""
        search_frame = ttk_bs.Frame(self.main_window.main_frame)
        search_frame.pack(fill=X, pady=10)

        # البحث
        ttk_bs.Label(search_frame, text="🔍 البحث:").pack(side=LEFT, padx=5)
        search_entry = ttk_bs.Entry(
            search_frame,
            textvariable=self.search_var,
            width=25
        )
        search_entry.pack(side=LEFT, padx=5)

        # فلتر الفئة
        ttk_bs.Label(search_frame, text="الفئة:").pack(side=LEFT, padx=(20, 5))
        category_combo = ttk_bs.Combobox(
            search_frame,
            textvariable=self.category_filter_var,
            width=20,
            state="readonly"
        )
        category_combo.pack(side=LEFT, padx=5)

        # فلتر الحالة
        ttk_bs.Label(search_frame, text="الحالة:").pack(side=LEFT, padx=(20, 5))
        status_combo = ttk_bs.Combobox(
            search_frame,
            textvariable=self.status_filter_var,
            values=["الكل", "نشط", "غير نشط"],
            width=15,
            state="readonly"
        )
        status_combo.set("الكل")
        status_combo.pack(side=LEFT, padx=5)

        # فلتر المخزون
        ttk_bs.Label(search_frame, text="المخزون:").pack(side=LEFT, padx=(20, 5))
        stock_combo = ttk_bs.Combobox(
            search_frame,
            textvariable=self.stock_filter_var,
            values=["الكل", "منخفض", "متوفر", "نفد"],
            width=15,
            state="readonly"
        )
        stock_combo.set("الكل")
        stock_combo.pack(side=LEFT, padx=5)

        # تحميل قائمة الفئات
        self.load_categories_filter(category_combo)

    def create_items_table(self):
        """إنشاء جدول الأصناف"""
        table_frame = ttk_bs.Frame(self.main_window.main_frame)
        table_frame.pack(fill=BOTH, expand=True, pady=10)

        # أعمدة الجدول - بدون خانة الكمية المدخلة
        columns = ("id", "item_number", "item_name", "custody_type", "classification", "current_quantity")
        column_names = ("الرقم", "رقم الصنف", "اسم الصنف", "نوع العهدة", "التصنيف", "الكمية الحالية")

        self.tree = ttk_bs.Treeview(
            table_frame,
            columns=columns,
            show="headings",
            height=15
        )

        # تعيين عناوين الأعمدة
        for col, name in zip(columns, column_names):
            self.tree.heading(col, text=name)
            if col == "id":
                self.tree.column(col, width=80, anchor=CENTER)
            elif col == "item_number":
                self.tree.column(col, width=150, anchor=CENTER)
            elif col == "item_name":
                self.tree.column(col, width=250, anchor=E)
            elif col == "custody_type":
                self.tree.column(col, width=150, anchor=E)
            elif col == "classification":
                self.tree.column(col, width=200, anchor=E)
            elif col == "current_quantity":
                self.tree.column(col, width=120, anchor=CENTER)
            elif col == "entered_quantity":
                self.tree.column(col, width=120, anchor=CENTER)

        # شريط التمرير
        scrollbar = ttk_bs.Scrollbar(table_frame, orient=VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)

        # تخطيط الجدول
        self.tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)

        # ربط أحداث الجدول
        self.tree.bind("<Double-1>", self.on_item_double_click)
        self.tree.bind("<<TreeviewSelect>>", self.on_item_select)
        self.tree.bind("<Button-3>", self.show_context_menu)  # Right click

        # إنشاء القائمة المنبثقة
        self.create_context_menu()

    def create_info_bar(self):
        """إنشاء شريط المعلومات"""
        info_frame = ttk_bs.Frame(self.main_window.main_frame)
        info_frame.pack(fill=X, pady=5)

        self.info_label = ttk_bs.Label(
            info_frame,
            text="جاري تحميل البيانات...",
            bootstyle="secondary"
        )
        self.info_label.pack(side=LEFT)

        # معلومات الإحصائيات
        self.stats_label = ttk_bs.Label(
            info_frame,
            text="",
            bootstyle="info"
        )
        self.stats_label.pack(side=RIGHT)

    def create_context_menu(self):
        """إنشاء القائمة المنبثقة"""
        self.context_menu = tk.Menu(self.tree, tearoff=0)

        # إضافة عناصر القائمة
        self.context_menu.add_command(
            label="➕ إضافة حركة مخزون",
            command=self.add_movement_for_selected
        )

        self.context_menu.add_separator()

        self.context_menu.add_command(
            label="👁️ عرض بيانات الصنف",
            command=self.show_item_details
        )

        self.context_menu.add_command(
            label="✏️ تعديل الصنف",
            command=self.edit_item_context
        )

        self.context_menu.add_command(
            label="🗑️ حذف الصنف",
            command=self.delete_item_context
        )

    def show_context_menu(self, event):
        """عرض القائمة المنبثقة"""
        # تحديد العنصر المحدد
        item = self.tree.identify_row(event.y)
        if item:
            self.tree.selection_set(item)
            self.tree.focus(item)
            # تحديث العنصر الحالي
            self.on_item_select(None)
            # عرض القائمة
            try:
                self.context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                self.context_menu.grab_release()

    def edit_item_context(self):
        """تعديل الصنف من القائمة المنبثقة"""
        self.edit_item()

    def delete_item_context(self):
        """حذف الصنف من القائمة المنبثقة"""
        self.delete_item()

    def show_item_details(self):
        """عرض بيانات الصنف المحدد"""
        if not self.current_item:
            messagebox.showwarning("تحذير", "يرجى اختيار صنف لعرض بياناته")
            return

        try:
            # تحضير بيانات الصنف لنافذة المعاينة (بأسماء الحقول المتوقعة)
            item_data = {
                'id': self.current_item.id,
                'item_number': self.current_item.item_number,
                'item_name': self.current_item.name,  # استخدام item_name بدلاً من name
                'custody_type': self.current_item.custody_type,
                'classification': self.current_item.classification,
                'unit': self.current_item.unit,
                'current_qty': int(getattr(self.current_item, 'current_quantity', 0)),  # استخدام current_qty
                'entered_qty': int(getattr(self.current_item, 'entered_quantity', 0)),  # استخدام entered_qty
                'dispensed_qty': int(getattr(self.current_item, 'dispensed_quantity', 0)),  # استخدام dispensed_qty
                'data_entry_user': self.current_item.data_entry_user,
                'entry_date': self.current_item.entry_date,
                'is_active': self.current_item.is_active
            }

            # فتح نافذة معاينة الصنف
            from ui.item_preview_window import ItemPreviewWindow
            ItemPreviewWindow(self.parent, item_data)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في عرض بيانات الصنف: {e}")
            print(f"خطأ في عرض بيانات الصنف: {e}")
            import traceback
            traceback.print_exc()

    def load_categories_filter(self, combo):
        """تحميل قائمة الفئات للفلتر"""
        try:
            categories = Category.get_all() if hasattr(Category, 'get_all') else []
            cat_names = ["الكل"] + [cat.name for cat in categories]
            combo['values'] = cat_names
            combo.set("الكل")
        except Exception as e:
            print(f"خطأ في تحميل الفئات: {e}")
            combo['values'] = ["الكل"]
            combo.set("الكل")

    def load_items_data(self):
        """تحميل بيانات الأصناف المضافة فقط"""
        try:
            print("📊 بدء تحميل بيانات الأصناف (النافذة الرئيسية)...")
            
            # تحميل البيانات من AddedItem (الأصناف المضافة من شاشة إضافة صنف جديد)
            added_items = AddedItem.get_all()
            print(f"📊 تم تحميل {len(added_items)} صنف من قاعدة البيانات")

            # تحويل بيانات AddedItem إلى تنسيق للعرض
            self.items_data = []
            for added_item in added_items:
                # إنشاء كائن للعرض
                display_item = type('DisplayItem', (), {})()
                display_item.id = added_item.id
                display_item.item_number = added_item.item_number
                display_item.name = added_item.item_name
                display_item.item_name = added_item.item_name  # للتوافق
                display_item.custody_type = added_item.custody_type
                display_item.classification = added_item.classification
                display_item.unit = added_item.unit
                display_item.data_entry_user = added_item.data_entry_user
                display_item.entry_date = added_item.entry_date
                display_item.is_active = added_item.is_active
                display_item.created_at = added_item.created_at
                display_item.updated_at = added_item.updated_at

                # استخدام الكميات المحفوظة مباشرة من قاعدة البيانات
                display_item.current_quantity = added_item.current_quantity
                display_item.entered_quantity = added_item.entered_quantity

                # حساب الكمية المصروفة من حركات المخزون (إضافية)
                dispensed_qty = self.get_dispensed_quantity(added_item.item_number)
                display_item.dispensed_quantity = dispensed_qty

                self.items_data.append(display_item)

            # تحديث البيانات المفلترة
            self.filtered_data = self.items_data.copy()
            
            # تحديث الجدول وشريط المعلومات
            self.update_table()
            self.update_info_bar()
            
            print(f"✅ تم تحميل {len(self.items_data)} صنف في النافذة الرئيسية")
            
        except Exception as e:
            print(f"❌ خطأ في تحميل بيانات الأصناف: {e}")
            import traceback
            print(f"تفاصيل الخطأ: {traceback.format_exc()}")
            self.items_data = []
            self.filtered_data = []
            self.update_table()
            self.update_info_bar()

    def get_dispensed_quantity(self, item_number):
        """حساب الكمية المصروفة لصنف معين"""
        try:
            # البحث في جدول حركات المخزون الجديد عن الكميات المصروفة
            from database import db_manager

            query = """
                SELECT COALESCE(SUM(quantity), 0) as total_dispensed
                FROM inventory_movements_new
                WHERE item_number = ? AND movement_type = 'صرف' AND is_active = 1
            """

            result = db_manager.fetch_one(query, (item_number,))

            if result and result['total_dispensed']:
                return float(result['total_dispensed'])
            else:
                return 0.0

        except Exception as e:
            print(f"خطأ في حساب الكمية المصروفة: {e}")
            return 0.0

    def get_entered_quantity(self, item_number):
        """حساب الكمية المدخلة لصنف معين"""
        try:
            # البحث في جدول حركات المخزون الجديد عن الكميات المدخلة
            from database import db_manager

            query = """
                SELECT COALESCE(SUM(quantity), 0) as total_entered
                FROM inventory_movements_new
                WHERE item_number = ? AND movement_type = 'إضافة' AND is_active = 1
            """

            result = db_manager.fetch_one(query, (item_number,))

            if result and result['total_entered']:
                return float(result['total_entered'])
            else:
                return 0.0

        except Exception as e:
            print(f"خطأ في حساب الكمية المدخلة: {e}")
            return 0.0

    def update_table(self):
        """تحديث جدول الأصناف"""
        # التحقق من وجود الجدول
        if not hasattr(self, 'tree') or self.tree is None:
            return

        # مسح البيانات الحالية
        for item in self.tree.get_children():
            self.tree.delete(item)

        # إضافة البيانات المفلترة
        for item in self.filtered_data:
            # تحديد لون الصف حسب مستوى المخزون
            tags = []
            if hasattr(item, 'current_quantity') and item.current_quantity <= 5:
                tags.append("low_stock")
            elif hasattr(item, 'current_quantity') and item.current_quantity == 0:
                tags.append("out_of_stock")

            item_id = self.tree.insert("", "end", values=(
                getattr(item, 'id', ''),
                getattr(item, 'item_number', ''),
                getattr(item, 'item_name', ''),
                getattr(item, 'custody_type', ''),
                getattr(item, 'classification', ''),
                int(getattr(item, 'current_quantity', 0))   # الكمية الحالية فقط
            ), tags=tags)

        # تعيين ألوان الصفوف
        self.tree.tag_configure("low_stock", background="#fff3cd")
        self.tree.tag_configure("out_of_stock", background="#f8d7da")

    def update_info_bar(self):
        """تحديث شريط المعلومات"""
        # التحقق من وجود عناصر شريط المعلومات
        if not hasattr(self, 'info_label') or not hasattr(self, 'stats_label'):
            return

        total = len(self.items_data)
        filtered = len(self.filtered_data)
        active = len([i for i in self.items_data if getattr(i, 'is_active', True)])
        # للأصناف المضافة، نعتبر الكمية منخفضة إذا كانت <= 5
        low_stock = len([i for i in self.items_data if getattr(i, 'current_quantity', 0) <= 5])
        out_of_stock = len([i for i in self.items_data if getattr(i, 'current_quantity', 0) == 0])

        if hasattr(self, 'info_label'):
            self.info_label.config(text=f"عرض {filtered} من {total} صنف")
        if hasattr(self, 'stats_label'):
            self.stats_label.config(text=f"النشطة: {active} | منخفضة: {low_stock} | نفدت: {out_of_stock}")

    def on_search_change(self, *args):
        """معالج تغيير البحث"""
        self.apply_filters()

    def on_filter_change(self, *args):
        """معالج تغيير الفلاتر"""
        self.apply_filters()

    def apply_filters(self):
        """تطبيق الفلاتر والبحث"""
        search_text = self.search_var.get().lower()
        category_filter = self.category_filter_var.get()
        status_filter = self.status_filter_var.get()
        stock_filter = self.stock_filter_var.get()

        self.filtered_data = []

        for item in self.items_data:
            # فلتر البحث
            if search_text:
                if not any(search_text in str(getattr(item, field, "")).lower()
                          for field in ['name', 'item_number', 'custody_type', 'classification']):
                    continue

            # فلتر الفئة
            if category_filter and category_filter != "الكل":
                try:
                    if item.category_id and hasattr(Category, 'get_by_id'):
                        cat = Category.get_by_id(item.category_id)
                        if not cat or cat.name != category_filter:
                            continue
                    else:
                        continue
                except:
                    continue

            # فلتر الحالة
            if status_filter and status_filter != "الكل":
                if status_filter == "نشط" and not item.is_active:
                    continue
                elif status_filter == "غير نشط" and item.is_active:
                    continue

            # فلتر المخزون
            if stock_filter and stock_filter != "الكل":
                current_qty = getattr(item, 'current_quantity', 0)
                if stock_filter == "منخفض" and current_qty > 5:  # للأصناف المضافة نعتبر 5 هو الحد الأدنى
                    continue
                elif stock_filter == "متوفر" and current_qty <= 5:
                    continue
                elif stock_filter == "نفد" and current_qty > 0:
                    continue

            self.filtered_data.append(item)

        self.update_table()
        self.update_info_bar()

    def on_item_select(self, event):
        """معالج اختيار عنصر من الجدول"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            item_id = item['values'][0]
            self.current_item = next(
                (i for i in self.items_data if i.id == item_id), None
            )

    def on_item_double_click(self, event):
        """معالج النقر المزدوج على عنصر"""
        self.edit_item()

    def add_item(self):
        """إضافة صنف جديد"""
        try:
            from ui.add_item_window import AddItemWindow
            # تمرير مرجع لهذه النافذة لتحديث البيانات
            add_window = AddItemWindow(self.parent, self)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح شاشة إضافة الصنف: {e}")

    def edit_item(self):
        """تعديل الصنف المحدد"""
        if not self.current_item:
            messagebox.showwarning("تحذير", "يرجى اختيار صنف للتعديل")
            return

        try:
            from ui.edit_item_window import EditItemWindow
            # تمرير مرجع لهذه النافذة لتحديث البيانات
            edit_window = EditItemWindow(self.parent, self, self.current_item)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح شاشة تعديل الصنف: {e}")

    def check_item_can_be_deleted(self):
        """التحقق من إمكانية حذف الصنف"""
        try:
            from database import db_manager

            # التحقق من وجود عمليات صرف فعلية مرتبطة بالصنف
            # البحث في جدول transaction_items مع التحقق من الكمية المصروفة
            transactions_count = db_manager.fetch_one("""
                SELECT COUNT(DISTINCT ti.transaction_id) as count,
                       COALESCE(SUM(ti.quantity), 0) as total_dispensed
                FROM transaction_items ti
                INNER JOIN transactions t ON ti.transaction_id = t.id
                WHERE ti.item_id = ? AND t.status = 'مكتمل'
            """, (self.current_item.id,))

            # التحقق من وجود حركات مخزون صرف فعلية
            inventory_movements = db_manager.fetch_one("""
                SELECT COUNT(*) as count,
                       COALESCE(SUM(quantity), 0) as total_dispensed
                FROM inventory_movements
                WHERE item_id = ? AND movement_type IN ('صرف', 'إرجاع')
            """, (self.current_item.id,))

            # إذا كان هناك عمليات صرف فعلية أو حركات مخزون
            has_actual_transactions = (
                (transactions_count and transactions_count['count'] > 0 and transactions_count['total_dispensed'] > 0) or
                (inventory_movements and inventory_movements['count'] > 0 and inventory_movements['total_dispensed'] > 0)
            )

            if has_actual_transactions:
                # عرض رسالة خطأ باللون الأحمر
                self.show_error_message("الصنف له عمليات صرف فعلية ولا يمكن حذفه نهائياً")
                return False

            # التحقق من وجود مراجع في جداول أخرى (اختياري)
            # يمكن حذف الصنف إذا كان مسجل فقط بدون عمليات صرف فعلية
            print(f"✅ يمكن حذف الصنف {self.current_item.item_number} - لا توجد عمليات صرف فعلية")
            return True

        except Exception as e:
            print(f"خطأ في التحقق من إمكانية الحذف: {e}")
            # في حالة الخطأ، نسمح بالحذف مع تحذير
            if messagebox.askyesno(
                "تحذير", 
                f"حدث خطأ في التحقق من إمكانية الحذف:\n{e}\n\nهل تريد المتابعة مع ذلك؟"
            ):
                return True
            return False

    def show_error_message(self, message):
        """عرض رسالة خطأ باللون الأحمر تختفي بعد 3 ثواني"""
        # إنشاء نافذة رسالة مؤقتة
        error_window = tk.Toplevel(self.parent)
        error_window.title("تحذير")
        error_window.configure(bg='white')
        error_window.resizable(False, False)

        # تحديد أبعاد النافذة
        window_width = 400
        window_height = 100

        # توسيط النافذة في وسط الشاشة
        error_window.update_idletasks()
        screen_width = error_window.winfo_screenwidth()
        screen_height = error_window.winfo_screenheight()
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        error_window.geometry(f"{window_width}x{window_height}+{x}+{y}")
        error_window.transient(self.parent)
        error_window.grab_set()

        # إنشاء إطار للرسالة
        message_frame = tk.Frame(error_window, bg='white', padx=20, pady=20)
        message_frame.pack(fill=tk.BOTH, expand=True)

        # عرض الرسالة باللون الأحمر
        message_label = tk.Label(
            message_frame,
            text=message,
            font=("Arial", 12, "bold"),
            fg="red",
            bg="white",
            wraplength=350,
            justify="center"
        )
        message_label.pack(expand=True)

        # إغلاق النافذة بعد 3 ثواني
        error_window.after(3000, error_window.destroy)

        # إغلاق النافذة عند النقر في أي مكان
        def close_on_click(event):
            error_window.destroy()

        error_window.bind("<Button-1>", close_on_click)
        message_label.bind("<Button-1>", close_on_click)

    def delete_item(self):
        """حذف الصنف المحدد"""
        if not self.current_item:
            messagebox.showwarning("تحذير", "يرجى اختيار صنف للحذف")
            return

        # التحقق من إمكانية حذف الصنف
        can_delete = self.check_item_can_be_deleted()
        
        if not can_delete:
            # إذا لم يكن بإمكان الحذف العادي، اعرض خيار الحذف القسري
            if messagebox.askyesno(
                "حذف قسري؟",
                f"الصنف '{self.current_item.name}' له مراجع في النظام.\n\n"
                "هل تريد الحذف القسري؟\n"
                "⚠️ تحذير: سيتم حذف جميع المراجع المرتبطة بهذا الصنف"
            ):
                self.force_delete_item()
            return

        # الحذف العادي
        if messagebox.askyesno(
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف الصنف '{self.current_item.name}'؟\n\nهذا الإجراء لا يمكن التراجع عنه."
        ):
            try:
                # حذف من جدول الأصناف المضافة
                from database import db_manager
                db_manager.execute_query("DELETE FROM added_items WHERE id = ?", (self.current_item.id,))
                messagebox.showinfo("نجح", "تم حذف الصنف بنجاح")
                self.refresh_data()
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ: {e}")

    def force_delete_item(self):
        """حذف قسري للصنف مع جميع المراجع"""
        try:
            from database import db_manager
            
            print(f"🗑️ بدء الحذف القسري للصنف: {self.current_item.item_number}")
            
            # حذف من جميع الجداول المرتبطة
            tables_to_clean = [
                ("transaction_items", "item_id", self.current_item.id),
                ("inventory_movements", "item_id", self.current_item.id),
                ("added_items", "id", self.current_item.id)
            ]
            
            deleted_records = 0
            for table, column, value in tables_to_clean:
                try:
                    # عد السجلات قبل الحذف
                    count_result = db_manager.fetch_one(f"SELECT COUNT(*) as count FROM {table} WHERE {column} = ?", (value,))
                    count = count_result['count'] if count_result else 0
                    
                    if count > 0:
                        # حذف السجلات
                        db_manager.execute_query(f"DELETE FROM {table} WHERE {column} = ?", (value,))
                        deleted_records += count
                        print(f"✅ تم حذف {count} سجل من جدول {table}")
                    
                except Exception as table_error:
                    print(f"⚠️ خطأ في حذف من جدول {table}: {table_error}")
                    continue
            
            print(f"✅ تم حذف {deleted_records} سجل إجمالي")
            
            messagebox.showinfo(
                "تم الحذف القسري", 
                f"تم حذف الصنف '{self.current_item.name}' قسرياً\n"
                f"تم حذف {deleted_records} سجل مرتبط"
            )
            
            self.refresh_data()
            
        except Exception as e:
            error_msg = f"فشل في الحذف القسري: {e}"
            print(f"❌ {error_msg}")
            messagebox.showerror("خطأ في الحذف القسري", error_msg)

    def generate_report(self):
        """إنشاء تقرير المخزون مع عرض البيانات"""
        try:
            print("📊 إنشاء تقرير المخزون...")
            
            # التحقق من وجود البيانات
            if not hasattr(self, 'items_data') or not self.items_data:
                messagebox.showwarning("لا توجد بيانات", "لا توجد أصناف لإنشاء التقرير")
                return
            
            # إنشاء نافذة التقرير
            report_window = tk.Toplevel(self.parent)
            report_window.title("📊 تقرير الأصناف المفصل")
            report_window.geometry("1000x700")
            report_window.resizable(True, True)
            
            # توسيط النافذة
            report_window.update_idletasks()
            x = (report_window.winfo_screenwidth() - 1000) // 2
            y = (report_window.winfo_screenheight() - 700) // 2
            report_window.geometry(f"1000x700+{x}+{y}")

            # الإطار الرئيسي
            main_frame = ttk_bs.Frame(report_window)
            main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)

            # عنوان التقرير
            header_frame = ttk_bs.Frame(main_frame)
            header_frame.pack(fill=X, pady=(0, 10))
            
            ttk_bs.Label(
                header_frame,
                text="📊 تقرير الأصناف المفصل",
                font=("Arial", 16, "bold"),
                bootstyle="primary"
            ).pack(side=LEFT)
            
            # تاريخ التقرير
            ttk_bs.Label(
                header_frame,
                text=f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}",
                bootstyle="secondary"
            ).pack(side=RIGHT)

            # إحصائيات سريعة
            stats_frame = ttk_bs.LabelFrame(main_frame, text="📈 الإحصائيات العامة", padding=10)
            stats_frame.pack(fill=X, pady=(0, 10))
            
            # حساب الإحصائيات
            total_items = len(self.items_data)
            active_items = len([i for i in self.items_data if getattr(i, 'is_active', True)])
            inactive_items = total_items - active_items
            zero_quantity = len([i for i in self.items_data if getattr(i, 'current_quantity', 0) == 0])
            low_quantity = len([i for i in self.items_data if 0 < getattr(i, 'current_quantity', 0) <= 5])
            
            # عرض الإحصائيات في شبكة
            stats_grid = ttk_bs.Frame(stats_frame)
            stats_grid.pack(fill=X)
            
            # الصف الأول
            ttk_bs.Label(stats_grid, text=f"إجمالي الأصناف: {total_items}", bootstyle="info").grid(row=0, column=0, sticky=W, padx=10)
            ttk_bs.Label(stats_grid, text=f"الأصناف النشطة: {active_items}", bootstyle="success").grid(row=0, column=1, sticky=W, padx=10)
            ttk_bs.Label(stats_grid, text=f"الأصناف غير النشطة: {inactive_items}", bootstyle="warning").grid(row=0, column=2, sticky=W, padx=10)
            
            # الصف الثاني
            ttk_bs.Label(stats_grid, text=f"الأصناف النافدة: {zero_quantity}", bootstyle="danger").grid(row=1, column=0, sticky=W, padx=10, pady=5)
            ttk_bs.Label(stats_grid, text=f"الأصناف منخفضة المخزون: {low_quantity}", bootstyle="warning").grid(row=1, column=1, sticky=W, padx=10, pady=5)

            # جدول البيانات
            table_frame = ttk_bs.LabelFrame(main_frame, text="📋 تفاصيل الأصناف", padding=5)
            table_frame.pack(fill=BOTH, expand=True)
            
            # إنشاء Treeview للبيانات
            columns = ("رقم_الصنف", "اسم_الصنف", "نوع_العهدة", "التصنيف", "الوحدة", "الكمية_الحالية", "الحالة")
            tree = ttk_bs.Treeview(table_frame, columns=columns, show="headings", height=15)
            
            # تعيين عناوين الأعمدة
            tree.heading("رقم_الصنف", text="رقم الصنف")
            tree.heading("اسم_الصنف", text="اسم الصنف")
            tree.heading("نوع_العهدة", text="نوع العهدة")
            tree.heading("التصنيف", text="التصنيف")
            tree.heading("الوحدة", text="الوحدة")
            tree.heading("الكمية_الحالية", text="الكمية الحالية")
            tree.heading("الحالة", text="الحالة")
            
            # تعيين عرض الأعمدة
            tree.column("رقم_الصنف", width=100, anchor=CENTER)
            tree.column("اسم_الصنف", width=200, anchor=E)
            tree.column("نوع_العهدة", width=120, anchor=E)
            tree.column("التصنيف", width=150, anchor=E)
            tree.column("الوحدة", width=80, anchor=CENTER)
            tree.column("الكمية_الحالية", width=100, anchor=CENTER)
            tree.column("الحالة", width=80, anchor=CENTER)
            
            # إضافة البيانات
            for item in self.items_data:
                try:
                    # التأكد من أن الكمية رقم صحيح
                    current_qty = int(getattr(item, 'current_quantity', 0))
                    
                    tree.insert("", "end", values=(
                        getattr(item, 'item_number', ''),
                        getattr(item, 'name', getattr(item, 'item_name', '')),
                        getattr(item, 'custody_type', ''),
                        getattr(item, 'classification', ''),
                        getattr(item, 'unit', ''),
                        current_qty,  # رقم صحيح
                        'نشط' if getattr(item, 'is_active', True) else 'غير نشط'
                    ))
                except Exception as e:
                    print(f"خطأ في إضافة صنف للتقرير: {e}")
                    continue
            
            # شريط التمرير للجدول
            scrollbar = ttk_bs.Scrollbar(table_frame, orient=VERTICAL, command=tree.yview)
            tree.configure(yscrollcommand=scrollbar.set)
            
            # تخطيط الجدول
            tree.pack(side=LEFT, fill=BOTH, expand=True)
            scrollbar.pack(side=RIGHT, fill=Y)
            
            # أزرار العمليات
            buttons_frame = ttk_bs.Frame(main_frame)
            buttons_frame.pack(fill=X, pady=(10, 0))
            
            ttk_bs.Button(
                buttons_frame,
                text="📤 تصدير التقرير إلى إكسل",
                command=lambda: self.export_report_to_excel(report_window),
                bootstyle="success"
            ).pack(side=LEFT, padx=5)
            
            ttk_bs.Button(
                buttons_frame,
                text="🖨️ طباعة التقرير",
                command=lambda: self.print_report(report_window),
                bootstyle="info"
            ).pack(side=LEFT, padx=5)
            
            ttk_bs.Button(
                buttons_frame,
                text="❌ إغلاق",
                command=report_window.destroy,
                bootstyle="secondary"
            ).pack(side=RIGHT, padx=5)
            
            print("✅ تم إنشاء التقرير بنجاح")

        except Exception as e:
            error_msg = f"فشل في إنشاء التقرير: {e}"
            print(f"❌ {error_msg}")
            messagebox.showerror("خطأ في التقرير", error_msg)
            import traceback
            traceback.print_exc()
    
    def export_report_to_excel(self, parent_window):
        """تصدير التقرير إلى ملف إكسل"""
        try:
            file_path = filedialog.asksaveasfilename(
                parent=parent_window,
                title="حفظ تقرير الأصناف",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                initialname=f"تقرير_الأصناف_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            )
            
            if file_path:
                # تحضير بيانات التقرير
                report_data = []
                for item in self.items_data:
                    # التأكد من أن الكميات أرقام صحيحة
                    current_qty = int(getattr(item, 'current_quantity', 0))
                    entered_qty = int(getattr(item, 'entered_quantity', 0))
                    dispensed_qty = int(getattr(item, 'dispensed_quantity', 0))
                    
                    report_data.append({
                        'رقم الصنف': getattr(item, 'item_number', ''),
                        'اسم الصنف': getattr(item, 'name', getattr(item, 'item_name', '')),
                        'نوع العهدة': getattr(item, 'custody_type', ''),
                        'التصنيف': getattr(item, 'classification', ''),
                        'الوحدة': getattr(item, 'unit', ''),
                        'الكمية الحالية': current_qty,
                        'الكمية المدخلة': entered_qty,
                        'الكمية المصروفة': dispensed_qty,
                        'مستخدم الإدخال': getattr(item, 'data_entry_user', ''),
                        'تاريخ الإدخال': str(getattr(item, 'entry_date', '')),
                        'الحالة': 'نشط' if getattr(item, 'is_active', True) else 'غير نشط'
                    })
                
                df = pd.DataFrame(report_data)
                df.to_excel(file_path, index=False, engine='openpyxl')
                
                messagebox.showinfo("تم التصدير", f"تم حفظ التقرير في:\n{file_path}")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير التقرير: {e}")
    
    def print_report(self, parent_window):
        """طباعة التقرير"""
        try:
            print("🖨️ [طباعة التقرير] بدء العملية...")
            
            # إنشاء تقرير HTML للطباعة
            html_content = self.generate_html_report()
            
            # حفظ التقرير في ملف HTML مؤقت
            import tempfile
            import os
            import webbrowser
            
            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
                f.write(html_content)
                temp_file = f.name
            
            print(f"📄 [طباعة التقرير] تم إنشاء ملف HTML: {temp_file}")
            
            # فتح التقرير في المتصفح للطباعة
            webbrowser.open(f'file://{temp_file}')
            
            messagebox.showinfo(
                "تم فتح التقرير للطباعة 🖨️",
                "تم فتح التقرير في المتصفح.\n\n"
                "يمكنك الآن:\n"
                "• طباعة التقرير (Ctrl+P)\n"
                "• حفظه كـ PDF\n"
                "• مشاركته\n\n"
                "سيتم حذف الملف المؤقت تلقائياً عند إغلاق التطبيق."
            )
            
            print("✅ [طباعة التقرير] تم فتح التقرير في المتصفح")
            
        except Exception as e:
            error_msg = f"فشل في طباعة التقرير: {e}"
            print(f"❌ [طباعة التقرير] {error_msg}")
            messagebox.showerror("خطأ في الطباعة", error_msg)
    
    def generate_html_report(self):
        """إنشاء تقرير HTML للطباعة"""
        try:
            from datetime import datetime
            
            # حساب الإحصائيات
            total_items = len(self.items_data)
            active_items = len([i for i in self.items_data if getattr(i, 'is_active', True)])
            inactive_items = total_items - active_items
            zero_quantity = len([i for i in self.items_data if getattr(i, 'current_quantity', 0) == 0])
            low_quantity = len([i for i in self.items_data if 0 < getattr(i, 'current_quantity', 0) <= 5])
            
            # إنشاء HTML
            html = f"""
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير الأصناف المفصل</title>
    <style>
        body {{
            font-family: 'Arial', 'Tahoma', sans-serif;
            margin: 20px;
            direction: rtl;
            text-align: right;
        }}
        .header {{
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }}
        .header h1 {{
            color: #2c3e50;
            margin: 0;
        }}
        .header p {{
            color: #7f8c8d;
            margin: 5px 0;
        }}
        .stats {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }}
        .stat-box {{
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            text-align: center;
        }}
        .stat-number {{
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }}
        .stat-label {{
            color: #6c757d;
            margin-top: 5px;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }}
        th, td {{
            border: 1px solid #dee2e6;
            padding: 8px;
            text-align: center;
        }}
        th {{
            background-color: #e9ecef;
            font-weight: bold;
        }}
        tr:nth-child(even) {{
            background-color: #f8f9fa;
        }}
        .footer {{
            margin-top: 30px;
            text-align: center;
            color: #6c757d;
            border-top: 1px solid #dee2e6;
            padding-top: 20px;
        }}
        @media print {{
            body {{ margin: 0; }}
            .no-print {{ display: none; }}
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 تقرير الأصناف المفصل</h1>
        <p>تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}</p>
        <p>نظام إدارة المخازن والمستودعات</p>
    </div>
    
    <div class="stats">
        <div class="stat-box">
            <div class="stat-number">{total_items}</div>
            <div class="stat-label">إجمالي الأصناف</div>
        </div>
        <div class="stat-box">
            <div class="stat-number">{active_items}</div>
            <div class="stat-label">الأصناف النشطة</div>
        </div>
        <div class="stat-box">
            <div class="stat-number">{inactive_items}</div>
            <div class="stat-label">الأصناف غير النشطة</div>
        </div>
        <div class="stat-box">
            <div class="stat-number">{zero_quantity}</div>
            <div class="stat-label">الأصناف النافدة</div>
        </div>
        <div class="stat-box">
            <div class="stat-number">{low_quantity}</div>
            <div class="stat-label">الأصناف منخفضة المخزون</div>
        </div>
    </div>
    
    <table>
        <thead>
            <tr>
                <th>رقم الصنف</th>
                <th>اسم الصنف</th>
                <th>نوع العهدة</th>
                <th>التصنيف</th>
                <th>الوحدة</th>
                <th>الكمية الحالية</th>
                <th>الحالة</th>
            </tr>
        </thead>
        <tbody>
"""
            
            # إضافة بيانات الأصناف
            for item in self.items_data:
                try:
                    current_qty = int(getattr(item, 'current_quantity', 0))
                    html += f"""
            <tr>
                <td>{getattr(item, 'item_number', '')}</td>
                <td>{getattr(item, 'name', getattr(item, 'item_name', ''))}</td>
                <td>{getattr(item, 'custody_type', '')}</td>
                <td>{getattr(item, 'classification', '')}</td>
                <td>{getattr(item, 'unit', '')}</td>
                <td>{current_qty}</td>
                <td>{'نشط' if getattr(item, 'is_active', True) else 'غير نشط'}</td>
            </tr>
"""
                except Exception as e:
                    print(f"خطأ في إضافة صنف للتقرير HTML: {e}")
                    continue
            
            html += """
        </tbody>
    </table>
    
    <div class="footer">
        <p>تم إنشاء هذا التقرير بواسطة نظام إدارة المخازن والمستودعات</p>
        <p>جميع الحقوق محفوظة © 2024</p>
    </div>
</body>
</html>
"""
            
            return html
            
        except Exception as e:
            print(f"خطأ في إنشاء تقرير HTML: {e}")
            return "<html><body><h1>خطأ في إنشاء التقرير</h1></body></html>"

    def add_movement_for_selected(self):
        """إضافة حركة مخزون للصنف المحدد"""
        if not self.current_item:
            messagebox.showwarning("تحذير", "يرجى اختيار صنف من الجدول أولاً")
            return

        try:
            # فتح شاشة إضافة حركة مخزون للصنف المحدد
            # استخدام النافذة الرئيسية بدلاً من self.parent لتجنب إخفاء شاشة إدارة الأصناف
            from ui.add_inventory_movement_window import AddInventoryMovementWindow
            AddInventoryMovementWindow(
                self.main_window.parent,  # استخدام النافذة الرئيسية بدلاً من self.parent
                self.main_window,
                self.current_item.item_number,
                self.current_item.name
            )
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح شاشة إضافة حركة المخزون: {e}")
            print(f"خطأ في فتح شاشة الحركة: {e}")
            import traceback
            traceback.print_exc()

    def refresh_data(self):
        """تحديث البيانات"""
        print("🔄 بدء تحديث بيانات شاشة إدارة الأصناف الرئيسية...")
        
        # إعادة تحميل البيانات من قاعدة البيانات
        self.load_items_data()
        
        # تطبيق الفلاتر الحالية
        self.apply_filters()
        
        # تحديث شريط المعلومات
        self.update_info_bar()
        
        # إعادة تعيين العنصر المحدد
        self.current_item = None
        
        print("✅ تم تحديث بيانات شاشة إدارة الأصناف الرئيسية بنجاح")

    def delete_all_data(self):
        """حذف جميع البيانات مع التحقق من عدم ارتباط الأصناف بعمليات صرف"""
        try:
            # التحقق من وجود أصناف مرتبطة بعمليات صرف
            linked_items = self.check_items_linked_to_transactions()

            if linked_items:
                # عرض رسالة تحذيرية مع قائمة الأصناف المرتبطة
                linked_items_text = "\n".join([f"- {item['item_name']} (رقم: {item['item_number']})" for item in linked_items])

                messagebox.showerror(
                    "تعذر الحذف",
                    f"لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:\n\n{linked_items_text}\n\n"
                    "يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً."
                )
                return

            # تأكيد الحذف
            confirm = messagebox.askyesno(
                "تأكيد الحذف",
                "⚠️ تحذير: سيتم حذف جميع البيانات التالية نهائياً:\n\n"
                "• جميع الأصناف المضافة\n"
                "• جميع حركات المخزون\n"
                "• جميع عمليات الصرف\n"
                "• جميع بيانات المستفيدين\n\n"
                "هذا الإجراء لا يمكن التراجع عنه!\n\n"
                "هل أنت متأكد من المتابعة؟",
                icon="warning"
            )

            if not confirm:
                return

            # تأكيد إضافي
            final_confirm = messagebox.askyesno(
                "تأكيد نهائي",
                "🚨 تأكيد أخير:\n\n"
                "سيتم حذف جميع البيانات نهائياً!\n"
                "لا يمكن استرجاع البيانات بعد الحذف.\n\n"
                "هل أنت متأكد تماماً؟",
                icon="warning"
            )

            if not final_confirm:
                return

            # تنفيذ الحذف
            success = self.perform_data_deletion()

            if success:
                messagebox.showinfo(
                    "تم الحذف بنجاح",
                    "✅ تم حذف جميع البيانات بنجاح!\n\n"
                    "قاعدة البيانات جاهزة الآن لإدخال بيانات جديدة."
                )

                # تحديث الشاشة
                self.refresh_data()
            else:
                messagebox.showerror(
                    "خطأ في الحذف",
                    "❌ حدث خطأ أثناء حذف البيانات.\n"
                    "يرجى المحاولة مرة أخرى أو التواصل مع الدعم الفني."
                )

        except Exception as e:
            print(f"❌ خطأ في حذف جميع البيانات: {e}")
            messagebox.showerror(
                "خطأ",
                f"حدث خطأ غير متوقع:\n{str(e)}"
            )

    def check_items_linked_to_transactions(self):
        """التحقق من وجود أصناف مرتبطة بعمليات صرف"""
        try:
            from database import db_manager

            # البحث عن الأصناف المرتبطة بعمليات صرف
            query = """
                SELECT DISTINCT ai.item_number, ai.item_name, ai.id
                FROM added_items ai
                INNER JOIN transaction_items ti ON ai.id = ti.item_id
                INNER JOIN transactions t ON ti.transaction_id = t.id
                WHERE ai.is_active = 1
                ORDER BY ai.item_name
            """

            linked_items = db_manager.fetch_all(query)
            return linked_items if linked_items else []

        except Exception as e:
            print(f"❌ خطأ في التحقق من الأصناف المرتبطة: {e}")
            return []

    def perform_data_deletion(self):
        """تنفيذ حذف جميع البيانات"""
        try:
            from database import db_manager

            print("🗑️ بدء عملية حذف جميع البيانات...")

            # قائمة الجداول التي سيتم مسحها بالترتيب الصحيح
            tables_to_clear = [
                'transaction_items',        # عناصر المعاملات (أولاً لتجنب مشاكل المفاتيح الخارجية)
                'transactions',             # المعاملات
                'inventory_movements_new',  # حركات المخزون الجديدة
                'inventory_movements',      # حركات المخزون القديمة
                'added_items',             # الأصناف المضافة
                'items',                   # الأصناف
                'beneficiaries',           # المستفيدين
            ]

            # تعطيل المفاتيح الخارجية مؤقتاً
            db_manager.execute_query("PRAGMA foreign_keys = OFF")

            # مسح البيانات من كل جدول
            for table in tables_to_clear:
                try:
                    # التحقق من وجود الجدول
                    check_query = "SELECT name FROM sqlite_master WHERE type='table' AND name=?"
                    result = db_manager.fetch_one(check_query, (table,))

                    if result:
                        # مسح البيانات
                        db_manager.execute_query(f"DELETE FROM {table}")

                        # إعادة تعيين العداد التلقائي
                        db_manager.execute_query(f"DELETE FROM sqlite_sequence WHERE name='{table}'")

                        print(f"✅ تم مسح جدول {table}")
                    else:
                        print(f"⚠️ الجدول {table} غير موجود")

                except Exception as e:
                    print(f"⚠️ خطأ في مسح جدول {table}: {e}")
                    # نتابع مع الجداول الأخرى

            # إعادة تفعيل المفاتيح الخارجية
            db_manager.execute_query("PRAGMA foreign_keys = ON")

            # تنظيف قاعدة البيانات وضغطها
            db_manager.execute_query("VACUUM")

            print("✅ تم حذف جميع البيانات بنجاح!")
            return True

        except Exception as e:
            print(f"❌ خطأ في حذف البيانات: {e}")
            return False

    def update_item_from_dialog(self, item, data):
        """تحديث بيانات الصنف من النافذة"""
        item.name = data['name']
        item.code = data['code']
        item.description = data['description']
        item.category_id = data['category_id']
        item.unit = data['unit']
        item.current_quantity = data['current_quantity']
        item.minimum_quantity = data['minimum_quantity']
        item.maximum_quantity = data['maximum_quantity']
        item.unit_price = data['unit_price']
        item.custody_type = data['custody_type']
        item.classification = data['classification']
        item.location = data['location']
        item.supplier = data['supplier']
        item.barcode = data['barcode']
        item.expiry_date = data['expiry_date']
        item.is_active = data['is_active']

    def get_current_user_name(self):
        """الحصول على اسم المستخدم الحالي"""
        try:
            # محاولة الحصول على المستخدم الحالي من النافذة الرئيسية
            if hasattr(self.main_window, 'current_user') and self.main_window.current_user:
                current_user = self.main_window.current_user
                if hasattr(current_user, 'full_name') and current_user.full_name:
                    return current_user.full_name
                elif hasattr(current_user, 'username') and current_user.username:
                    return current_user.username

            # محاولة الحصول على المستخدم من قاعدة البيانات
            from database import db_manager
            current_user_query = """
                SELECT full_name, username FROM users
                WHERE is_active = 1
                ORDER BY last_login DESC
                LIMIT 1
            """
            current_user = db_manager.fetch_one(current_user_query)
            if current_user:
                full_name = current_user.get('full_name')
                username = current_user.get('username')

                if full_name and full_name.strip():
                    return full_name.strip()
                elif username and username.strip():
                    return username.strip()

            # قيمة افتراضية
            return "مدير النظام"

        except Exception as e:
            print(f"خطأ في الحصول على المستخدم الحالي: {e}")
            return "مدير النظام"

    def import_excel(self):
        """استيراد الأصناف من ملف إكسل"""
        try:
            # اختيار ملف الإكسل
            file_path = filedialog.askopenfilename(
                title="اختر ملف الإكسل للاستيراد",
                filetypes=[
                    ("Excel files", "*.xlsx *.xls"),
                    ("All files", "*.*")
                ]
            )
            
            if not file_path:
                return
            
            # قراءة ملف الإكسل
            try:
                df = pd.read_excel(file_path)
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في قراءة ملف الإكسل: {e}")
                return
            
            # التحقق من وجود الأعمدة المطلوبة (بدون الكمية المدخلة)
            required_columns = ['رقم الصنف', 'اسم الصنف', 'نوع العهدة', 'التصنيف', 'الوحدة', 'الكمية الحالية']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                messagebox.showerror(
                    "خطأ في البيانات",
                    f"الأعمدة التالية مفقودة في ملف الإكسل:\n{', '.join(missing_columns)}\n\nيرجى استخدام النموذج الصحيح"
                )
                return
            
            # عرض معاينة البيانات
            preview_msg = f"سيتم استيراد {len(df)} صنف من الملف.\n\nهل تريد المتابعة؟"
            if not messagebox.askyesno("تأكيد الاستيراد", preview_msg):
                return
            
            # استيراد البيانات
            success_count = 0
            error_count = 0
            errors = []
            
            for index, row in df.iterrows():
                try:
                    # التحقق من البيانات الأساسية
                    if pd.isna(row['رقم الصنف']) or pd.isna(row['اسم الصنف']):
                        error_count += 1
                        errors.append(f"الصف {index + 2}: رقم الصنف أو اسم الصنف فارغ")
                        continue
                    
                    # إنشاء صنف جديد
                    from models import AddedItem
                    
                    # التحقق من عدم وجود الصنف مسبقاً
                    existing_item = AddedItem.get_by_item_number(str(row['رقم الصنف']))
                    if existing_item:
                        error_count += 1
                        errors.append(f"الصف {index + 2}: الصنف برقم {row['رقم الصنف']} موجود مسبقاً")
                        continue
                    
                    # الحصول على المستخدم الحالي
                    current_user_name = self.get_current_user_name()

                    # إنشاء الصنف الجديد مع توحيد الكميتين
                    current_qty = int(row.get('الكمية الحالية', 0)) if not pd.isna(row.get('الكمية الحالية', 0)) else 0
                    # الكمية المدخلة = الكمية الحالية (توحيد الكميتين)
                    entered_qty = current_qty

                    new_item = AddedItem(
                        item_number=str(row['رقم الصنف']),
                        item_name=str(row['اسم الصنف']),
                        custody_type=str(row.get('نوع العهدة', '')),
                        classification=str(row.get('التصنيف', '')),
                        unit=str(row.get('الوحدة', 'عدد')),
                        current_quantity=current_qty,
                        entered_quantity=entered_qty,
                        # البيانات التلقائية من النظام
                        data_entry_user=current_user_name,
                        entry_date=datetime.now().strftime('%Y-%m-%d'),
                        is_active=True
                    )
                    
                    # حفظ الصنف
                    if new_item.save():
                        success_count += 1

                        # إنشاء حركة مخزون تلقائية للصنف المستورد
                        try:
                            self.create_import_movement(new_item, current_user_name)
                            print(f"✅ تم إنشاء حركة مخزون للصنف {new_item.item_number}")
                        except Exception as movement_error:
                            print(f"⚠️ خطأ في إنشاء حركة المخزون للصنف {new_item.item_number}: {movement_error}")
                            # لا نوقف العملية، فقط نسجل الخطأ

                    else:
                        error_count += 1
                        errors.append(f"الصف {index + 2}: فشل في حفظ الصنف")
                        
                except Exception as e:
                    error_count += 1
                    errors.append(f"الصف {index + 2}: {str(e)}")
            
            # عرض نتائج الاستيراد
            result_msg = f"تم الاستيراد بنجاح!\n\n"
            result_msg += f"✅ تم استيراد: {success_count} صنف\n"
            if error_count > 0:
                result_msg += f"❌ فشل في استيراد: {error_count} صنف\n\n"
                if errors:
                    result_msg += "الأخطاء:\n" + "\n".join(errors[:10])  # عرض أول 10 أخطاء فقط
                    if len(errors) > 10:
                        result_msg += f"\n... و {len(errors) - 10} أخطاء أخرى"
            
            messagebox.showinfo("نتائج الاستيراد", result_msg)
            
            # تحديث البيانات
            if success_count > 0:
                self.refresh_data()
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء الاستيراد: {e}")
            print(f"خطأ في استيراد الإكسل: {e}")
            import traceback
            traceback.print_exc()

    def create_import_movement(self, item, user_name):
        """إنشاء حركة مخزون تلقائية للصنف المستورد من Excel"""
        try:
            from models import InventoryMovement
            from database import db_manager

            # الحصول على معرف المستخدم الحالي
            user_id = None
            try:
                user_query = "SELECT id FROM users WHERE username = ? OR full_name = ?"
                user_result = db_manager.fetch_one(user_query, (user_name, user_name))
                if user_result:
                    user_id = user_result[0]
                else:
                    # إذا لم نجد المستخدم، نستخدم المدير الافتراضي
                    admin_result = db_manager.fetch_one("SELECT id FROM users WHERE username = 'admin' LIMIT 1")
                    if admin_result:
                        user_id = admin_result[0]
            except Exception as e:
                print(f"خطأ في الحصول على معرف المستخدم: {e}")
                user_id = 1  # افتراضي

            # إنشاء حركة مخزون للإضافة
            movement = InventoryMovement()
            movement.item_number = item.item_number
            movement.movement_type = "إضافة"
            movement.quantity = float(item.current_quantity)
            movement.organization_type = "استيراد Excel"
            movement.organization_name = "استيراد تلقائي من ملف Excel"
            movement.notes = f"إضافة تلقائية عند استيراد الصنف من Excel - الكمية: {item.current_quantity}"
            movement.movement_date = datetime.now()
            movement.user_id = user_id
            movement.is_active = True

            # حفظ الحركة بدون تحديث الكميات (لأن الصنف جديد)
            if movement.save_without_quantity_update():
                print(f"✅ تم إنشاء حركة استيراد للصنف {item.item_number} بكمية {item.current_quantity}")
                return True
            else:
                print(f"⚠️ فشل في حفظ حركة الاستيراد للصنف {item.item_number}")
                return False

        except Exception as e:
            print(f"❌ خطأ في إنشاء حركة الاستيراد: {e}")
            import traceback
            traceback.print_exc()
            return False

    def get_comprehensive_item_movement_data(self, item_number):
        """الحصول على بيانات شاملة لحركة الصنف"""
        try:
            from database import db_manager

            # الحصول على بيانات الصنف الأساسية
            item_query = """
                SELECT item_number, item_name, custody_type, classification,
                       unit, current_quantity, entered_quantity
                FROM added_items
                WHERE item_number = ? AND is_active = 1
            """
            item_data = db_manager.fetch_one(item_query, (item_number,))

            if not item_data:
                return None

            # حساب إجمالي الإضافات من حركات المخزون
            additions_query = """
                SELECT COALESCE(SUM(quantity), 0)
                FROM inventory_movements_new
                WHERE item_number = ? AND movement_type = 'إضافة' AND is_active = 1
            """
            total_additions = db_manager.fetch_one(additions_query, (item_number,))[0]

            # حساب إجمالي الصرف من حركات المخزون
            dispensed_query = """
                SELECT COALESCE(SUM(quantity), 0)
                FROM inventory_movements_new
                WHERE item_number = ? AND movement_type = 'صرف' AND is_active = 1
            """
            total_dispensed = db_manager.fetch_one(dispensed_query, (item_number,))[0]

            # حساب المجموع الفعلي (الإضافات - الصرف)
            actual_total = total_additions - total_dispensed

            # إنشاء كائن البيانات الشاملة
            comprehensive_data = {
                'item_number': item_data[0],
                'item_name': item_data[1],
                'custody_type': item_data[2],
                'classification': item_data[3],
                'unit': item_data[4],
                'entered_quantity': item_data[6],  # الكمية المدخلة الأصلية
                'total_additions': total_additions,  # إجمالي الإضافات
                'total_dispensed': total_dispensed,  # إجمالي الصرف
                'actual_total': actual_total,  # المجموع الفعلي
                'current_quantity': item_data[5]  # الكمية الحالية في الجدول
            }

            return comprehensive_data

        except Exception as e:
            print(f"خطأ في الحصول على بيانات الحركة الشاملة للصنف {item_number}: {e}")
            return None

    def download_template(self):
        """تحميل نموذج البيانات (ملف إكسل فارغ)"""
        print("🔄 [نموذج البيانات] بدء العملية...")
        
        try:
            # التأكد من استيراد المكتبات
            import pandas as pd
            import os
            from tkinter import filedialog, messagebox
            
            print("📋 [نموذج البيانات] اختيار مكان الحفظ...")
            
            # اختيار مكان حفظ الملف
            file_path = filedialog.asksaveasfilename(
                parent=self.parent,
                title="حفظ نموذج البيانات",
                defaultextension=".xlsx",
                filetypes=[
                    ("Excel files", "*.xlsx"),
                    ("All files", "*.*")
                ],
                initialfile="نموذج_الأصناف.xlsx"
            )
            
            if not file_path:
                print("❌ [نموذج البيانات] تم إلغاء العملية")
                return
            
            print(f"📁 [نموذج البيانات] مسار الحفظ: {file_path}")
            
            # إنشاء DataFrame مع الأعمدة المطلوبة (بدون الكمية المدخلة)
            template_data = {
                'الرقم': [1, 2, 3, 4, ''],
                'رقم الصنف': ['001', '002', '003', '004', ''],
                'اسم الصنف': ['قلم رصاص', 'دفتر A4', 'مسطرة 30 سم', 'ممحاة', ''],
                'نوع العهدة': ['مستهلكة', 'مستديمة', 'مستهلكة', 'مستهلكة', ''],
                'التصنيف': ['قرطاسية', 'مكتبية', 'قرطاسية', 'قرطاسية', ''],
                'الوحدة': ['عدد', 'عدد', 'عدد', 'عدد', ''],
                'الكمية الحالية': [100, 50, 25, 75, '']
            }
            
            print("📊 [نموذج البيانات] إنشاء جدول البيانات...")
            df = pd.DataFrame(template_data)
            
            # حفظ الملف
            print("💾 [نموذج البيانات] حفظ الملف...")
            df.to_excel(file_path, index=False, engine='openpyxl')
            
            print("✅ [نموذج البيانات] تم الحفظ بنجاح")
            
            # التحقق من وجود الملف
            if os.path.exists(file_path):
                print("✅ [نموذج البيانات] الملف موجود على القرص")
                
                # عرض رسالة النجاح
                messagebox.showinfo(
                    "تم إنشاء النموذج بنجاح ✅",
                    f"تم حفظ نموذج البيانات في:\n{file_path}\n\n"
                    "📋 محتويات النموذج:\n"
                    "• الرقم\n"
                    "• رقم الصنف\n"
                    "• اسم الصنف\n"
                    "• نوع العهدة\n"
                    "• التصنيف\n"
                    "• الوحدة\n"
                    "• الكمية الحالية\n"
                    "• الكمية المدخلة\n\n"
                    "🔄 الخطوة التالية:\n"
                    "املأ البيانات واستخدم زر 'استيراد إكسل'"
                )
                
                # فتح مجلد الملف
                try:
                    if messagebox.askyesno("فتح المجلد 📁", "هل تريد فتح مجلد الملف الآن؟"):
                        import subprocess
                        if os.name == 'nt':  # Windows
                            subprocess.run(['explorer', '/select,', file_path], shell=True)
                        else:  # Linux/Mac
                            subprocess.run(['xdg-open', os.path.dirname(file_path)])
                except Exception as folder_error:
                    print(f"⚠️ [نموذج البيانات] تعذر فتح المجلد: {folder_error}")
            else:
                print("❌ [نموذج البيانات] الملف غير موجود بعد الحفظ!")
                messagebox.showerror("خطأ", "فشل في حفظ الملف!")
                    
        except ImportError as ie:
            error_msg = f"مكتبة مفقودة: {ie}"
            print(f"❌ [نموذج البيانات] {error_msg}")
            
            # محاولة تثبيت المكتبات المطلوبة
            if messagebox.askyesno(
                "مكتبة مفقودة", 
                f"{error_msg}\n\nهل تريد محاولة تثبيت المكتبات المطلوبة؟"
            ):
                self.install_required_packages()
            
        except Exception as e:
            error_msg = f"فشل في إنشاء نموذج البيانات:\n{str(e)}"
            print(f"❌ [نموذج البيانات] {error_msg}")
            messagebox.showerror("خطأ في إنشاء النموذج", error_msg)
            import traceback
            traceback.print_exc()

    def install_required_packages(self):
        """تثبيت المكتبات المطلوبة"""
        try:
            import subprocess
            import sys
            
            packages = ['pandas', 'openpyxl']
            
            for package in packages:
                print(f"📦 تثبيت {package}...")
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            
            messagebox.showinfo(
                "تم التثبيت", 
                "تم تثبيت المكتبات المطلوبة بنجاح!\n\nيمكنك الآن استخدام أزرار الإكسل."
            )
            
        except Exception as e:
            messagebox.showerror(
                "فشل التثبيت", 
                f"فشل في تثبيت المكتبات:\n{e}\n\nيرجى تثبيتها يدوياً:\npip install pandas openpyxl"
            )

    def export_excel(self):
        """تصدير الأصناف إلى ملف إكسل"""
        print("🔄 [تصدير إكسل] بدء العملية...")
        
        try:
            # التأكد من استيراد المكتبات
            import pandas as pd
            import os
            from tkinter import filedialog, messagebox
            from datetime import datetime
            
            # التحقق من وجود البيانات
            if not hasattr(self, 'items_data') or not self.items_data:
                print("⚠️ [تصدير إكسل] لا توجد بيانات للتصدير")
                messagebox.showwarning("لا توجد بيانات", "لا توجد أصناف للتصدير.\n\nيرجى إضافة بعض الأصناف أولاً.")
                return
            
            print(f"📊 [تصدير إكسل] عدد الأصناف: {len(self.items_data)}")
            
            # اختيار مكان حفظ الملف
            file_path = filedialog.asksaveasfilename(
                parent=self.parent,
                title="تصدير الأصناف إلى إكسل",
                defaultextension=".xlsx",
                filetypes=[
                    ("Excel files", "*.xlsx"),
                    ("All files", "*.*")
                ],
                initialfile=f"الأصناف_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            )
            
            if not file_path:
                print("❌ [تصدير إكسل] تم إلغاء العملية")
                return
            
            print(f"📁 [تصدير إكسل] مسار الحفظ: {file_path}")
            
            # تحضير البيانات للتصدير
            print("📋 [تصدير إكسل] تحضير البيانات...")
            export_data = []
            
            for i, item in enumerate(self.items_data):
                try:
                    # التأكد من أن الكميات أرقام صحيحة
                    current_qty = int(getattr(item, 'current_quantity', 0))
                    entered_qty = int(getattr(item, 'entered_quantity', 0))
                    dispensed_qty = int(getattr(item, 'dispensed_quantity', 0))
                    
                    row_data = {
                        'الرقم': getattr(item, 'id', i+1),
                        'رقم الصنف': getattr(item, 'item_number', ''),
                        'اسم الصنف': getattr(item, 'name', getattr(item, 'item_name', '')),
                        'نوع العهدة': getattr(item, 'custody_type', ''),
                        'التصنيف': getattr(item, 'classification', ''),
                        'الوحدة': getattr(item, 'unit', ''),
                        'الكمية الحالية': current_qty,
                        'الكمية المدخلة': entered_qty,
                        'الكمية المصروفة': dispensed_qty,
                        'مستخدم الإدخال': getattr(item, 'data_entry_user', ''),
                        'تاريخ الإدخال': str(getattr(item, 'entry_date', '')),
                        'الحالة': 'نشط' if getattr(item, 'is_active', True) else 'غير نشط',
                        'تاريخ الإنشاء': str(getattr(item, 'created_at', '')),
                        'تاريخ التحديث': str(getattr(item, 'updated_at', ''))
                    }
                    export_data.append(row_data)
                    
                except Exception as item_error:
                    print(f"⚠️ [تصدير إكسل] خطأ في معالجة الصنف {i+1}: {item_error}")
                    continue
            
            if not export_data:
                print("❌ [تصدير إكسل] لا توجد بيانات صالحة")
                messagebox.showerror("خطأ", "لا توجد بيانات صالحة للتصدير")
                return
            
            print(f"✅ [تصدير إكسل] تم تحضير {len(export_data)} صنف")
            
            # إنشاء DataFrame
            print("📊 [تصدير إكسل] إنشاء جدول البيانات...")
            df = pd.DataFrame(export_data)
            
            # حفظ الملف مع تنسيق جميل
            print("💾 [تصدير إكسل] حفظ الملف...")
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='الأصناف', index=False)
                
                # تنسيق الورقة
                worksheet = writer.sheets['الأصناف']
                
                # تعديل عرض الأعمدة
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            cell_length = len(str(cell.value)) if cell.value is not None else 0
                            if cell_length > max_length:
                                max_length = cell_length
                        except:
                            pass
                    adjusted_width = min(max(max_length + 2, 10), 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width
            
            print("✅ [تصدير إكسل] تم الحفظ بنجاح")
            
            # التحقق من وجود الملف
            if os.path.exists(file_path):
                print("✅ [تصدير إكسل] الملف موجود على القرص")
                
                # عرض رسالة النجاح
                messagebox.showinfo(
                    "تم التصدير بنجاح ✅", 
                    f"تم تصدير {len(export_data)} صنف بنجاح إلى:\n{file_path}\n\n"
                    f"📊 محتويات الملف:\n"
                    f"• جميع بيانات الأصناف\n"
                    f"• الكميات (أرقام صحيحة)\n"
                    f"• تواريخ الإدخال والتحديث\n"
                    f"• معلومات المستخدمين\n"
                    f"• حالة كل صنف"
                )
                
                # فتح مجلد الملف
                try:
                    if messagebox.askyesno("فتح المجلد 📁", "هل تريد فتح مجلد الملف الآن؟"):
                        import subprocess
                        if os.name == 'nt':  # Windows
                            subprocess.run(['explorer', '/select,', file_path], shell=True)
                        else:  # Linux/Mac
                            subprocess.run(['xdg-open', os.path.dirname(file_path)])
                except Exception as folder_error:
                    print(f"⚠️ [تصدير إكسل] تعذر فتح المجلد: {folder_error}")
            else:
                print("❌ [تصدير إكسل] الملف غير موجود بعد الحفظ!")
                messagebox.showerror("خطأ", "فشل في حفظ الملف!")
                    
        except ImportError as ie:
            error_msg = f"مكتبة مفقودة: {ie}"
            print(f"❌ [تصدير إكسل] {error_msg}")
            
            # محاولة تثبيت المكتبات المطلوبة
            if messagebox.askyesno(
                "مكتبة مفقودة", 
                f"{error_msg}\n\nهل تريد محاولة تثبيت المكتبات المطلوبة؟"
            ):
                self.install_required_packages()
            
        except Exception as e:
            error_msg = f"فشل في تصدير البيانات:\n{str(e)}"
            print(f"❌ [تصدير إكسل] {error_msg}")
            messagebox.showerror("خطأ في التصدير", error_msg)
            import traceback
            traceback.print_exc()

    def setup_shortcuts(self):
        """إعداد مفاتيح الاختصار العامة"""
        try:
            # إنشاء معالج السياق
            self.context_handler = ContextHandler()
            
            # تعيين دوال العمليات (يمكن تخصيصها حسب النافذة)
            self.context_handler.set_save_callback(self.shortcut_save)
            self.context_handler.set_delete_callback(self.shortcut_delete)
            self.context_handler.set_copy_callback(self.shortcut_copy)
            self.context_handler.set_paste_callback(self.shortcut_paste)
            
            # تفعيل مفاتيح الاختصار
            window = getattr(self, 'window', None) or getattr(self, 'parent', None)
            if window:
                self.global_shortcuts = GlobalShortcuts(window, self.context_handler)
        except Exception as e:
            print(f"خطأ في إعداد مفاتيح الاختصار: {e}")
    
    def shortcut_save(self):
        """عملية الحفظ (F1)"""
        try:
            if hasattr(self, 'save_data'):
                self.save_data()
            elif hasattr(self, 'save_changes'):
                self.save_changes()
            elif hasattr(self, 'save'):
                self.save()
            else:
                print("عملية الحفظ غير متاحة")
        except Exception as e:
            print(f"خطأ في عملية الحفظ: {e}")
    
    def shortcut_delete(self):
        """عملية الحذف (F2)"""
        try:
            if hasattr(self, 'delete_selected'):
                self.delete_selected()
            elif hasattr(self, 'delete_item'):
                self.delete_item()
            elif hasattr(self, 'delete'):
                self.delete()
            else:
                print("عملية الحذف غير متاحة")
        except Exception as e:
            print(f"خطأ في عملية الحذف: {e}")
    
    def shortcut_copy(self):
        """عملية النسخ (F3)"""
        try:
            if hasattr(self, 'copy_data'):
                self.copy_data()
            else:
                # نسخ عامة للبيانات المحددة
                import pyperclip
                pyperclip.copy("تم النسخ من النافذة")
                print("تم نسخ البيانات")
        except Exception as e:
            print(f"خطأ في عملية النسخ: {e}")
    
    def shortcut_paste(self):
        """عملية اللصق (F4)"""
        try:
            if hasattr(self, 'paste_data'):
                self.paste_data()
            else:
                import pyperclip
                clipboard_text = pyperclip.paste()
                if clipboard_text:
                    print(f"تم لصق: {clipboard_text[:50]}")
                else:
                    print("لا توجد بيانات في الحافظة")
        except Exception as e:
            print(f"خطأ في عملية اللصق: {e}")

class ItemDialog:
    """نافذة حوار إضافة/تعديل الصنف"""

    def __init__(self, parent, title, item=None):
        self.result = None
        self.item = item

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("700x800")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.center_window()

        # إعداد المحتوى
        self.setup_dialog()

        # انتظار إغلاق النافذة
        self.dialog.wait_window()

    def center_window(self):
        """توسيط النافذة"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() - 700) // 2
        y = (self.dialog.winfo_screenheight() - 800) // 2
        self.dialog.geometry(f"700x800+{x}+{y}")

    def setup_dialog(self):
        """إعداد محتوى النافذة"""
        main_frame = ttk_bs.Frame(self.dialog)
        main_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)

        # العنوان
        title_label = ttk_bs.Label(
            main_frame,
            text="📦 بيانات الصنف",
            bootstyle="primary"
        )
        title_label.pack(pady=10)

        # إنشاء النموذج
        self.create_form(main_frame)

        # أزرار العمليات
        self.create_buttons(main_frame)

        # تعبئة البيانات إذا كان في وضع التعديل
        if self.item:
            self.populate_form()

    def create_form(self, parent):
        """إنشاء نموذج البيانات"""
        # إنشاء notebook للتبويبات
        notebook = ttk_bs.Notebook(parent)
        notebook.pack(fill=BOTH, expand=True, pady=20)

        # تبويب البيانات الأساسية
        basic_frame = ttk_bs.Frame(notebook)
        notebook.add(basic_frame, text="البيانات الأساسية")
        self.create_basic_fields(basic_frame)

        # تبويب المخزون والأسعار
        inventory_frame = ttk_bs.Frame(notebook)
        notebook.add(inventory_frame, text="المخزون والأسعار")
        self.create_inventory_fields(inventory_frame)

        # تبويب معلومات إضافية
        additional_frame = ttk_bs.Frame(notebook)
        notebook.add(additional_frame, text="معلومات إضافية")
        self.create_additional_fields(additional_frame)

    def create_basic_fields(self, parent):
        """إنشاء حقول البيانات الأساسية"""
        # متغيرات النموذج
        self.name_var = tk.StringVar()
        self.code_var = tk.StringVar()
        self.description_var = tk.StringVar()
        self.category_var = tk.StringVar()
        self.unit_var = tk.StringVar()
        self.is_active_var = tk.BooleanVar(value=True)

        # الحقول
        fields = [
            ("اسم الصنف *", self.name_var, "entry"),
            ("كود الصنف", self.code_var, "entry"),
            ("الوصف", self.description_var, "text"),
            ("الفئة", self.category_var, "combobox"),
            ("وحدة القياس *", self.unit_var, "combobox"),
        ]

        for i, (label, var, field_type) in enumerate(fields):
            field_frame = ttk_bs.Frame(parent)
            field_frame.pack(fill=X, pady=5, padx=10)

            ttk_bs.Label(field_frame, text=label, width=20).pack(side=LEFT, anchor=E, padx=5)

            if field_type == "entry":
                widget = ttk_bs.Entry(field_frame, textvariable=var, width=40)
            elif field_type == "combobox":
                widget = ttk_bs.Combobox(field_frame, textvariable=var, width=37, state="readonly")
                if "category" in label.lower():
                    self.load_categories(widget)
                elif "unit" in label.lower():
                    widget['values'] = self.get_units()
            elif field_type == "text":
                widget = tk.Text(field_frame, height=3, width=40)
                # ربط النص بالمتغير
                widget.bind('<KeyRelease>', lambda e: var.set(widget.get("1.0", "end-1c")))

            widget.pack(side=LEFT, padx=5)

            if field_type == "text":
                setattr(self, f"description_text", widget)

        # خانة اختيار الحالة
        status_frame = ttk_bs.Frame(parent)
        status_frame.pack(fill=X, pady=10, padx=10)

        ttk_bs.Checkbutton(
            status_frame,
            text="الصنف نشط",
            variable=self.is_active_var,
            bootstyle="success"
        ).pack(side=LEFT)

    def create_inventory_fields(self, parent):
        """إنشاء حقول المخزون والأسعار"""
        # متغيرات المخزون - تغيير الكميات إلى أعداد صحيحة
        self.current_quantity_var = tk.IntVar(value=0)
        self.minimum_quantity_var = tk.IntVar(value=0)
        self.maximum_quantity_var = tk.IntVar(value=0)
        self.unit_price_var = tk.DoubleVar(value=0.0)  # السعر يبقى عشري

        # الحقول
        fields = [
            ("الكمية الحالية", self.current_quantity_var, "integer"),
            ("الحد الأدنى", self.minimum_quantity_var, "integer"),
            ("الحد الأقصى", self.maximum_quantity_var, "integer"),
            ("سعر الوحدة", self.unit_price_var, "number"),
        ]

        for i, (label, var, field_type) in enumerate(fields):
            field_frame = ttk_bs.Frame(parent)
            field_frame.pack(fill=X, pady=5, padx=10)

            ttk_bs.Label(field_frame, text=label, width=20).pack(side=LEFT, anchor=E, padx=5)

            widget = ttk_bs.Entry(field_frame, textvariable=var, width=40)
            widget.pack(side=LEFT, padx=5)

            # إضافة التحقق من الأعداد الصحيحة للكميات
            if field_type == "integer":
                def validate_integer_input(event, entry_widget=widget):
                    """التحقق من إدخال الأرقام الصحيحة فقط"""
                    allowed_keys = ['BackSpace', 'Delete', 'Left', 'Right', 'Tab', 'Return', 'KP_Enter']
                    if event.keysym in allowed_keys:
                        return True
                    if event.char.isdigit():
                        return True
                    return "break"

                widget.bind('<KeyPress>', validate_integer_input)

    def create_additional_fields(self, parent):
        """إنشاء الحقول الإضافية"""
        # متغيرات إضافية
        self.custody_type_var = tk.StringVar()
        self.classification_var = tk.StringVar()
        self.location_var = tk.StringVar()
        self.supplier_var = tk.StringVar()
        self.barcode_var = tk.StringVar()
        self.expiry_date_var = tk.StringVar()

        # الحقول
        fields = [
            ("نوع العهدة", self.custody_type_var, "combobox"),
            ("التصنيف", self.classification_var, "entry"),
            ("الموقع", self.location_var, "entry"),
            ("المورد", self.supplier_var, "entry"),
            ("الباركود", self.barcode_var, "entry"),
            ("تاريخ الانتهاء", self.expiry_date_var, "date"),
        ]

        for i, (label, var, field_type) in enumerate(fields):
            field_frame = ttk_bs.Frame(parent)
            field_frame.pack(fill=X, pady=5, padx=10)

            ttk_bs.Label(field_frame, text=label, width=20).pack(side=LEFT, anchor=E, padx=5)

            if field_type == "entry" or field_type == "date":
                widget = ttk_bs.Entry(field_frame, textvariable=var, width=40)
            elif field_type == "combobox":
                widget = ttk_bs.Combobox(field_frame, textvariable=var, width=37, state="readonly")
                if "custody" in label.lower():
                    widget['values'] = ["مستهلكة", "مستديمة", "أخرى"]

            widget.pack(side=LEFT, padx=5)

    def create_buttons(self, parent):
        """إنشاء أزرار العمليات"""
        buttons_frame = ttk_bs.Frame(parent)
        buttons_frame.pack(pady=20)

        ttk_bs.Button(
            buttons_frame,
            text="💾 حفظ",
            command=self.save_data,
            bootstyle="success",
            width=15
        ).pack(side=LEFT, padx=10)

        ttk_bs.Button(
            buttons_frame,
            text="❌ إلغاء",
            command=self.cancel,
            bootstyle="secondary",
            width=15
        ).pack(side=LEFT, padx=10)

    def load_categories(self, combo):
        """تحميل قائمة الفئات"""
        try:
            if hasattr(Category, 'get_all'):
                categories = Category.get_all()
                cat_names = [cat.name for cat in categories]
                combo['values'] = cat_names
        except Exception as e:
            print(f"خطأ في تحميل الفئات: {e}")
            combo['values'] = []

    def get_units(self):
        """الحصول على قائمة وحدات الصرف الثابتة"""
        # قائمة ثابتة لوحدات الصرف - غير مرتبطة بأي شاشة أخرى
        return [
            "عدد",
            "قطعة", 
            "كرتون",
            "علبة",
            "أخرى"
        ]

    def populate_form(self):
        """تعبئة النموذج ببيانات الصنف"""
        if not self.item:
            return

        self.name_var.set(self.item.name or "")
        self.code_var.set(self.item.code or "")
        self.unit_var.set(self.item.unit or "")
        # تحويل الكميات إلى أعداد صحيحة
        self.current_quantity_var.set(int(self.item.current_quantity or 0))
        self.minimum_quantity_var.set(int(self.item.minimum_quantity or 0))
        self.maximum_quantity_var.set(int(self.item.maximum_quantity or 0))
        self.unit_price_var.set(self.item.unit_price or 0.0)
        self.custody_type_var.set(self.item.custody_type or "")
        self.classification_var.set(self.item.classification or "")
        self.location_var.set(self.item.location or "")
        self.supplier_var.set(self.item.supplier or "")
        self.barcode_var.set(self.item.barcode or "")
        self.is_active_var.set(self.item.is_active)

        if self.item.description:
            self.description_text.insert("1.0", self.item.description)

        if self.item.expiry_date:
            self.expiry_date_var.set(str(self.item.expiry_date))

        # تحميل الفئة
        if self.item.category_id:
            try:
                if hasattr(Category, 'get_by_id'):
                    cat = Category.get_by_id(self.item.category_id)
                    if cat:
                        self.category_var.set(cat.name)
            except:
                pass

    def save_data(self):
        """حفظ البيانات"""
        # التحقق من صحة البيانات
        if not self.name_var.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم الصنف")
            return

        if not self.unit_var.get().strip():
            messagebox.showerror("خطأ", "يرجى اختيار وحدة القياس")
            return

        # جمع البيانات
        try:
            # البحث عن معرف الفئة
            category_id = None
            if self.category_var.get() and hasattr(Category, 'get_all'):
                categories = Category.get_all()
                for cat in categories:
                    if cat.name == self.category_var.get():
                        category_id = cat.id
                        break

            # تحويل تاريخ الانتهاء
            expiry_date = None
            if self.expiry_date_var.get():
                try:
                    expiry_date = datetime.strptime(self.expiry_date_var.get(), "%Y-%m-%d").date()
                except:
                    pass

            self.result = {
                'name': self.name_var.get().strip(),
                'code': self.code_var.get().strip() or None,
                'description': self.description_text.get("1.0", "end-1c").strip() or None,
                'category_id': category_id,
                'unit': self.unit_var.get().strip(),
                'current_quantity': self.current_quantity_var.get(),
                'minimum_quantity': self.minimum_quantity_var.get(),
                'maximum_quantity': self.maximum_quantity_var.get(),
                'unit_price': self.unit_price_var.get(),
                'custody_type': self.custody_type_var.get().strip() or None,
                'classification': self.classification_var.get().strip() or None,
                'location': self.location_var.get().strip() or None,
                'supplier': self.supplier_var.get().strip() or None,
                'barcode': self.barcode_var.get().strip() or None,
                'expiry_date': expiry_date,
                'is_active': self.is_active_var.get()
            }

            self.dialog.destroy()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في معالجة البيانات: {e}")

    def cancel(self):
        """إلغاء العملية"""
        self.dialog.destroy()