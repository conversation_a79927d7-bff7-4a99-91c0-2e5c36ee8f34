#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لمفاتيح الاختصار في نافذة المستفيد
Quick Test for Beneficiary Shortcuts
"""

import tkinter as tk
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_shortcuts():
    """اختبار مفاتيح الاختصار"""
    print("🧪 بدء اختبار مفاتيح الاختصار...")
    
    try:
        # استيراد النوافذ
        from ui.beneficiaries_window import AddBeneficiaryWindow
        
        # إنشاء نافذة رئيسية
        root = ttk_bs.Window(themename="flatly")
        root.title("🧪 اختبار مفاتيح الاختصار")
        root.geometry("400x300")
        
        # إنشاء كائن وهمي للنافذة الرئيسية
        class MockBeneficiariesWindow:
            def refresh_data(self):
                print("✅ تم تحديث البيانات")
        
        mock_window = MockBeneficiariesWindow()
        
        # دالة لفتح نافذة إضافة المستفيد
        def open_add_window():
            try:
                print("🔄 فتح نافذة إضافة المستفيد...")
                add_window = AddBeneficiaryWindow(
                    parent=root,
                    beneficiaries_window=mock_window,
                    edit_mode=False
                )
                print("✅ تم فتح النافذة - جرب مفاتيح F1, F2, F3, F4")
            except Exception as e:
                print(f"❌ خطأ في فتح النافذة: {e}")
        
        # إنشاء واجهة بسيطة
        title_label = ttk_bs.Label(
            root,
            text="🧪 اختبار مفاتيح الاختصار",
            font=("Arial", 16, "bold")
        )
        title_label.pack(pady=20)
        
        info_label = ttk_bs.Label(
            root,
            text="اضغط على الزر لفتح نافذة إضافة المستفيد\nثم جرب مفاتيح F1-F4",
            font=("Arial", 12)
        )
        info_label.pack(pady=10)
        
        open_btn = ttk_bs.Button(
            root,
            text="➕ فتح نافذة إضافة المستفيد",
            command=open_add_window,
            bootstyle="success",
            width=30
        )
        open_btn.pack(pady=20)
        
        # معلومات مفاتيح الاختصار
        shortcuts_frame = ttk_bs.LabelFrame(
            root,
            text="⌨️ مفاتيح الاختصار",
            padding=10
        )
        shortcuts_frame.pack(pady=10, padx=20, fill="x")
        
        shortcuts_info = [
            "F1 - حفظ المستفيد",
            "F2 - مسح النموذج",
            "F3 - نسخ البيانات",
            "F4 - لصق البيانات"
        ]
        
        for shortcut in shortcuts_info:
            label = ttk_bs.Label(
                shortcuts_frame,
                text=f"• {shortcut}",
                font=("Arial", 10)
            )
            label.pack(anchor="w")
        
        print("✅ تم إنشاء نافذة الاختبار")
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")

if __name__ == "__main__":
    test_shortcuts()
