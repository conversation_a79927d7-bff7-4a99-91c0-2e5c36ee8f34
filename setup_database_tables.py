#!/usr/bin/env python3
"""
إعداد جداول قاعدة البيانات الجديدة
Setup New Database Tables
"""

import sqlite3
import os
from database import db_manager

def setup_tables():
    """إنشاء الجداول الجديدة"""
    
    print("إعداد جداول قاعدة البيانات...")
    
    try:
        # جدول التصنيفات
        categories_table = """
        CREATE TABLE IF NOT EXISTS categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            description TEXT,
            is_active BOOLEAN DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        """
        db_manager.execute_query(categories_table)
        print("- تم إنشاء جدول التصنيفات")
        

        
        # التحقق من وجود عمود التصنيف في جدول الأصناف
        try:
            db_manager.execute_query("ALTER TABLE items ADD COLUMN category_id INTEGER REFERENCES categories(id)")
            print("- تم إضافة عمود التصنيف لجدول الأصناف")
        except:
            print("- عمود التصنيف موجود بالفعل")
        
        # إدراج بيانات أولية للتصنيفات
        categories_data = [
            ('أدوات مكتبية', 'أقلام، أوراق، مجلدات'),
            ('أجهزة إلكترونية', 'حاسوب، طابعات، شاشات'),
            ('مواد استهلاكية', 'مواد تنظيف، قرطاسية'),
            ('أثاث مكتبي', 'مكاتب، كراسي، خزائن'),
            ('معدات أمنية', 'كاميرات، أجهزة إنذار'),
            ('مواد غذائية', 'مشروبات، وجبات خفيفة'),
            ('أدوات صيانة', 'مفاتيح، براغي، أدوات'),
            ('مستلزمات طبية', 'إسعافات أولية، أدوية')
        ]
        
        for name, description in categories_data:
            try:
                db_manager.execute_query(
                    "INSERT OR IGNORE INTO categories (name, description) VALUES (?, ?)",
                    (name, description)
                )
            except:
                pass
        print("- تم إدراج البيانات الأولية للتصنيفات")
        

        
        # إنشاء فهارس لتحسين الأداء
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_categories_name ON categories(name)",
            "CREATE INDEX IF NOT EXISTS idx_categories_active ON categories(is_active)",
            "CREATE INDEX IF NOT EXISTS idx_items_category ON items(category_id)"
        ]
        
        for index_sql in indexes:
            try:
                db_manager.execute_query(index_sql)
            except:
                pass
        print("- تم إنشاء الفهارس")
        
        print("\nتم إعداد جميع الجداول بنجاح!")
        
        # عرض إحصائيات
        categories_count = db_manager.fetch_one("SELECT COUNT(*) FROM categories")[0]
        
        print(f"\nالإحصائيات:")
        print(f"   - عدد التصنيفات: {categories_count}")
        
        return True
        
    except Exception as e:
        print(f"خطأ في إعداد الجداول: {str(e)}")
        return False

def main():
    """تشغيل إعداد الجداول"""
    setup_tables()

if __name__ == "__main__":
    main()