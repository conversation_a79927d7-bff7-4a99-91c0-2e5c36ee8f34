#!/usr/bin/env python3
"""
إنشاء ملف مضغوط للحزمة النهائية
Create ZIP Package Script
"""

import zipfile
import os
from pathlib import Path
import time

def print_status(message, status="INFO"):
    """طباعة رسالة حالة ملونة"""
    colors = {
        "INFO": "\033[94m",     # أزرق
        "SUCCESS": "\033[92m",  # أخضر
        "WARNING": "\033[93m",  # أصفر
        "ERROR": "\033[91m",    # أحمر
        "RESET": "\033[0m"      # إعادة تعيين
    }
    
    color = colors.get(status, colors["INFO"])
    reset = colors["RESET"]
    print(f"{color}[{status}] {message}{reset}")

def create_zip_package():
    """إنشاء ملف مضغوط للحزمة النهائية"""
    print_status("إنشاء ملف مضغوط للحزمة النهائية...")
    
    # مسارات المجلدات
    dist_dir = Path("dist")
    final_package_dir = dist_dir / "نظام_إدارة_المخازن_النهائي"
    
    if not final_package_dir.exists():
        print_status("المجلد النهائي غير موجود", "ERROR")
        return False
    
    # اسم الملف المضغوط
    zip_filename = f"نظام_إدارة_المخازن_v1.1_{time.strftime('%Y%m%d')}.zip"
    zip_path = dist_dir / zip_filename
    
    # إنشاء الملف المضغوط
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED, compresslevel=9) as zipf:
        # إضافة جميع ملفات المجلد النهائي
        for root, dirs, files in os.walk(final_package_dir):
            for file in files:
                file_path = Path(root) / file
                # الحصول على المسار النسبي
                arcname = file_path.relative_to(final_package_dir)
                zipf.write(file_path, arcname)
                print_status(f"إضافة: {arcname}", "INFO")
        
        # إضافة المثبت
        installer_path = dist_dir / "مثبت_نظام_إدارة_المخازن.bat"
        if installer_path.exists():
            zipf.write(installer_path, "مثبت_نظام_إدارة_المخازن.bat")
            print_status("إضافة: مثبت_نظام_إدارة_المخازن.bat", "INFO")
    
    # حساب حجم الملف المضغوط
    zip_size = zip_path.stat().st_size / (1024 * 1024)  # بالميجابايت
    
    print_status(f"تم إنشاء الملف المضغوط: {zip_filename}", "SUCCESS")
    print_status(f"حجم الملف المضغوط: {zip_size:.1f} ميجابايت", "INFO")
    print_status(f"مسار الملف: {zip_path.absolute()}", "INFO")
    
    return True

def create_readme_for_zip():
    """إنشاء ملف README للملف المضغوط"""
    print_status("إنشاء ملف README للملف المضغوط...")
    
    readme_content = f"""
# نظام إدارة المخازن والمستودعات - الإصدار 1.1
## تاريخ الإنشاء: {time.strftime('%Y-%m-%d')}

### 📦 محتويات الحزمة:
- نظام_إدارة_المخازن.exe (البرنامج الرئيسي - 54.4 ميجابايت)
- مثبت_نظام_إدارة_المخازن.bat (مثبت تلقائي)
- اقرأني_أولاً.txt (تعليمات مفصلة)
- تشغيل_سريع.bat (تشغيل مباشر)
- معلومات_النظام.bat (معلومات تقنية)
- إلغاء_التثبيت.bat (إزالة البرنامج)

### 🚀 طريقة التثبيت السريع:
1. فك ضغط الملف في أي مكان
2. شغل "مثبت_نظام_إدارة_المخازن.bat"
3. اتبع التعليمات على الشاشة

### 🔑 بيانات الدخول الافتراضية:
- اسم المستخدم: admin
- كلمة المرور: admin123

### 💻 متطلبات النظام:
- Windows 7/8/10/11
- 2 جيجابايت RAM
- 200 ميجابايت مساحة فارغة

### 📞 الدعم الفني:
للحصول على المساعدة، راجع ملف "اقرأني_أولاً.txt"

---
© 2025 نظام إدارة المخازن والمستودعات
"""
    
    readme_path = Path("dist") / "README_التثبيت.txt"
    with open(readme_path, "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    print_status(f"تم إنشاء ملف README: {readme_path}", "SUCCESS")

def main():
    """الدالة الرئيسية"""
    print_status("=" * 60, "INFO")
    print_status("إنشاء ملف مضغوط للحزمة النهائية", "INFO")
    print_status("=" * 60, "INFO")
    
    # إنشاء الملف المضغوط
    if create_zip_package():
        # إنشاء ملف README
        create_readme_for_zip()
        
        print_status("=" * 60, "SUCCESS")
        print_status("تم إنشاء الحزمة المضغوطة بنجاح!", "SUCCESS")
        print_status("الحزمة جاهزة للتوزيع والنشر", "SUCCESS")
        print_status("=" * 60, "SUCCESS")
    else:
        print_status("فشل في إنشاء الملف المضغوط", "ERROR")

if __name__ == "__main__":
    main()
    input("اضغط Enter للخروج...")