#!/usr/bin/env python3
"""
شاشة إدارة المستخدمين
Users Management Window
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime
from ui.global_shortcuts import GlobalShortcuts, ContextHandler
import bcrypt

from config import APP_CONFIG, UI_CONFIG, get_message
from models import User
from database import db_manager
from permissions_manager import check_permission, can_access

class UsersManagementWindow:
    """شاشة إدارة المستخدمين"""
    
    def __init__(self, parent, main_window):
        self.parent = parent
        self.main_window = main_window
        self.users_window = None
        self.users_tree = None
        self.search_var = None
        self.status_var = None
        self.filter_role_var = None
        
        # فحص الصلاحيات قبل إنشاء النافذة
        if not can_access(self.main_window.current_user, 'users'):
            messagebox.showerror(
                "غير مسموح", 
                "ليس لديك صلاحية للوصول إلى إدارة المستخدمين"
            )
            return
        
        self.setup_window()
        self.load_users()
    
    def setup_window(self):
        """إعداد النافذة"""
        self.users_window = tk.Toplevel(self.parent)
        self.users_window.title("👥 إدارة المستخدمين")
        self.users_window.geometry("1200x700")
        self.users_window.resizable(True, True)
        
        # توسيط النافذة
        self.center_window()
        
        # إعداد المحتوى
        self.setup_content()
        
        # جعل النافذة في المقدمة
        self.users_window.lift()
        self.users_window.focus_force()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.users_window.update_idletasks()
        
        screen_width = self.users_window.winfo_screenwidth()
        screen_height = self.users_window.winfo_screenheight()
        
        window_width = 1200
        window_height = 700
        
        x = max(0, (screen_width - window_width) // 2)
        y = max(0, (screen_height - window_height) // 2)
        
        self.users_window.geometry(f"{window_width}x{window_height}+{x}+{y}")
    
    def setup_content(self):
        """إعداد محتوى النافذة"""
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.users_window)
        main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # شريط العنوان والأدوات
        self.create_header(main_frame)
        
        # شريط البحث والفلاتر
        self.create_filters(main_frame)
        
        # جدول المستخدمين
        self.create_users_table(main_frame)
        
        # شريط الحالة
        self.create_status_bar(main_frame)
    
    def create_header(self, parent):
        """إنشاء شريط العنوان والأدوات"""
        header_frame = ttk_bs.Frame(parent)
        header_frame.pack(fill=X, pady=(0, 10))
        
        # العنوان
        title_label = ttk_bs.Label(
            header_frame,
            text="👥 إدارة المستخدمين",
            bootstyle="primary"
        )
        title_label.pack(side=LEFT)
        
        # أزرار الأدوات
        tools_frame = ttk_bs.Frame(header_frame)
        tools_frame.pack(side=RIGHT)
        
        # زر مستخدم جديد - فقط للمستخدمين الذين لديهم صلاحية إضافة مستخدمين
        if check_permission(self.main_window.current_user, 'users', 'add'):
            new_btn = ttk_bs.Button(
                tools_frame,
                text="➕ مستخدم جديد",
                command=self.add_user,
                bootstyle="success",
                width=20
            )
            new_btn.pack(side=RIGHT, padx=5)
        
        # زر تحديث
        refresh_btn = ttk_bs.Button(
            tools_frame,
            text="🔄 تحديث",
            command=self.load_users,
            bootstyle="outline-primary",
            width=15
        )
        refresh_btn.pack(side=RIGHT, padx=5)
    
    def create_filters(self, parent):
        """إنشاء شريط البحث والفلاتر"""
        filters_frame = ttk_bs.LabelFrame(parent, text="🔍 البحث والفلاتر", bootstyle="info")
        filters_frame.pack(fill=X, pady=(0, 10))
        
        # الصف الأول - البحث والدور
        row1_frame = ttk_bs.Frame(filters_frame)
        row1_frame.pack(fill=X, padx=10, pady=5)
        
        # البحث
        ttk_bs.Label(row1_frame, text="البحث:").pack(side=LEFT, padx=(0, 5))
        self.search_var = tk.StringVar()
        search_entry = ttk_bs.Entry(
            row1_frame,
            textvariable=self.search_var,
            width=30
        )
        search_entry.pack(side=LEFT, padx=(0, 20))
        search_entry.bind('<KeyRelease>', lambda e: self.filter_users())
        
        # الدور
        ttk_bs.Label(row1_frame, text="الدور:").pack(side=LEFT, padx=(0, 5))
        self.filter_role_var = tk.StringVar(value="الكل")
        role_combo = ttk_bs.Combobox(
            row1_frame,
            textvariable=self.filter_role_var,
            values=["الكل", "مدير", "مستخدم"],
            state="readonly",
            width=15
        )
        role_combo.pack(side=LEFT, padx=(0, 20))
        role_combo.bind('<<ComboboxSelected>>', lambda e: self.filter_users())
        
        # زر مسح الفلاتر
        clear_btn = ttk_bs.Button(
            row1_frame,
            text="🗑️ مسح الفلاتر",
            command=self.clear_filters,
            bootstyle="outline-warning",
            width=18
        )
        clear_btn.pack(side=LEFT, padx=20)
    
    def create_users_table(self, parent):
        """إنشاء جدول المستخدمين"""
        table_frame = ttk_bs.LabelFrame(parent, text="📋 قائمة المستخدمين", bootstyle="primary")
        table_frame.pack(fill=BOTH, expand=True, pady=(0, 10))
        
        # إنشاء Treeview - الأعمدة المطلوبة
        columns = ("id", "username", "full_name", "is_admin", "user_type", "is_active", "last_login", "created_at")
        self.users_tree = ttk.Treeview(
            table_frame,
            columns=columns,
            show="headings",
            height=15
        )

        # تعيين عناوين الأعمدة
        headings = {
            "id": "الرقم",
            "username": "اسم المستخدم",
            "full_name": "الاسم بالكامل",
            "is_admin": "مدير",
            "user_type": "نوع المستخدم",
            "is_active": "نشط",
            "last_login": "آخر دخول",
            "created_at": "تاريخ الإنشاء"
        }

        for col, heading in headings.items():
            self.users_tree.heading(col, text=heading)
            self.users_tree.column(col, width=120, anchor="center")

        # تعيين عرض أعمدة محددة
        self.users_tree.column("id", width=60)
        self.users_tree.column("username", width=130)
        self.users_tree.column("full_name", width=180)
        self.users_tree.column("is_admin", width=80)
        self.users_tree.column("user_type", width=120)
        self.users_tree.column("is_active", width=80)
        self.users_tree.column("last_login", width=150)
        self.users_tree.column("created_at", width=150)
        
        # شريط التمرير
        scrollbar_y = ttk.Scrollbar(table_frame, orient=VERTICAL, command=self.users_tree.yview)
        scrollbar_x = ttk.Scrollbar(table_frame, orient=HORIZONTAL, command=self.users_tree.xview)
        self.users_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        # تخطيط الجدول
        self.users_tree.pack(side=LEFT, fill=BOTH, expand=True, padx=(10, 0), pady=10)
        scrollbar_y.pack(side=RIGHT, fill=Y, pady=10)
        scrollbar_x.pack(side=BOTTOM, fill=X, padx=10)
        
        # ربط الأحداث
        self.users_tree.bind('<Double-1>', self.edit_user)
        self.users_tree.bind('<Button-3>', self.show_context_menu)
    
    def create_status_bar(self, parent):
        """إنشاء شريط الحالة"""
        status_frame = ttk_bs.Frame(parent)
        status_frame.pack(fill=X)
        
        self.status_var = tk.StringVar(value="جاري تحميل البيانات...")
        status_label = ttk_bs.Label(
            status_frame,
            textvariable=self.status_var,
            bootstyle="secondary"
        )
        status_label.pack(side=LEFT)
    
    def load_users(self):
        """تحميل المستخدمين"""
        try:
            # مسح البيانات الحالية
            for item in self.users_tree.get_children():
                self.users_tree.delete(item)

            self.status_var.set("جاري تحميل البيانات...")

            # تحميل البيانات من قاعدة البيانات مع الأدوار
            query = """
                SELECT u.*,
                       GROUP_CONCAT(r.description, ', ') as user_roles
                FROM users u
                LEFT JOIN user_roles ur ON u.id = ur.user_id
                LEFT JOIN roles r ON ur.role_id = r.id
                GROUP BY u.id
                ORDER BY u.full_name
            """

            from database import db_manager
            rows = db_manager.fetch_all(query)

            # إضافة البيانات للجدول
            for row in rows:
                # تنسيق التاريخ
                last_login = ""
                if row["last_login"]:
                    try:
                        from datetime import datetime
from ui.global_shortcuts import GlobalShortcuts, ContextHandler
                        if isinstance(row["last_login"], str):
                            dt = datetime.fromisoformat(row["last_login"])
                        else:
                            dt = row["last_login"]
                        last_login = dt.strftime('%Y-%m-%d %H:%M')
                    except:
                        last_login = str(row["last_login"])

                created_at = ""
                if row["created_at"]:
                    try:
                        from datetime import datetime
from ui.global_shortcuts import GlobalShortcuts, ContextHandler
                        if isinstance(row["created_at"], str):
                            dt = datetime.fromisoformat(row["created_at"])
                        else:
                            dt = row["created_at"]
                        created_at = dt.strftime('%Y-%m-%d %H:%M')
                    except:
                        created_at = str(row["created_at"])

                # تحديد نوع المستخدم
                user_type = row["user_roles"] or "مستخدم"
                if not user_type or user_type == "None":
                    user_type = "مدير النظام" if row["is_admin"] else "مستخدم"

                self.users_tree.insert('', 'end', values=(
                    row["id"],
                    row["username"],
                    row["full_name"],
                    "نعم" if row["is_admin"] else "لا",
                    user_type,
                    "نعم" if row["is_active"] else "لا",
                    last_login,
                    created_at
                ))

            # تحديث شريط الحالة
            count = len(rows)
            self.status_var.set(f"تم تحميل {count} مستخدم")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل المستخدمين: {e}")
            self.status_var.set("خطأ في تحميل البيانات")
    
    def filter_users(self):
        """فلترة المستخدمين"""
        # هذه دالة مبسطة - يمكن تطويرها لاحقاً
        self.load_users()
    
    def clear_filters(self):
        """مسح جميع الفلاتر"""
        self.search_var.set("")
        self.filter_role_var.set("الكل")
        self.load_users()
    
    def add_user(self):
        """إضافة مستخدم جديد"""
        # فحص الصلاحيات
        if not check_permission(self.main_window.current_user, 'users', 'add'):
            messagebox.showwarning("غير مسموح", "ليس لديك صلاحية لإضافة مستخدمين")
            return
        from ui.add_user_simple import AddUserSimple
        AddUserSimple(self.users_window, self)

        # تفعيل مفاتيح الاختصار العامة
        self.setup_shortcuts()

    def edit_user(self, event=None):
        """تعديل مستخدم"""
        # فحص الصلاحيات
        if not check_permission(self.main_window.current_user, 'users', 'edit'):
            messagebox.showwarning("غير مسموح", "ليس لديك صلاحية لتعديل المستخدمين")
            return
            
        selection = self.users_tree.selection()
        if selection:
            item = self.users_tree.item(selection[0])
            user_id = item['values'][0]
            from ui.add_user_simple import AddUserSimple
            AddUserSimple(self.users_window, self, user_id)
    
    def show_user_form(self, user_id=None):
        """عرض نموذج المستخدم - استخدم add_user بدلاً من هذه الدالة"""
        # استخدام الشاشة الجديدة بدلاً من هذه الدالة القديمة
        if user_id:
            self.edit_user()
        else:
            self.add_user()
        return

    
    def show_context_menu(self, event):
        """عرض القائمة السياقية"""
        selection = self.users_tree.selection()
        if selection:
            context_menu = tk.Menu(self.users_window, tearoff=0)
            
            # إضافة خيارات حسب الصلاحيات
            if check_permission(self.main_window.current_user, 'users', 'edit'):
                context_menu.add_command(label="تعديل", command=self.edit_user)
                context_menu.add_command(label="تغيير كلمة المرور", command=self.change_password)
                context_menu.add_separator()
                context_menu.add_command(label="تفعيل/إلغاء تفعيل", command=self.toggle_user_status)
          
            if check_permission(self.main_window.current_user, 'users', 'delete'):
                if context_menu.index("end") is not None:  # إذا كان هناك عناصر أخرى
                    context_menu.add_separator()
                context_menu.add_command(label="حذف", command=self.delete_user)
            
            # إذا لم تكن هناك خيارات متاحة، لا تظهر القائمة
            if context_menu.index("end") is not None:
                try:
                    context_menu.tk_popup(event.x_root, event.y_root)
                finally:
                    context_menu.grab_release()
    
    def change_password(self):
        """تغيير كلمة المرور"""
        selection = self.users_tree.selection()
        if selection:
            item = self.users_tree.item(selection[0])
            user_id = item['values'][0]
            username = item['values'][1]
            
            # طلب كلمة المرور الجديدة
            new_password = simpledialog.askstring(
                "تغيير كلمة المرور",
                f"أدخل كلمة المرور الجديدة للمستخدم '{username}':",
                show='*'
            )
            
            if new_password:
                confirm_password = simpledialog.askstring(
                    "تأكيد كلمة المرور",
                    "أدخل كلمة المرور مرة أخرى للتأكيد:",
                    show='*'
                )
                
                if new_password == confirm_password:
                    try:
                        user = User.get_by_id(user_id)
                        if user:
                            import bcrypt
                            from datetime import datetime
from ui.global_shortcuts import GlobalShortcuts, ContextHandler
                            user.password_hash = bcrypt.hashpw(
                                new_password.encode('utf-8'),
                                bcrypt.gensalt()
                            ).decode('utf-8')
                            user.updated_at = datetime.now()

                            if user.save():  # استخدام save بدلاً من update
                                messagebox.showinfo("نجح", "تم تغيير كلمة المرور بنجاح")
                            else:
                                messagebox.showerror("خطأ", "فشل في تغيير كلمة المرور")
                    except Exception as e:
                        messagebox.showerror("خطأ", f"خطأ في تغيير كلمة المرور: {e}")
                else:
                    messagebox.showerror("خطأ", "كلمة المرور وتأكيدها غير متطابقتين")
    
    def toggle_user_status(self):
        """تفعيل/إلغاء تفعيل المستخدم"""
        selection = self.users_tree.selection()
        if selection:
            item = self.users_tree.item(selection[0])
            user_id = item['values'][0]
            username = item['values'][1]
            current_status = item['values'][5] == "نعم"  # العمود الخامس هو "نشط"

            new_status = not current_status
            action = "تفعيل" if new_status else "إلغاء تفعيل"

            if messagebox.askyesno("تأكيد", f"هل تريد {action} المستخدم '{username}'؟"):
                try:
                    user = User.get_by_id(user_id)
                    if user:
                        user.is_active = new_status
                        from datetime import datetime
from ui.global_shortcuts import GlobalShortcuts, ContextHandler
                        user.updated_at = datetime.now()

                        if user.save():  # استخدام save بدلاً من update
                            messagebox.showinfo("نجح", f"تم {action} المستخدم بنجاح")
                            self.load_users()
                        else:
                            messagebox.showerror("خطأ", f"فشل في {action} المستخدم")
                except Exception as e:
                    messagebox.showerror("خطأ", f"خطأ في {action} المستخدم: {e}")
    
    def delete_user(self):
        """حذف المستخدم"""
        # فحص الصلاحيات
        if not check_permission(self.main_window.current_user, 'users', 'delete'):
            messagebox.showwarning("غير مسموح", "ليس لديك صلاحية لحذف المستخدمين")
            return
            
        selection = self.users_tree.selection()
        if selection:
            item = self.users_tree.item(selection[0])
            user_id = item['values'][0]
            username = item['values'][1]
            
            if messagebox.askyesno("تأكيد الحذف", f"هل تريد حذف المستخدم '{username}'؟\n\nتحذير: هذا الإجراء لا يمكن التراجع عنه!"):
                try:
                    user = User.get_by_id(user_id)
                    if user:
                        if user.delete():
                            messagebox.showinfo("نجح", "تم حذف المستخدم بنجاح")
                            self.load_users()
                        else:
                            messagebox.showerror("خطأ", "فشل في حذف المستخدم")
                except Exception as e:
                    messagebox.showerror("خطأ", f"خطأ في حذف المستخدم: {e}")
    def setup_shortcuts(self):
        """إعداد مفاتيح الاختصار العامة"""
        try:
            # إنشاء معالج السياق
            self.context_handler = ContextHandler()
            
            # تعيين دوال العمليات (يمكن تخصيصها حسب النافذة)
            self.context_handler.set_save_callback(self.shortcut_save)
            self.context_handler.set_delete_callback(self.shortcut_delete)
            self.context_handler.set_copy_callback(self.shortcut_copy)
            self.context_handler.set_paste_callback(self.shortcut_paste)
            
            # تفعيل مفاتيح الاختصار
            window = getattr(self, 'window', None) or getattr(self, 'parent', None)
            if window:
                self.global_shortcuts = GlobalShortcuts(window, self.context_handler)
        except Exception as e:
            print(f"خطأ في إعداد مفاتيح الاختصار: {e}")
    
    def shortcut_save(self):
        """عملية الحفظ (F1)"""
        try:
            if hasattr(self, 'save_data'):
                self.save_data()
            elif hasattr(self, 'save_changes'):
                self.save_changes()
            elif hasattr(self, 'save'):
                self.save()
            else:
                print("عملية الحفظ غير متاحة")
        except Exception as e:
            print(f"خطأ في عملية الحفظ: {e}")
    
    def shortcut_delete(self):
        """عملية الحذف (F2)"""
        try:
            if hasattr(self, 'delete_selected'):
                self.delete_selected()
            elif hasattr(self, 'delete_item'):
                self.delete_item()
            elif hasattr(self, 'delete'):
                self.delete()
            else:
                print("عملية الحذف غير متاحة")
        except Exception as e:
            print(f"خطأ في عملية الحذف: {e}")
    
    def shortcut_copy(self):
        """عملية النسخ (F3)"""
        try:
            if hasattr(self, 'copy_data'):
                self.copy_data()
            else:
                # نسخ عامة للبيانات المحددة
                import pyperclip
                pyperclip.copy("تم النسخ من النافذة")
                print("تم نسخ البيانات")
        except Exception as e:
            print(f"خطأ في عملية النسخ: {e}")
    
    def shortcut_paste(self):
        """عملية اللصق (F4)"""
        try:
            if hasattr(self, 'paste_data'):
                self.paste_data()
            else:
                import pyperclip
                clipboard_text = pyperclip.paste()
                if clipboard_text:
                    print(f"تم لصق: {clipboard_text[:50]}")
                else:
                    print("لا توجد بيانات في الحافظة")
        except Exception as e:
            print(f"خطأ في عملية اللصق: {e}")

# اختبار النافذة
if __name__ == "__main__":
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    # إنشاء نافذة الاختبار
    window = UsersManagementWindow(root, None)
    root.mainloop()
