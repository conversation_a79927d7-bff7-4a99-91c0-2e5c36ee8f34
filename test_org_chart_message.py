#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار رسالة النجاح في شاشة الجدول التنظيمي
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import tkinter as tk
from ui.success_message import AutoSuccessMessage

def test_success_message():
    """اختبار رسالة النجاح"""
    print("🧪 اختبار رسالة النجاح في الجدول التنظيمي...")
    
    # إنشاء نافذة رئيسية للاختبار
    root = tk.Tk()
    root.title("اختبار رسالة النجاح")
    root.geometry("400x300")
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    # اختبار رسائل مختلفة
    test_messages = [
        "تم تحديث الصنف بنجاح\n\nاسم الصنف: مواد غذائية\nرقم الصنف: 101",
        "تم تحديث الصنف بنجاح\n\nاسم الصنف: أدوات مكتبية\nرقم الصنف: 102",
        "",  # رسالة فارغة
        None,  # رسالة None
        "رسالة قصيرة",
        "رسالة طويلة جداً تحتوي على نص كثير لاختبار كيفية عرض النص الطويل في رسالة النجاح والتأكد من أن التنسيق يعمل بشكل صحيح"
    ]
    
    def show_next_message(index=0):
        if index < len(test_messages):
            message = test_messages[index]
            print(f"\n📋 اختبار الرسالة {index + 1}: {repr(message)}")
            
            try:
                AutoSuccessMessage.show(root, message, duration=2000)
                # جدولة الرسالة التالية بعد 3 ثوانٍ
                root.after(3000, lambda: show_next_message(index + 1))
            except Exception as e:
                print(f"❌ خطأ في عرض الرسالة: {e}")
                root.after(1000, lambda: show_next_message(index + 1))
        else:
            print("\n✅ انتهى اختبار جميع الرسائل")
            root.after(2000, root.quit)
    
    # بدء الاختبار
    root.after(1000, show_next_message)
    
    # تشغيل الحلقة الرئيسية
    root.mainloop()
    root.destroy()

def test_organizational_chart_message():
    """اختبار رسالة الجدول التنظيمي تحديداً"""
    print("\n🔧 اختبار رسالة الجدول التنظيمي...")
    
    # محاكاة البيانات
    item_name = "مواد غذائية"
    item_code = "101"
    
    # إنشاء نافذة للاختبار
    root = tk.Tk()
    root.title("اختبار الجدول التنظيمي")
    root.geometry("400x300")
    root.withdraw()
    
    # محاكاة الرسالة الفعلية
    success_message = f"تم تحديث الصنف بنجاح\n\nاسم الصنف: {item_name}\nرقم الصنف: {item_code}"
    print(f"الرسالة المُرسلة: {repr(success_message)}")
    
    try:
        AutoSuccessMessage.show(root, success_message, duration=5000)
        print("✅ تم عرض الرسالة بنجاح")
        
        # انتظار 6 ثوانٍ ثم إغلاق
        root.after(6000, root.quit)
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في عرض رسالة الجدول التنظيمي: {e}")
    
    root.destroy()

def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار رسائل النجاح")
    print("=" * 50)
    
    # اختبار رسائل مختلفة
    test_success_message()
    
    # اختبار رسالة الجدول التنظيمي
    test_organizational_chart_message()
    
    print("\n✅ انتهى الاختبار")

if __name__ == "__main__":
    main()
