"""
مسح جميع البيانات من الجداول
Clear All Data from Tables
"""

import tkinter as tk
from tkinter import messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *

from database import db_manager
from models import Unit, Department

def clear_all_data():
    """مسح جميع البيانات من جميع الجداول"""
    
    try:
        print("بدء عملية مسح البيانات...")
        
        # مسح بيانات الأقسام (إذا كان الجدول موجود)
        try:
            sections_count = db_manager.fetch_one("SELECT COUNT(*) as count FROM sections")
            if sections_count:
                db_manager.execute_query("DELETE FROM sections")
                print(f"تم مسح {sections_count['count']} قسم")
        except Exception as e:
            print(f"لا يوجد جدول أقسام أو خطأ: {e}")
        
        # مسح بيانات المستفيدين (إذا كان الجدول موجود)
        try:
            beneficiaries_count = db_manager.fetch_one("SELECT COUNT(*) as count FROM beneficiaries")
            if beneficiaries_count:
                db_manager.execute_query("DELETE FROM beneficiaries")
                print(f"تم مسح {beneficiaries_count['count']} مستفيد")
        except Exception as e:
            print(f"لا يوجد جدول مستفيدين أو خطأ: {e}")
        
        # مسح بيانات الإدارات
        try:
            departments_count = db_manager.fetch_one("SELECT COUNT(*) as count FROM departments")
            if departments_count:
                db_manager.execute_query("DELETE FROM departments")
                print(f"تم مسح {departments_count['count']} إدارة")
        except Exception as e:
            print(f"خطأ في مسح الإدارات: {e}")
        
        # مسح بيانات الوحدات
        try:
            units_count = db_manager.fetch_one("SELECT COUNT(*) as count FROM units")
            if units_count:
                db_manager.execute_query("DELETE FROM units")
                print(f"تم مسح {units_count['count']} وحدة")
        except Exception as e:
            print(f"خطأ في مسح الوحدات: {e}")
        
        # إعادة تعيين AUTO_INCREMENT للجداول
        try:
            db_manager.execute_query("DELETE FROM sqlite_sequence WHERE name='units'")
            db_manager.execute_query("DELETE FROM sqlite_sequence WHERE name='departments'")
            db_manager.execute_query("DELETE FROM sqlite_sequence WHERE name='sections'")
            db_manager.execute_query("DELETE FROM sqlite_sequence WHERE name='beneficiaries'")
            print("تم إعادة تعيين معرفات الجداول")
        except Exception as e:
            print(f"خطأ في إعادة تعيين المعرفات: {e}")
        
        print("✅ تم مسح جميع البيانات بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في مسح البيانات: {e}")
        return False

def create_clear_data_app():
    """إنشاء تطبيق مسح البيانات"""
    
    # إنشاء النافذة الرئيسية
    root = ttk_bs.Window(themename="cosmo")
    root.title("مسح جميع البيانات")
    root.geometry("600x500")
    
    # الإطار الرئيسي
    main_frame = ttk_bs.Frame(root)
    main_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)
    
    # العنوان
    ttk_bs.Label(
        main_frame,
        text="🗑️ مسح جميع البيانات",
        font=("Arial", 20, "bold"),
        bootstyle="danger"
    ).pack(pady=20)
    
    # تحذير
    warning_frame = ttk_bs.LabelFrame(main_frame, text="⚠️ تحذير مهم", bootstyle="warning")
    warning_frame.pack(fill=X, pady=10)
    
    warning_text = """
⚠️ تحذير: هذه العملية ستمسح جميع البيانات نهائياً!

سيتم مسح البيانات من:
• جدول الوحدات (units)
• جدول الإدارات (departments) 
• جدول الأقسام (sections)
• جدول المستفيدين (beneficiaries)

❌ لا يمكن التراجع عن هذه العملية!
✅ تأكد من أنك تريد مسح جميع البيانات قبل المتابعة
    """
    
    ttk_bs.Label(
        warning_frame,
        text=warning_text,
        font=("Arial", 11),
        justify=LEFT,
        bootstyle="warning"
    ).pack(padx=15, pady=15, anchor=W)
    
    # معلومات البيانات الحالية
    info_frame = ttk_bs.LabelFrame(main_frame, text="📊 البيانات الحالية", bootstyle="info")
    info_frame.pack(fill=X, pady=10)
    
    info_label = ttk_bs.Label(info_frame, text="جاري تحميل معلومات البيانات...")
    info_label.pack(padx=15, pady=15)
    
    def update_data_info():
        """تحديث معلومات البيانات الحالية"""
        try:
            info_text = "البيانات الحالية في قاعدة البيانات:\n\n"
            
            # عدد الوحدات
            try:
                units_count = db_manager.fetch_one("SELECT COUNT(*) as count FROM units")
                info_text += f"• الوحدات: {units_count['count'] if units_count else 0}\n"
            except:
                info_text += "• الوحدات: 0 (الجدول غير موجود)\n"
            
            # عدد الإدارات
            try:
                departments_count = db_manager.fetch_one("SELECT COUNT(*) as count FROM departments")
                info_text += f"• الإدارات: {departments_count['count'] if departments_count else 0}\n"
            except:
                info_text += "• الإدارات: 0 (الجدول غير موجود)\n"
            
            # عدد الأقسام
            try:
                sections_count = db_manager.fetch_one("SELECT COUNT(*) as count FROM sections")
                info_text += f"• الأقسام: {sections_count['count'] if sections_count else 0}\n"
            except:
                info_text += "• الأقسام: 0 (الجدول غير موجود)\n"
            
            # عدد المستفيدين
            try:
                beneficiaries_count = db_manager.fetch_one("SELECT COUNT(*) as count FROM beneficiaries")
                info_text += f"• المستفيدين: {beneficiaries_count['count'] if beneficiaries_count else 0}\n"
            except:
                info_text += "• المستفيدين: 0 (الجدول غير موجود)\n"
            
            info_label.config(text=info_text)
            
        except Exception as e:
            info_label.config(text=f"خطأ في قراءة البيانات: {e}")
    
    # تحديث معلومات البيانات عند بدء التطبيق
    update_data_info()
    
    # أزرار العمليات
    buttons_frame = ttk_bs.Frame(main_frame)
    buttons_frame.pack(fill=X, pady=20)
    
    def confirm_and_clear():
        """تأكيد ومسح البيانات"""
        # تأكيد أول
        result1 = messagebox.askyesno(
            "تأكيد المسح",
            "هل أنت متأكد من أنك تريد مسح جميع البيانات؟\n\n"
            "⚠️ هذه العملية لا يمكن التراجع عنها!"
        )
        
        if not result1:
            return
        
        # تأكيد ثاني
        result2 = messagebox.askyesno(
            "تأكيد نهائي",
            "تأكيد أخير!\n\n"
            "سيتم مسح جميع البيانات من:\n"
            "• الوحدات\n"
            "• الإدارات\n"
            "• الأقسام\n"
            "• المستفيدين\n\n"
            "هل تريد المتابعة؟"
        )
        
        if not result2:
            return
        
        # تنفيذ المسح
        if clear_all_data():
            messagebox.showinfo(
                "تم بنجاح",
                "✅ تم مسح جميع البيانات بنجاح!\n\n"
                "جميع الجداول فارغة الآن."
            )
            # تحديث معلومات البيانات
            update_data_info()
        else:
            messagebox.showerror(
                "فشل",
                "❌ فشل في مسح البيانات!\n\n"
                "تحقق من سجل الأخطاء."
            )
    
    def test_window_sizes():
        """اختبار مقاسات الشاشات بعد الإصلاح"""
        try:
            from ui.units_window import UnitsWindow
            from ui.departments_window import DepartmentsWindow
            from models import User
            
            # إنشاء مستخدم وهمي
            admin_user = User(id=1, username="admin", full_name="مدير", is_admin=True, is_active=True)
            
            # فتح شاشة الوحدات
            print("فتح شاشة الوحدات...")
            units_window = UnitsWindow(root, admin_user)
            
            # فتح شاشة الإدارات
            print("فتح شاشة الإدارات...")
            dept_window = DepartmentsWindow(root)
            
            messagebox.showinfo(
                "اختبار المقاسات",
                "تم فتح كلا الشاشتين!\n\n"
                "تحقق من أن:\n"
                "✅ شاشة الوحدات: 1000x700\n"
                "✅ شاشة الإدارات: 1000x700\n"
                "✅ لا يمكن تكبير أو تصغير أي منهما"
            )
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في اختبار الشاشات:\n{str(e)}")
    
    # الأزرار
    ttk_bs.Button(
        buttons_frame,
        text="🔄 تحديث معلومات البيانات",
        command=update_data_info,
        bootstyle="info",
        width=30
    ).pack(pady=5)
    
    ttk_bs.Button(
        buttons_frame,
        text="🧪 اختبار مقاسات الشاشات",
        command=test_window_sizes,
        bootstyle="success",
        width=30
    ).pack(pady=5)
    
    ttk_bs.Button(
        buttons_frame,
        text="🗑️ مسح جميع البيانات",
        command=confirm_and_clear,
        bootstyle="danger",
        width=30
    ).pack(pady=10)
    
    ttk_bs.Button(
        buttons_frame,
        text="❌ إغلاق",
        command=root.quit,
        bootstyle="secondary",
        width=30
    ).pack(pady=5)
    
    return root

def main():
    """الدالة الرئيسية"""
    print("بدء تطبيق مسح البيانات...")
    
    try:
        # إنشاء وتشغيل التطبيق
        root = create_clear_data_app()
        root.mainloop()
        
    except Exception as e:
        print(f"خطأ في التطبيق: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()