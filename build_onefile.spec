# -*- mode: python ; coding: utf-8 -*-
# ملف إنشاء EXE واحد مستقل - محسن للسرعة

import os
import sys
from pathlib import Path

# تحديد مسار المشروع
project_root = Path.cwd()
sys.path.insert(0, str(project_root))

# تحديد الملفات والمجلدات المطلوبة
datas = []

# إضافة الملفات والمجلدات إذا كانت موجودة
if os.path.exists('ui'):
    datas.append(('ui', 'ui'))
if os.path.exists('utils'):
    datas.append(('utils', 'utils'))
if os.path.exists('assets'):
    datas.append(('assets', 'assets'))
if os.path.exists('data'):
    datas.append(('data', 'data'))

# إضافة الملفات الفردية المهمة فقط
for file_pattern in ['*.db', '*.sqlite', 'settings.json']:
    import glob
    for file in glob.glob(file_pattern):
        datas.append((file, '.'))

# تحديد المكتبات المخفية المطلوبة - مبسط للسرعة
hiddenimports = [
    # واجهة المستخدم الأساسية
    'tkinter',
    'tkinter.ttk',
    'tkinter.messagebox',
    'tkinter.filedialog',
    'tkinter.font',
    
    # ttkbootstrap
    'ttkbootstrap',
    'ttkbootstrap.constants',
    'ttkbootstrap.themes',
    'ttkbootstrap.style',
    
    # معالجة الصور
    'PIL',
    'PIL.Image',
    'PIL.ImageTk',
    
    # قاعدة البيانات
    'sqlite3',
    
    # المكتبات الأساسية
    'datetime',
    'pathlib',
    'threading',
    'json',
    'os',
    'sys',
    'logging',
    'hashlib',
    'uuid',
    'base64',
    
    # ملفات Excel والتقارير
    'openpyxl',
    'reportlab',
    'reportlab.pdfgen',
    'reportlab.lib',
    
    # معالجة البيانات
    'pandas',
    
    # الأمان والتشفير
    'bcrypt',
    'cryptography',
    
    # وحدات التطبيق
    'config',
    'database',
    'models',
    'auth_manager',
    'font_manager',
    'safe_window_manager',
]

# تحليل الملف الرئيسي
a = Analysis(
    ['main.py'],
    pathex=[str(project_root)],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # ملفات الاختبار
        'test_*',
        'tests',
        'pytest',
        'unittest',
        
        # ملفات التطوير
        '__pycache__',
        '*.pyc',
        '*.pyo',
        '.git',
        '.vscode',
        '.idea',
        
        # مكتبات غير مطلوبة
        'IPython',
        'jupyter',
        'notebook',
        'scipy',
        'sklearn',
        'tensorflow',
        'torch',
        'cv2',
        'selenium',
        'matplotlib',  # إزالة matplotlib لتقليل الحجم
        'numpy',       # إزالة numpy لتقليل الحجم
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# إزالة الملفات غير المرغوب فيها
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# إنشاء ملف تنفيذي واحد مستقل
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='نظام_إدارة_المخازن',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,  # تعطيل UPX لتسريع البناء
    runtime_tmpdir=None,
    console=False,  # إخفاء نافذة الكونسول
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/app_icon.ico' if os.path.exists('assets/app_icon.ico') else None,
    version_file=None,
)