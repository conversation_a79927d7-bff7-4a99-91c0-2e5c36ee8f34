#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار عملي لنظام منع تكرار النوافذ
Practical Test for Single Instance Window System
"""

import sys
import os
import traceback
import inspect

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_main_window_methods():
    """اختبار طرق النافذة الرئيسية"""
    print("🧪 اختبار طرق النافذة الرئيسية...")
    print("=" * 70)
    
    try:
        from ui.main_window import MainWindow
        
        # فحص وجود window_instances
        if hasattr(MainWindow, '__init__'):
            init_source = inspect.getsource(MainWindow.__init__)
            if 'self.window_instances = {}' in init_source:
                print("✅ window_instances - موجود في __init__")
            else:
                print("❌ window_instances - غير موجود في __init__")
        
        # فحص الطرق المحدثة
        methods_to_check = [
            'show_transactions',
            'manage_users', 
            'manage_departments',
            'manage_organizational_chart',
            'show_beneficiaries',
            'show_items',
            'show_reports'
        ]
        
        updated_methods = []
        old_methods = []
        
        for method_name in methods_to_check:
            if hasattr(MainWindow, method_name):
                method = getattr(MainWindow, method_name)
                try:
                    method_source = inspect.getsource(method)
                    if 'show_existing_window_or_create_new' in method_source:
                        updated_methods.append(method_name)
                        print(f"✅ {method_name} - يستخدم النظام الجديد")
                    else:
                        old_methods.append(method_name)
                        print(f"❌ {method_name} - يستخدم النظام القديم")
                except:
                    old_methods.append(method_name)
                    print(f"❌ {method_name} - لا يمكن فحص الكود")
            else:
                old_methods.append(method_name)
                print(f"❌ {method_name} - الطريقة غير موجودة")
        
        print(f"\n📊 النتيجة: {len(updated_methods)}/{len(methods_to_check)} طرق محدثة")
        
        if old_methods:
            print(f"⚠️ الطرق التي تحتاج تحديث: {old_methods}")
            return False, old_methods
        else:
            print("🎉 جميع الطرق محدثة!")
            return True, []
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        traceback.print_exc()
        return False, []

def show_method_details():
    """عرض تفاصيل الطرق"""
    print("\n" + "=" * 70)
    print("🔍 تفاصيل الطرق المحدثة...")
    print("=" * 70)
    
    try:
        from ui.main_window import MainWindow
        
        methods_to_show = ['show_transactions', 'manage_users', 'manage_departments']
        
        for method_name in methods_to_show:
            if hasattr(MainWindow, method_name):
                method = getattr(MainWindow, method_name)
                try:
                    method_source = inspect.getsource(method)
                    print(f"\n📝 {method_name}:")
                    
                    # البحث عن الكلمات المفتاحية
                    if 'show_existing_window_or_create_new' in method_source:
                        print("   ✅ يستخدم show_existing_window_or_create_new")
                    if 'register_window' in method_source:
                        print("   ✅ يستخدم register_window")
                    if 'def create_' in method_source:
                        print("   ✅ يحتوي على دالة create_")
                        
                except Exception as e:
                    print(f"   ❌ خطأ في فحص {method_name}: {e}")
                    
    except Exception as e:
        print(f"❌ خطأ في عرض التفاصيل: {e}")

def test_window_registration_system():
    """اختبار نظام تسجيل النوافذ"""
    print("\n" + "=" * 70)
    print("🔧 اختبار نظام تسجيل النوافذ...")
    print("=" * 70)
    
    try:
        from ui.main_window import MainWindow
        
        # فحص الطرق المطلوبة
        required_methods = [
            'register_window',
            'unregister_window',
            'show_existing_window_or_create_new'
        ]
        
        all_present = True
        
        for method_name in required_methods:
            if hasattr(MainWindow, method_name):
                print(f"✅ {method_name} - موجود")
            else:
                print(f"❌ {method_name} - مفقود")
                all_present = False
        
        if all_present:
            print("\n🎉 جميع طرق النظام موجودة!")
            
            # فحص تفاصيل show_existing_window_or_create_new
            method = getattr(MainWindow, 'show_existing_window_or_create_new')
            method_source = inspect.getsource(method)
            
            print("\n📋 تفاصيل show_existing_window_or_create_new:")
            if 'window_instances' in method_source:
                print("   ✅ يستخدم window_instances")
            if 'lift()' in method_source:
                print("   ✅ يرفع النافذة الموجودة")
            if 'focus_force()' in method_source:
                print("   ✅ يركز على النافذة الموجودة")
            if 'create_function()' in method_source:
                print("   ✅ ينشئ نافذة جديدة عند الحاجة")
                
            return True
        else:
            print("\n❌ بعض طرق النظام مفقودة!")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار النظام: {e}")
        return False

if __name__ == "__main__":
    try:
        print("🚀 بدء الاختبار العملي لنظام منع تكرار النوافذ")
        print("=" * 70)
        
        # اختبار الطرق
        methods_ok, old_methods = test_main_window_methods()
        
        # عرض تفاصيل الطرق
        show_method_details()
        
        # اختبار نظام التسجيل
        system_ok = test_window_registration_system()
        
        print("\n" + "=" * 70)
        print("📋 التقييم النهائي")
        print("=" * 70)
        
        if methods_ok and system_ok:
            print("🎉 النظام مطبق بشكل مثالي!")
            print("✅ جميع الشاشات ستظهر مرة واحدة فقط")
            print("✅ عند الضغط على زر شاشة مفتوحة، ستظهر في المقدمة")
            print("\n🔧 كيفية العمل:")
            print("   1. عند فتح شاشة لأول مرة، يتم إنشاؤها وتسجيلها")
            print("   2. عند محاولة فتحها مرة أخرى، يتم إظهار الشاشة الموجودة")
            print("   3. عند إغلاق الشاشة، يتم إلغاء تسجيلها تلقائياً")
            
        elif system_ok:
            print("🟡 النظام يعمل مع بعض النقص")
            print(f"⚠️ يجب تحديث {len(old_methods)} طرق:")
            for method in old_methods:
                print(f"   - {method}")
            print("\n💡 هذه الطرق ستفتح نوافذ متعددة حالياً")
            
        else:
            print("🔴 النظام يحتاج إلى إصلاح")
            print("❌ بعض المكونات الأساسية مفقودة")
            
        print("\n" + "=" * 70)
        print("🏁 انتهى الاختبار")
        
    except Exception as e:
        print(f"❌ خطأ عام في الاختبار: {e}")
        traceback.print_exc()
