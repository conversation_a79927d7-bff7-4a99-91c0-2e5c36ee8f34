2025-06-12 07:45:02 - BackupManager - INFO - تم بدء النسخ الاحتياطي التلقائي
2025-06-12 07:45:03 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 07:45:26 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 07:45:26 - StoresApp - ERROR - خطأ في عرض النافذة الرئيسية: 'MainWindow' object has no attribute 'status_var'
2025-06-12 07:46:22 - BackupManager - INFO - تم بدء النسخ الاحتياطي التلقائي
2025-06-12 07:46:22 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 07:46:36 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 07:46:36 - StoresApp - ERROR - خطأ في عرض النافذة الرئيسية: 'MainWindow' object has no attribute 'status_var'
2025-06-12 08:12:10 - BackupManager - INFO - تم بدء النسخ الاحتياطي التلقائي
2025-06-12 08:12:10 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 08:12:19 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 08:22:48 - BackupManager - INFO - تم بدء النسخ الاحتياطي التلقائي
2025-06-12 08:22:48 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 08:22:56 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 08:26:10 - BackupManager - INFO - تم بدء النسخ الاحتياطي التلقائي
2025-06-12 08:26:11 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 08:29:00 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 08:29:43 - BackupManager - INFO - تم بدء النسخ الاحتياطي التلقائي
2025-06-12 08:29:43 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 08:29:53 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 08:29:57 - BackupManager - INFO - تم إنشاء نسخة احتياطية: E:\desktop_stores_app\backups\backup_20250612_082957.zip
2025-06-12 08:29:58 - StoresApp - INFO - تم تسجيل خروج المستخدم: admin
2025-06-12 08:30:13 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 08:30:20 - BackupManager - INFO - تم إنشاء نسخة احتياطية: E:\desktop_stores_app\backups\backup_20250612_083020.zip
2025-06-12 08:30:20 - StoresApp - INFO - تم تسجيل خروج المستخدم: admin
2025-06-12 08:36:48 - BackupManager - INFO - تم بدء النسخ الاحتياطي التلقائي
2025-06-12 08:36:49 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 08:41:11 - BackupManager - INFO - تم بدء النسخ الاحتياطي التلقائي
2025-06-12 08:41:11 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 08:41:18 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 08:51:17 - BackupManager - INFO - تم بدء النسخ الاحتياطي التلقائي
2025-06-12 08:51:17 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 08:51:58 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 08:54:20 - BackupManager - INFO - تم بدء النسخ الاحتياطي التلقائي
2025-06-12 08:54:20 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 08:54:31 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 08:57:17 - BackupManager - INFO - تم بدء النسخ الاحتياطي التلقائي
2025-06-12 08:57:17 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 08:57:22 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 09:09:17 - BackupManager - INFO - تم بدء النسخ الاحتياطي التلقائي
2025-06-12 09:09:18 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 09:09:23 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 09:17:11 - BackupManager - INFO - تم بدء النسخ الاحتياطي التلقائي
2025-06-12 09:17:12 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 09:17:17 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 09:20:23 - BackupManager - INFO - تم بدء النسخ الاحتياطي التلقائي
2025-06-12 09:20:23 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 09:20:28 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 09:24:33 - BackupManager - INFO - تم بدء النسخ الاحتياطي التلقائي
2025-06-12 09:24:33 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 09:24:39 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 09:28:59 - BackupManager - INFO - تم إنشاء نسخة احتياطية: e:\desktop_stores_app\backups\backup_20250612_092859.zip
2025-06-12 09:29:03 - StoresApp - INFO - تم تسجيل خروج المستخدم: admin
2025-06-12 09:31:40 - BackupManager - INFO - تم بدء النسخ الاحتياطي التلقائي
2025-06-12 09:31:40 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 09:31:45 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 09:43:22 - BackupManager - INFO - تم بدء النسخ الاحتياطي التلقائي
2025-06-12 09:43:22 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 09:43:28 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 09:51:25 - BackupManager - INFO - تم إنشاء نسخة احتياطية: e:\desktop_stores_app\backups\backup_20250612_095125.zip
2025-06-12 09:51:27 - StoresApp - INFO - تم تسجيل خروج المستخدم: admin
2025-06-12 09:53:40 - BackupManager - INFO - تم بدء النسخ الاحتياطي التلقائي
2025-06-12 09:53:40 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 09:53:45 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 09:59:43 - BackupManager - INFO - تم إنشاء نسخة احتياطية: e:\desktop_stores_app\backups\backup_20250612_095943.zip
2025-06-12 09:59:44 - StoresApp - INFO - تم تسجيل خروج المستخدم: admin
2025-06-12 10:01:34 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 10:01:39 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 10:03:23 - StoresApp - INFO - تم تسجيل خروج المستخدم: admin
2025-06-12 10:09:05 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 10:09:13 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 10:10:51 - StoresApp - INFO - تم تسجيل خروج المستخدم: admin
2025-06-12 10:16:12 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 10:16:17 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 10:27:20 - StoresApp - INFO - تم تسجيل خروج المستخدم: admin
2025-06-12 10:27:40 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 10:27:45 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 10:30:25 - StoresApp - INFO - تم تسجيل خروج المستخدم: admin
2025-06-12 10:49:43 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 10:49:48 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 10:56:49 - StoresApp - INFO - تم تسجيل خروج المستخدم: admin
2025-06-12 11:17:43 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 11:17:48 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 11:20:11 - StoresApp - INFO - تم تسجيل خروج المستخدم: admin
2025-06-12 11:22:31 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 11:22:36 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 11:27:02 - StoresApp - INFO - تم تسجيل خروج المستخدم: admin
2025-06-12 11:27:15 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 11:27:20 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 11:31:24 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 11:31:28 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 11:35:46 - StoresApp - INFO - تم تسجيل خروج المستخدم: admin
2025-06-12 11:35:58 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 11:36:04 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 11:48:18 - StoresApp - INFO - تم تسجيل خروج المستخدم: admin
2025-06-12 11:48:21 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 11:48:28 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 11:56:24 - StoresApp - INFO - تم تسجيل خروج المستخدم: admin
2025-06-12 11:56:27 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 11:56:32 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 12:00:44 - StoresApp - INFO - تم تسجيل خروج المستخدم: admin
2025-06-12 12:04:52 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 12:04:58 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 12:08:17 - StoresApp - INFO - تم تسجيل خروج المستخدم: admin
2025-06-12 12:11:15 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 12:11:20 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 12:15:48 - StoresApp - INFO - تم تسجيل خروج المستخدم: admin
2025-06-12 12:21:20 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 12:21:25 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 12:22:31 - StoresApp - INFO - تم تسجيل خروج المستخدم: admin
2025-06-12 12:24:14 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 12:24:19 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 12:28:57 - StoresApp - INFO - تم تسجيل خروج المستخدم: admin
2025-06-12 12:30:02 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 12:30:07 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 12:38:58 - StoresApp - INFO - تم تسجيل خروج المستخدم: admin
2025-06-12 12:40:11 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 12:40:17 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 12:48:59 - StoresApp - INFO - تم تسجيل خروج المستخدم: admin
2025-06-12 12:49:03 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 12:49:08 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 12:55:29 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 12:55:34 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 12:56:22 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 12:56:28 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 12:59:27 - StoresApp - INFO - تم تسجيل خروج المستخدم: admin
2025-06-12 12:59:35 - StoresApp - INFO - تم تسجيل خروج المستخدم: admin
2025-06-12 12:59:38 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 12:59:43 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 13:13:54 - StoresApp - INFO - تم تسجيل خروج المستخدم: admin
2025-06-12 13:13:59 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 13:14:06 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 13:20:20 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 13:20:25 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 13:21:28 - StoresApp - INFO - تم تسجيل خروج المستخدم: admin
2025-06-12 13:21:43 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 13:21:47 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 13:23:13 - StoresApp - INFO - تم تسجيل خروج المستخدم: admin
2025-06-12 13:30:06 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 13:30:11 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 13:33:28 - StoresApp - INFO - تم تسجيل خروج المستخدم: admin
2025-06-12 13:45:36 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 13:45:42 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 13:50:07 - StoresApp - INFO - تم تسجيل خروج المستخدم: admin
2025-06-12 13:50:08 - StoresApp - INFO - تم تسجيل خروج المستخدم: admin
2025-06-12 13:50:13 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 13:50:18 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 13:50:59 - StoresApp - INFO - تم تسجيل خروج المستخدم: admin
2025-06-12 13:56:00 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 13:56:05 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 13:56:26 - StoresApp - INFO - تم تسجيل خروج المستخدم: admin
2025-06-12 14:01:52 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 14:03:59 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 18:52:14 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 18:55:52 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 18:56:07 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 19:04:46 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 19:05:12 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 19:09:47 - StoresApp - INFO - تم تسجيل خروج المستخدم: admin
2025-06-12 19:09:54 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 19:10:09 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 19:10:25 - StoresApp - INFO - تم تسجيل خروج المستخدم: admin
2025-06-12 19:13:29 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 19:13:46 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 19:16:01 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 19:16:19 - StoresApp - INFO - تم تسجيل خروج المستخدم: admin
2025-06-12 19:16:21 - StoresApp - INFO - تم تسجيل خروج المستخدم: admin
2025-06-12 19:16:31 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 19:23:20 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 19:23:41 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 19:32:09 - StoresApp - INFO - تم تسجيل خروج المستخدم: admin
2025-06-12 19:33:42 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 19:33:53 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 19:35:10 - StoresApp - INFO - تم تسجيل خروج المستخدم: admin
2025-06-12 19:35:28 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 19:35:37 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 19:39:42 - StoresApp - INFO - تم تسجيل خروج المستخدم: admin
2025-06-12 19:39:43 - StoresApp - INFO - تم تسجيل خروج المستخدم: admin
2025-06-12 19:41:23 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 19:41:34 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 19:42:52 - StoresApp - INFO - تم تسجيل خروج المستخدم: admin
2025-06-12 19:44:08 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 19:45:22 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-12 19:55:08 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 19:55:20 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-12 19:56:09 - StoresApp - INFO - تم تسجيل دخول المستخدم: 2
2025-06-12 19:56:25 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-12 20:06:55 - BackupManager - INFO - تم إنشاء نسخة احتياطية: e:\desktop_stores_app\backups\backup_20250612_200655.zip
2025-06-12 20:22:37 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 20:22:44 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-12 20:26:27 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 20:26:37 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-12 20:29:18 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-12 20:29:31 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-12 20:29:37 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 20:29:44 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-12 20:34:47 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-12 20:36:52 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 20:36:59 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-12 20:44:15 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-12 20:44:19 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 20:44:26 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-12 20:48:29 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-12 20:52:14 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 20:52:21 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-12 20:52:29 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-12 20:53:20 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 20:53:27 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-12 20:58:08 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-12 23:04:34 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 23:04:40 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-12 23:09:14 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-12 23:11:01 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 23:11:09 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-12 23:15:26 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-12 23:15:31 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 23:15:38 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-12 23:31:34 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-12 23:31:41 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 23:31:55 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-12 23:37:28 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-12 23:40:04 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 23:40:10 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-12 23:40:25 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-12 23:43:25 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 23:43:31 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-12 23:48:39 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-12 23:52:06 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 23:52:13 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-12 23:56:34 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-12 23:57:29 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 23:57:37 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-12 23:58:15 - BackupManager - INFO - تم إنشاء نسخة احتياطية: E:\desktop_stores_app\backups\backup_20250612_235815.zip
2025-06-12 23:58:17 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-12 23:58:50 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-12 23:58:57 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 00:01:00 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 00:01:06 - BackupManager - INFO - تم إنشاء نسخة احتياطية: E:\desktop_stores_app\backups\backup_20250613_000106.zip
2025-06-13 00:01:07 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 00:01:07 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 00:01:15 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 00:01:24 - BackupManager - INFO - تم إنشاء نسخة احتياطية: E:\desktop_stores_app\backups\backup_20250613_000124.zip
2025-06-13 00:01:25 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 00:02:27 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 00:02:50 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 00:02:57 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 00:03:12 - BackupManager - INFO - تم إنشاء نسخة احتياطية: E:\desktop_stores_app\backups\backup_20250613_000312.zip
2025-06-13 00:03:12 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 00:09:00 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 00:09:09 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 00:09:59 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 00:10:10 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 00:10:29 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 00:12:02 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 00:12:10 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 00:14:32 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 00:14:44 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 00:17:51 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 00:18:06 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 00:21:28 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 00:21:41 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 00:21:48 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 00:21:48 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 00:22:01 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 00:22:56 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 00:32:00 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 00:32:11 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 00:32:13 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 00:37:44 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 00:40:03 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 00:40:12 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 00:42:25 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 00:49:22 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 00:49:29 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 00:52:41 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 00:56:01 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 00:56:10 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 00:59:24 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 01:01:36 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 01:01:42 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 01:08:43 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 01:10:30 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 01:10:36 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 01:15:59 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 01:18:38 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 01:18:46 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 01:23:07 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 01:23:14 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 01:25:41 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 01:25:48 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 01:29:46 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 01:29:55 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 01:33:55 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 01:34:03 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 01:34:54 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 01:34:54 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 01:34:55 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 01:34:55 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 01:38:39 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 01:38:48 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 01:41:21 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 01:41:28 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 01:42:51 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 01:45:58 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 01:46:04 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 01:51:22 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 02:06:29 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 02:06:36 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 02:10:28 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 02:11:08 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 02:11:15 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 02:14:04 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 02:14:45 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 02:14:51 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 02:23:18 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 02:28:58 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 02:29:05 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 02:33:59 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 02:34:12 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 02:34:13 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 02:34:18 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 02:42:07 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 02:43:52 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 02:43:59 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 02:46:37 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 02:48:41 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 02:48:47 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 02:51:51 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 02:55:01 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 02:55:08 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 02:57:20 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 02:57:47 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 03:02:18 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 03:02:24 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 03:06:47 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 03:13:41 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 03:13:48 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 03:18:30 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 03:20:59 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 03:21:05 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 03:24:06 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 03:28:08 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 03:28:16 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 03:35:33 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 03:35:40 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 03:43:07 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 03:48:56 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 03:49:04 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 03:52:47 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 08:32:37 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 08:32:48 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 08:46:58 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 08:49:46 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 08:49:52 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 08:55:24 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 09:01:40 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 09:01:46 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 09:03:16 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 09:04:21 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 09:04:35 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 09:07:40 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 09:07:47 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 09:10:30 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 09:12:35 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 09:12:42 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 09:15:20 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 09:18:07 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 09:18:13 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 09:24:23 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 09:25:41 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 09:25:48 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 09:33:39 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 09:33:47 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 09:35:59 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 09:36:05 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 09:44:16 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 09:48:37 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 09:49:10 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 09:58:55 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 09:59:05 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 09:59:12 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 10:08:05 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 10:08:09 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 10:08:15 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 10:10:55 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 10:13:03 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 10:13:09 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 10:16:21 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 10:19:52 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 10:19:59 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 10:28:23 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 10:28:34 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 10:28:42 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 10:31:31 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 10:39:20 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 10:39:29 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 10:46:32 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 10:46:39 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 10:55:15 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 11:00:00 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 11:00:07 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 11:01:57 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 11:02:00 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 11:02:07 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 11:07:06 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 11:10:29 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 11:10:36 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 11:14:40 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 11:14:57 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 11:14:58 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 11:14:58 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 11:15:00 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 11:15:00 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 11:15:01 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 11:15:04 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 11:22:25 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 11:22:33 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 11:27:49 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 11:28:02 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 11:30:36 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 11:34:13 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 11:34:24 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 11:38:49 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 13:01:06 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 13:01:13 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 13:04:56 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 13:05:04 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 13:05:08 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 13:07:39 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 13:07:45 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 13:16:11 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 13:20:26 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 13:20:36 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 13:24:01 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 13:24:07 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 13:30:36 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 13:30:42 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 13:34:56 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 13:35:03 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 13:37:21 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 13:37:28 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 13:43:00 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 13:43:06 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 13:46:01 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 13:46:11 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 13:46:14 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 13:48:46 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 13:50:08 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 13:50:14 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 13:51:55 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 13:53:19 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 13:53:24 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 13:56:29 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 13:56:32 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 13:56:38 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 13:59:40 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 13:59:45 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 13:59:52 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 14:03:40 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 14:04:34 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 14:04:40 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 14:14:08 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 14:14:17 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 14:14:52 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 14:17:03 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 14:17:09 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 14:19:01 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 14:20:07 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 14:20:13 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 14:23:06 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 14:25:16 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 14:25:22 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 14:31:48 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 14:31:57 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 14:32:03 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 14:36:28 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 14:36:44 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 14:36:51 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 14:37:23 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 14:37:28 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 14:42:44 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 14:57:36 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 14:57:44 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 15:00:05 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 15:00:26 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 15:00:32 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 15:13:31 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 15:19:13 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 15:19:20 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 15:25:10 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 15:30:22 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 15:30:28 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 15:32:10 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 15:33:45 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 15:33:52 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 15:38:46 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 15:39:00 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 15:39:21 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 15:40:24 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 15:43:27 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 15:43:48 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 15:44:48 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 15:45:23 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 15:45:47 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 19:02:09 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 19:02:42 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 19:02:53 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-13 19:12:42 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-13 19:16:22 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-13 19:16:31 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 06:39:09 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 06:39:18 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 06:46:46 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 06:47:00 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 06:47:03 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 06:54:59 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 06:55:10 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 07:10:34 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 07:10:41 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 07:28:16 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 07:28:53 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 07:28:59 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 07:32:10 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 07:35:12 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 07:35:18 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 07:38:43 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 07:40:40 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 07:40:46 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 07:50:17 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 07:51:22 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 07:51:27 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 07:55:21 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 07:56:19 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 07:56:32 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 07:58:56 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 08:03:07 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 08:03:12 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 08:06:26 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 08:09:18 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 08:09:24 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 08:12:31 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 08:14:45 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 08:14:51 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 08:17:25 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 13:54:16 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 13:54:22 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 14:01:42 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 14:02:33 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 14:02:38 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 14:05:34 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 14:05:44 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 14:05:50 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 14:12:06 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 14:13:29 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 14:13:35 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 14:19:22 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 14:19:35 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 14:19:41 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 14:24:29 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 14:27:03 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 14:27:08 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 14:30:36 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 14:30:43 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 14:30:50 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 14:32:31 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 14:32:41 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 14:32:46 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 14:33:12 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 14:34:16 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 14:34:23 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 14:37:40 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 14:39:30 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 14:39:41 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 14:42:56 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 14:42:59 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 14:43:10 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 14:43:37 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 14:44:28 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 14:44:33 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 14:49:10 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 14:52:51 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 14:52:58 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 14:55:48 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 15:04:26 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 15:04:31 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 15:09:19 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 15:22:41 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 15:22:47 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 15:24:29 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 15:25:45 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 15:25:51 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 15:28:46 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 15:28:57 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 15:29:00 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 15:36:35 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 15:36:41 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 15:40:35 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 15:40:43 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 15:45:41 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 15:45:47 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 15:51:56 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 15:52:04 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 15:52:08 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 16:02:54 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 16:03:00 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 16:49:09 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 16:49:15 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 16:58:17 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 17:03:27 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 17:03:33 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 17:08:48 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 17:11:27 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 17:11:34 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 17:15:16 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 17:15:19 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 17:15:25 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 17:25:05 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 17:25:13 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 17:33:26 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 17:33:32 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 17:37:59 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 17:38:19 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 17:38:24 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 17:40:59 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 17:42:57 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 17:43:03 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 17:46:54 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 17:47:00 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 17:47:07 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 17:48:32 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 17:49:10 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 17:49:15 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 17:53:09 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 17:53:10 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 17:53:16 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 18:02:11 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 18:02:19 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 18:14:13 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 18:17:02 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 18:17:10 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 18:21:50 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 18:21:53 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 18:21:59 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 18:26:28 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 18:26:34 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 18:26:40 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 18:35:59 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 18:36:03 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 18:36:10 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 18:42:01 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 18:42:17 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 18:42:23 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 18:43:38 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 18:45:03 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 18:45:11 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 18:59:04 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 18:59:11 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 19:14:39 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 19:26:47 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 19:26:55 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 19:38:52 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 19:38:53 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 19:54:55 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 19:55:01 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 19:57:43 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 20:06:22 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 20:06:28 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 20:17:47 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 20:17:54 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 20:18:14 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 20:18:16 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 20:18:23 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 20:18:32 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 20:18:39 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 20:19:33 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 20:19:58 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 20:20:06 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 20:20:11 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 20:20:18 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 20:21:22 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 20:21:43 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 20:28:51 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 20:28:58 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 20:31:12 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 20:31:20 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 20:31:27 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 20:39:34 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 20:39:49 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 20:39:54 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 20:42:08 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 20:43:50 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 20:43:57 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 20:48:17 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 20:53:43 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 20:53:55 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 20:58:34 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 21:26:19 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 21:26:26 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 21:32:40 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 21:32:51 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 21:32:56 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 21:33:33 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 21:33:39 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 21:33:54 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 21:34:13 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 21:34:46 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 21:42:43 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 21:43:31 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 21:43:38 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 21:48:12 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 21:48:19 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 21:53:45 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 21:53:55 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 21:55:16 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 21:57:16 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 21:57:23 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 21:59:08 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 22:07:17 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 22:07:24 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 22:10:31 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 22:10:40 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 22:27:45 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 22:27:56 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 22:28:05 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 22:32:12 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 22:32:22 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 22:40:12 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 22:40:15 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 22:40:30 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 22:47:48 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 22:49:15 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 22:49:24 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 22:56:30 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 22:56:37 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 22:58:10 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 23:05:45 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 23:05:52 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 23:10:40 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 23:11:20 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 23:11:28 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 23:17:00 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 23:17:03 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 23:17:08 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 23:22:00 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 23:22:09 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 23:23:58 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 23:24:05 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 23:27:45 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 23:27:52 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 23:28:20 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 23:28:28 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 23:28:37 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 23:28:47 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 23:35:33 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 23:35:39 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 23:35:42 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 23:36:43 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 23:36:50 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 23:45:37 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 23:46:10 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 23:46:17 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 23:49:23 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 23:50:57 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 23:51:05 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 23:53:34 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 23:53:40 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-14 23:55:23 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-14 23:55:27 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-14 23:55:34 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-15 00:08:45 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-15 00:13:34 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-15 00:13:40 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-15 00:17:38 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-15 00:21:28 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-15 00:21:34 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-15 00:26:41 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-15 00:26:48 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-15 00:26:53 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-15 00:28:50 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-15 00:28:52 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-15 00:28:59 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-15 00:29:30 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-15 01:33:17 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-15 01:33:57 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-15 01:34:03 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-15 01:50:24 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-15 01:50:45 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-15 01:51:33 - StoresApp - INFO - تم تسجيل خروج المستخدم: admin
2025-06-15 15:23:57 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-15 15:24:15 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-15 15:24:41 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-15 15:25:36 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-15 15:25:45 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-15 15:30:33 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-15 15:32:14 - BackupManager - INFO - تم إنشاء نسخة احتياطية: E:\desktop_stores_app\backups\backup_20250615_153214.zip
2025-06-15 17:05:56 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-15 17:06:07 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-15 17:06:07 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-15 17:12:23 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-15 17:12:31 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-15 17:12:32 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-15 17:35:25 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-15 17:35:34 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-15 17:35:35 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-15 17:36:16 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-17 18:39:29 - StoresApp - ERROR - UI Error - خطأ اختبار: هذه رسالة خطأ يجب أن تكون صامتة
2025-06-17 18:41:50 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-17 18:41:59 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-17 18:42:00 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-17 18:54:47 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-17 18:54:59 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-17 18:58:45 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-17 18:58:59 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-17 18:59:06 - StoresApp - INFO - تم تسجيل خروج المستخدم: 1
2025-06-17 19:00:22 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-17 19:00:39 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-17 19:05:26 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-17 19:05:34 - StoresApp - INFO - تم تسجيل دخول المستخدم: 1
2025-06-17 19:06:08 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-17 19:07:52 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-17 19:08:44 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-17 19:10:20 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-17 19:13:54 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-17 19:18:07 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-17 19:18:07 - StoresApp - WARNING - تعذر عرض شاشة البداية: can't invoke "tk" command: application has been destroyed
2025-06-17 19:18:31 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-17 19:42:56 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-17 19:46:13 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-17 19:47:14 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-17 19:55:50 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-17 19:58:44 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-17 20:00:05 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-17 20:03:57 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-17 20:06:01 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-17 20:07:33 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-17 20:07:45 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-17 20:08:45 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-17 20:08:59 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-17 20:09:18 - StoresApp - ERROR - UI Error - خطأ: فشل في تبديل المستخدم: 'NoneType' object has no attribute 'logout_user'
2025-06-17 20:09:26 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-17 20:17:36 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-17 20:17:48 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-17 20:18:47 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-17 20:18:57 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-17 20:19:57 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-17 20:27:19 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-17 20:27:29 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-17 20:32:46 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe6.!treeview"
2025-06-17 20:34:20 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe14.!treeview"
2025-06-17 20:34:24 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe16.!treeview"
2025-06-17 20:34:53 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-17 20:35:03 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-17 20:40:40 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-17 20:40:48 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-17 20:45:44 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-17 20:45:53 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-17 20:46:35 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-17 20:46:45 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-17 20:48:47 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-17 20:49:07 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-17 20:55:00 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-17 20:55:10 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-17 21:01:14 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-17 21:01:21 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-17 21:05:55 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-17 21:06:04 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-17 21:06:22 - StoresApp - ERROR - UI Error - خطأ: فشل في تحديث بيانات لوحة التحكم: can't invoke "winfo" command: application has been destroyed
2025-06-17 21:06:27 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe6.!treeview"
2025-06-17 21:06:41 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe10.!treeview"
2025-06-17 22:11:27 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-17 22:11:35 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-17 22:11:47 - StoresApp - ERROR - UI Error - خطأ: فشل في تبديل المستخدم: 'NoneType' object has no attribute 'logout_user'
2025-06-17 22:11:54 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-17 22:12:01 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-17 22:19:52 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-17 22:21:57 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-17 22:26:07 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-17 22:27:10 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-17 22:30:16 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-17 22:31:51 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-17 22:36:07 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-17 22:41:35 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 04:58:01 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 05:02:47 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 05:09:05 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 05:16:36 - StoresApp - ERROR - حدث خطأ فادح في التطبيق: 'charmap' codec can't encode character '\U0001f9f9' in position 0: character maps to <undefined>
2025-06-18 05:17:35 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 05:25:00 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 05:25:15 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 05:25:19 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 05:25:27 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 05:28:11 - StoresApp - ERROR - حدث خطأ فادح في التطبيق: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>
2025-06-18 05:28:47 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 05:28:51 - StoresApp - ERROR - خطأ في عرض نافذة تسجيل الدخول: 'charmap' codec can't encode character '\U0001f511' in position 0: character maps to <undefined>
2025-06-18 05:28:51 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض نافذة تسجيل الدخول: 'charmap' codec can't encode character '\U0001f511' in position 0: character maps to <undefined>
2025-06-18 05:32:18 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 05:32:32 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 05:32:48 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: 'charmap' codec can't encode character '\u2705' in position 0: character maps to <undefined>
2025-06-18 05:32:48 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح لوحة تحكم المخزون: 'charmap' codec can't encode character '\u2705' in position 0: character maps to <undefined>
2025-06-18 05:32:50 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة التقارير: 'charmap' codec can't encode character '\U0001f4dd' in position 0: character maps to <undefined>
2025-06-18 05:32:54 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: 'charmap' codec can't encode character '\u2705' in position 0: character maps to <undefined>
2025-06-18 05:32:54 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح لوحة تحكم المخزون: 'charmap' codec can't encode character '\u2705' in position 0: character maps to <undefined>
2025-06-18 05:33:00 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: 'charmap' codec can't encode character '\u2705' in position 0: character maps to <undefined>
2025-06-18 05:33:00 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح لوحة تحكم المخزون: 'charmap' codec can't encode character '\u2705' in position 0: character maps to <undefined>
2025-06-18 05:33:12 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: 'charmap' codec can't encode character '\u2705' in position 0: character maps to <undefined>
2025-06-18 05:33:12 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح لوحة تحكم المخزون: 'charmap' codec can't encode character '\u2705' in position 0: character maps to <undefined>
2025-06-18 05:33:50 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: 'charmap' codec can't encode character '\u2705' in position 0: character maps to <undefined>
2025-06-18 05:33:50 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح لوحة تحكم المخزون: 'charmap' codec can't encode character '\u2705' in position 0: character maps to <undefined>
2025-06-18 05:35:51 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 05:40:16 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 05:40:35 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 05:40:39 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: 'charmap' codec can't encode character '\u2705' in position 0: character maps to <undefined>
2025-06-18 05:40:39 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح لوحة تحكم المخزون: 'charmap' codec can't encode character '\u2705' in position 0: character maps to <undefined>
2025-06-18 05:40:45 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: 'charmap' codec can't encode character '\u2705' in position 0: character maps to <undefined>
2025-06-18 05:40:45 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح لوحة تحكم المخزون: 'charmap' codec can't encode character '\u2705' in position 0: character maps to <undefined>
2025-06-18 05:40:51 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: 'charmap' codec can't encode character '\u2705' in position 0: character maps to <undefined>
2025-06-18 05:40:51 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح لوحة تحكم المخزون: 'charmap' codec can't encode character '\u2705' in position 0: character maps to <undefined>
2025-06-18 05:41:02 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة التقارير: 'charmap' codec can't encode character '\U0001f4dd' in position 0: character maps to <undefined>
2025-06-18 05:41:18 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة التقارير: 'charmap' codec can't encode character '\U0001f4dd' in position 0: character maps to <undefined>
2025-06-18 05:41:23 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: 'charmap' codec can't encode character '\u2705' in position 0: character maps to <undefined>
2025-06-18 05:41:23 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح لوحة تحكم المخزون: 'charmap' codec can't encode character '\u2705' in position 0: character maps to <undefined>
2025-06-18 05:41:31 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: 'charmap' codec can't encode character '\u2705' in position 0: character maps to <undefined>
2025-06-18 05:41:31 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح لوحة تحكم المخزون: 'charmap' codec can't encode character '\u2705' in position 0: character maps to <undefined>
2025-06-18 05:41:41 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: 'charmap' codec can't encode character '\u2705' in position 0: character maps to <undefined>
2025-06-18 05:41:41 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح لوحة تحكم المخزون: 'charmap' codec can't encode character '\u2705' in position 0: character maps to <undefined>
2025-06-18 05:42:11 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المخزون: 'charmap' codec can't encode character '\u2705' in position 0: character maps to <undefined>
2025-06-18 05:42:39 - StoresApp - ERROR - UI Error - خطأ: رقم الصنف '1' موجود مسبقاً
2025-06-18 05:42:42 - StoresApp - ERROR - UI Error - خطأ: رقم الصنف '1' موجود مسبقاً
2025-06-18 05:42:42 - StoresApp - ERROR - UI Error - خطأ: رقم الصنف '1' موجود مسبقاً
2025-06-18 05:42:43 - StoresApp - ERROR - UI Error - خطأ: رقم الصنف '1' موجود مسبقاً
2025-06-18 05:42:43 - StoresApp - ERROR - UI Error - خطأ: رقم الصنف '1' موجود مسبقاً
2025-06-18 05:42:44 - StoresApp - ERROR - UI Error - خطأ: رقم الصنف '1' موجود مسبقاً
2025-06-18 05:43:00 - StoresApp - ERROR - UI Error - خطأ: رقم الصنف '1' موجود مسبقاً
2025-06-18 05:47:07 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 05:47:19 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 05:47:39 - StoresApp - ERROR - UI Error - خطأ: رقم الصنف '101001' موجود مسبقاً
2025-06-18 05:47:40 - StoresApp - ERROR - UI Error - خطأ: رقم الصنف '101001' موجود مسبقاً
2025-06-18 05:47:40 - StoresApp - ERROR - UI Error - خطأ: رقم الصنف '101001' موجود مسبقاً
2025-06-18 05:48:40 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المخزون: 'charmap' codec can't encode character '\u2705' in position 0: character maps to <undefined>
2025-06-18 05:50:03 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>
2025-06-18 05:56:26 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 05:56:33 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 06:00:15 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe12.!treeview"
2025-06-18 06:00:16 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe12.!treeview"
2025-06-18 06:01:21 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe12.!treeview"
2025-06-18 06:01:23 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe12.!treeview"
2025-06-18 06:04:44 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe12.!treeview"
2025-06-18 12:54:21 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 12:54:31 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 13:02:41 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 13:02:49 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 13:10:47 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 13:10:55 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 13:16:24 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 13:16:31 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 13:17:14 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 13:17:24 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 13:18:45 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 13:18:53 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 13:21:01 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe8.!treeview"
2025-06-18 13:21:01 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe10.!treeview"
2025-06-18 13:21:02 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe8.!treeview"
2025-06-18 13:21:02 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe10.!treeview"
2025-06-18 13:26:09 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 13:26:15 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 13:29:19 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 13:29:25 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 13:30:45 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe8.!treeview"
2025-06-18 13:30:46 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe8.!treeview"
2025-06-18 13:31:17 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe8.!treeview"
2025-06-18 13:33:28 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 13:33:34 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 13:39:14 - StoresApp - ERROR - UI Error - خطأ: لم يتم العثور على العملية
2025-06-18 13:39:17 - StoresApp - ERROR - UI Error - خطأ: لم يتم العثور على العملية
2025-06-18 13:42:56 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe12.!treeview"
2025-06-18 13:44:40 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 13:44:46 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 13:47:45 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 13:47:52 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 14:02:54 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 14:03:02 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 14:13:00 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 14:13:06 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 14:15:12 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 14:15:18 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 14:38:28 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 14:38:33 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 14:43:20 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 14:43:26 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 14:53:37 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 14:53:45 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 16:35:06 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 16:35:12 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 17:08:17 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 17:08:24 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 18:10:28 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 18:10:34 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 18:15:40 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 18:15:47 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 18:16:21 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات الأصناف الأكثر استخداماً: no such column: ti.item_number
2025-06-18 18:16:33 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات الأصناف الأكثر استخداماً: no such column: ti.item_number
2025-06-18 18:16:34 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة التقرير: no such column: ti.item_number
2025-06-18 18:16:36 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة التقرير: no such column: ti.item_number
2025-06-18 18:16:49 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء حذف البيانات: 'MainWindow' object has no attribute 'root'
2025-06-18 18:16:51 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء حذف البيانات: 'MainWindow' object has no attribute 'root'
2025-06-18 18:16:51 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء حذف البيانات: 'MainWindow' object has no attribute 'root'
2025-06-18 18:16:52 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء حذف البيانات: 'MainWindow' object has no attribute 'root'
2025-06-18 18:16:52 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء حذف البيانات: 'MainWindow' object has no attribute 'root'
2025-06-18 18:16:55 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء حذف البيانات: 'MainWindow' object has no attribute 'root'
2025-06-18 18:16:56 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء حذف البيانات: 'MainWindow' object has no attribute 'root'
2025-06-18 18:17:02 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء حذف البيانات: 'MainWindow' object has no attribute 'root'
2025-06-18 18:45:48 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 18:45:53 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 18:58:41 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 19:02:13 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 19:02:58 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 19:03:10 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 19:03:13 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 19:03:15 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 19:03:15 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 19:03:15 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 19:03:36 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 19:03:38 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 19:03:38 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 19:03:38 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 19:03:38 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 19:03:38 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 19:03:39 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 19:03:39 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 19:03:39 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 19:03:39 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 19:03:39 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 19:03:39 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 19:03:40 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 19:03:40 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 19:03:40 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 19:03:40 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 19:03:40 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 19:03:45 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 19:03:45 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 19:03:45 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 19:03:45 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 19:03:45 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 19:03:45 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 19:03:46 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 19:03:55 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 19:03:55 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 19:03:55 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 19:03:56 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 19:03:56 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 19:03:56 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 19:07:48 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 19:07:54 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 19:08:04 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 19:08:18 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 19:12:37 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 19:12:42 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 19:22:32 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 19:23:03 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 20:29:34 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 20:29:40 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 20:35:43 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 20:35:52 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 20:37:57 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 20:38:03 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 20:38:38 - StoresApp - ERROR - UI Error - خطأ: الصنف المحدد غير موجود
2025-06-18 20:38:39 - StoresApp - ERROR - UI Error - خطأ: الصنف المحدد غير موجود
2025-06-18 20:40:58 - StoresApp - ERROR - UI Error - خطأ: الصنف المحدد غير موجود
2025-06-18 20:41:00 - StoresApp - ERROR - UI Error - خطأ: الصنف المحدد غير موجود
2025-06-18 20:41:00 - StoresApp - ERROR - UI Error - خطأ: الصنف المحدد غير موجود
2025-06-18 20:41:00 - StoresApp - ERROR - UI Error - خطأ: الصنف المحدد غير موجود
2025-06-18 20:44:12 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 20:44:17 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 20:54:35 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 20:54:41 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 21:03:26 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 21:03:32 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 21:33:34 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 21:33:40 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 21:39:07 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 21:39:13 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 21:42:31 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 21:42:38 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 21:45:54 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 21:46:02 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 21:47:41 - StoresApp - ERROR - UI Error - خطأ: فشل في تبديل المستخدم: 'NoneType' object has no attribute 'logout_user'
2025-06-18 21:47:48 - StoresApp - ERROR - UI Error - خطأ: فشل في تبديل المستخدم: 'NoneType' object has no attribute 'logout_user'
2025-06-18 21:48:05 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 21:48:11 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 21:48:13 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 21:48:14 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 21:48:14 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 21:48:23 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 21:48:24 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 21:48:24 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 21:52:17 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 21:52:27 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 21:52:28 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 21:52:28 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 21:52:28 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 21:52:28 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 21:52:29 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 21:52:29 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 21:52:29 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 21:52:29 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 21:52:30 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 21:52:30 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 21:52:30 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 21:52:30 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 21:52:30 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 21:52:36 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 21:52:39 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 21:52:39 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 21:52:41 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم المستخدم وكلمة المرور
2025-06-18 21:56:02 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 21:56:12 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 21:58:10 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 21:58:17 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 21:58:56 - StoresApp - ERROR - UI Error - خطأ: فشل في إضافة المستخدم
2025-06-18 21:58:57 - StoresApp - ERROR - UI Error - خطأ: فشل في إضافة المستخدم
2025-06-18 21:58:58 - StoresApp - ERROR - UI Error - خطأ: فشل في إضافة المستخدم
2025-06-18 21:58:58 - StoresApp - ERROR - UI Error - خطأ: فشل في إضافة المستخدم
2025-06-18 21:59:33 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 21:59:42 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 22:00:26 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 22:00:33 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 22:05:36 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 22:05:46 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 22:14:29 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 22:14:40 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 22:14:46 - StoresApp - ERROR - UI Error - خطأ في البيانات: يرجى إدخال الاسم
يرجى إدخال الرقم العام
يرجى اختيار الوحدة
يرجى اختيار الإدارة
2025-06-18 22:15:13 - StoresApp - ERROR - UI Error - خطأ في البيانات: يرجى اختيار الإدارة
2025-06-18 22:15:14 - StoresApp - ERROR - UI Error - خطأ في البيانات: يرجى اختيار الإدارة
2025-06-18 22:15:14 - StoresApp - ERROR - UI Error - خطأ في البيانات: يرجى اختيار الإدارة
2025-06-18 22:19:35 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 22:19:44 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 22:20:40 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 22:20:49 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 22:40:28 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 22:40:37 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 22:40:50 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة إدارة المستخدمين: unindent does not match any outer indentation level (users_management_window.py, line 380)
2025-06-18 22:40:53 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة إدارة المستخدمين: unindent does not match any outer indentation level (users_management_window.py, line 380)
2025-06-18 22:40:54 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة إدارة المستخدمين: unindent does not match any outer indentation level (users_management_window.py, line 380)
2025-06-18 22:41:40 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe8.!treeview"
2025-06-18 22:41:41 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe8.!treeview"
2025-06-18 22:42:00 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة إدارة المستخدمين: unindent does not match any outer indentation level (users_management_window.py, line 380)
2025-06-18 22:42:40 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 22:42:46 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-18 22:42:56 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة إدارة المستخدمين: unindent does not match any outer indentation level (users_management_window.py, line 380)
2025-06-18 22:42:58 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة إدارة المستخدمين: unindent does not match any outer indentation level (users_management_window.py, line 380)
2025-06-18 22:43:14 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة إدارة المستخدمين: unindent does not match any outer indentation level (users_management_window.py, line 380)
2025-06-18 22:52:40 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-18 22:52:49 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-20 10:40:26 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-20 10:40:38 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-20 10:50:13 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-20 10:50:19 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-20 10:56:38 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-20 10:56:46 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-20 11:00:28 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-20 11:00:37 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-20 11:04:38 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-20 11:04:45 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-20 11:06:15 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-20 11:06:54 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-20 11:07:01 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-20 11:13:24 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-20 11:13:31 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-20 11:16:38 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-20 11:16:47 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-20 11:23:50 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-20 11:23:56 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-20 11:24:01 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة عملية صرف جديدة: keyword argument repeated: width (new_transaction_window.py, line 1428)
2025-06-20 11:24:04 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة عملية صرف جديدة: keyword argument repeated: width (new_transaction_window.py, line 1428)
2025-06-20 11:26:32 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-20 11:26:38 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-20 11:26:48 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة عملية صرف جديدة: keyword argument repeated: width (new_transaction_window.py, line 1428)
2025-06-20 11:26:49 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة عملية صرف جديدة: keyword argument repeated: width (new_transaction_window.py, line 1428)
2025-06-20 11:26:49 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة عملية صرف جديدة: keyword argument repeated: width (new_transaction_window.py, line 1428)
2025-06-20 11:26:49 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة عملية صرف جديدة: keyword argument repeated: width (new_transaction_window.py, line 1428)
2025-06-20 11:26:54 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة عملية صرف جديدة: keyword argument repeated: width (new_transaction_window.py, line 1428)
2025-06-20 11:26:55 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة عملية صرف جديدة: keyword argument repeated: width (new_transaction_window.py, line 1428)
2025-06-20 11:26:55 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة عملية صرف جديدة: keyword argument repeated: width (new_transaction_window.py, line 1428)
2025-06-20 11:26:59 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة عملية صرف جديدة: keyword argument repeated: width (new_transaction_window.py, line 1428)
2025-06-20 11:27:50 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة عملية صرف جديدة: keyword argument repeated: width (new_transaction_window.py, line 1428)
2025-06-20 11:27:50 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة عملية صرف جديدة: keyword argument repeated: width (new_transaction_window.py, line 1428)
2025-06-20 11:28:48 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة التقارير: keyword argument repeated: width (reports_window.py, line 269)
2025-06-20 11:28:53 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة التقارير: keyword argument repeated: width (reports_window.py, line 269)
2025-06-20 11:28:54 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة التقارير: keyword argument repeated: width (reports_window.py, line 269)
2025-06-20 11:28:55 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة التقارير: keyword argument repeated: width (reports_window.py, line 269)
2025-06-20 11:28:55 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة التقارير: keyword argument repeated: width (reports_window.py, line 269)
2025-06-20 11:28:56 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة التقارير: keyword argument repeated: width (reports_window.py, line 269)
2025-06-20 11:28:57 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة التقارير: keyword argument repeated: width (reports_window.py, line 269)
2025-06-20 11:29:23 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة التقارير: keyword argument repeated: width (reports_window.py, line 269)
2025-06-20 11:30:34 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe10.!treeview"
2025-06-20 11:30:34 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe14.!treeview"
2025-06-20 11:30:34 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe20.!treeview"
2025-06-20 11:30:35 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe10.!treeview"
2025-06-20 11:30:35 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe14.!treeview"
2025-06-20 11:30:35 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe20.!treeview"
2025-06-20 11:31:32 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe10.!treeview"
2025-06-20 11:31:32 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe14.!treeview"
2025-06-20 11:31:32 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe20.!treeview"
2025-06-20 11:31:33 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe10.!treeview"
2025-06-20 11:31:33 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe14.!treeview"
2025-06-20 11:31:33 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe20.!treeview"
2025-06-20 11:32:04 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe10.!treeview"
2025-06-20 11:32:15 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe10.!treeview"
2025-06-20 11:32:15 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe14.!treeview"
2025-06-20 11:32:15 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe20.!treeview"
2025-06-20 11:32:15 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe24.!treeview"
2025-06-20 11:32:16 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe10.!treeview"
2025-06-20 11:32:16 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe14.!treeview"
2025-06-20 11:32:16 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe20.!treeview"
2025-06-20 11:32:16 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe24.!treeview"
2025-06-20 11:33:44 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe14.!treeview"
2025-06-20 11:40:26 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-20 11:40:32 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-20 11:40:35 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة التقارير: keyword argument repeated: width (reports_window.py, line 269)
2025-06-20 11:41:20 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة التقارير: keyword argument repeated: width (reports_window.py, line 269)
2025-06-20 11:49:35 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-20 11:49:42 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-20 11:49:58 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض تفاصيل العملية: keyword argument repeated: width (transaction_details_window.py, line 850)
2025-06-20 11:50:01 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض تفاصيل العملية: keyword argument repeated: width (transaction_details_window.py, line 850)
2025-06-20 11:50:06 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض تفاصيل العملية: keyword argument repeated: width (transaction_details_window.py, line 850)
2025-06-20 14:00:57 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-20 14:01:03 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-20 14:01:33 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض تفاصيل العملية: keyword argument repeated: width (transaction_details_window.py, line 850)
2025-06-20 14:03:00 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض تفاصيل العملية: keyword argument repeated: width (transaction_details_window.py, line 850)
2025-06-20 14:04:12 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-20 14:04:18 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-20 14:09:50 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-20 14:09:56 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-20 14:11:29 - StoresApp - ERROR - UI Error - خطأ: رقم الصنف 101001 موجود بالفعل
2025-06-20 14:11:30 - StoresApp - ERROR - UI Error - خطأ: رقم الصنف 101001 موجود بالفعل
2025-06-20 14:11:30 - StoresApp - ERROR - UI Error - خطأ: رقم الصنف 101001 موجود بالفعل
2025-06-20 14:11:30 - StoresApp - ERROR - UI Error - خطأ: رقم الصنف 101001 موجود بالفعل
2025-06-21 08:35:33 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-21 08:35:39 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-21 08:45:09 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-21 08:45:16 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-21 08:57:03 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-21 08:57:09 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-21 09:06:40 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-21 09:06:46 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-21 09:07:02 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:07:03 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:07:04 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:07:04 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:07:04 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:07:05 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:07:05 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:07:11 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:07:12 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:08:08 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:08:08 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:18:47 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-21 09:18:54 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-21 09:19:21 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:19:22 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:19:23 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:19:23 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:19:23 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:19:23 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:19:24 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:19:24 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:19:24 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:19:24 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:19:25 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:21:15 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-21 09:21:22 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-21 09:29:05 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-21 09:29:14 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-21 09:29:31 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:29:33 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:29:33 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:29:33 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:29:34 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:29:34 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:29:34 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:29:34 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:29:34 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:29:34 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:29:35 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:29:35 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:29:35 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:29:35 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:29:35 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:29:36 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:29:36 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:29:36 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:29:36 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:29:36 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:29:37 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:29:37 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:29:37 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:29:40 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:29:40 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:29:40 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:29:40 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:29:41 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:29:42 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:29:42 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:30:29 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:30:29 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:30:29 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:30:29 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:30:29 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:30:30 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:30:30 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:30:30 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:30:30 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:33:07 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-21 09:33:13 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-21 09:42:10 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-21 09:42:17 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-21 09:42:52 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:42:54 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:42:55 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:42:55 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:42:57 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:42:57 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:42:58 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:42:58 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:42:58 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:42:58 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:42:59 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:42:59 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:43:03 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:43:03 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:43:04 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:43:04 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:43:04 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:43:04 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:43:05 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:43:05 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:43:05 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:43:05 - StoresApp - ERROR - UI Error - خطأ: فشل في طباعة المعاملة: NewTransactionWindow.show_enhanced_print_preview() missing 8 required positional arguments: 'transaction', 'beneficiary', 'receiver', 'rank', 'department', 'receiver_rank', 'receiver_department', and 'notes'
2025-06-21 09:48:45 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-21 09:48:51 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-21 10:05:33 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-21 10:05:39 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-21 10:16:12 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-21 10:16:18 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-21 10:26:13 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-21 10:26:19 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-21 10:34:53 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-21 10:35:00 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-21 10:53:15 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-21 10:53:21 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-21 11:02:46 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-21 11:02:55 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-21 11:16:39 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-21 11:16:47 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-21 11:26:21 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-21 11:26:27 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-21 11:27:13 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 11:28:44 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 11:29:10 - StoresApp - ERROR - UI Error - خطأ: يرجى إدخال اسم التصنيف
2025-06-21 11:29:11 - StoresApp - ERROR - UI Error - خطأ: يرجى اختيار تصنيف للتحديث
2025-06-21 11:29:11 - StoresApp - ERROR - UI Error - خطأ: يرجى اختيار تصنيف للحذف
2025-06-21 11:29:45 - StoresApp - ERROR - UI Error - خطأ: يرجى اختيار تصنيف للتحديث
2025-06-21 11:29:46 - StoresApp - ERROR - UI Error - خطأ: يرجى اختيار تصنيف للتحديث
2025-06-21 11:30:09 - StoresApp - ERROR - UI Error - خطأ: فشل في تحديث التصنيف: no such column: updated_at
2025-06-21 11:30:11 - StoresApp - ERROR - UI Error - خطأ: فشل في تحديث التصنيف: no such column: updated_at
2025-06-21 11:30:12 - StoresApp - ERROR - UI Error - خطأ: فشل في تحديث التصنيف: no such column: updated_at
2025-06-21 11:30:12 - StoresApp - ERROR - UI Error - خطأ: فشل في تحديث التصنيف: no such column: updated_at
2025-06-21 11:30:12 - StoresApp - ERROR - UI Error - خطأ: فشل في تحديث التصنيف: no such column: updated_at
2025-06-21 11:30:18 - StoresApp - ERROR - UI Error - خطأ: فشل في تحديث التصنيف: no such column: updated_at
2025-06-21 11:30:19 - StoresApp - ERROR - UI Error - خطأ: فشل في تحديث التصنيف: no such column: updated_at
2025-06-21 11:30:19 - StoresApp - ERROR - UI Error - خطأ: فشل في تحديث التصنيف: no such column: updated_at
2025-06-21 11:30:19 - StoresApp - ERROR - UI Error - خطأ: فشل في تحديث التصنيف: no such column: updated_at
2025-06-21 11:30:19 - StoresApp - ERROR - UI Error - خطأ: فشل في تحديث التصنيف: no such column: updated_at
2025-06-21 11:30:53 - StoresApp - ERROR - UI Error - خطأ: فشل في تحديث التصنيف: no such column: updated_at
2025-06-21 11:30:53 - StoresApp - ERROR - UI Error - خطأ: فشل في تحديث التصنيف: no such column: updated_at
2025-06-21 11:30:53 - StoresApp - ERROR - UI Error - خطأ: فشل في تحديث التصنيف: no such column: updated_at
2025-06-21 11:30:54 - StoresApp - ERROR - UI Error - خطأ: فشل في تحديث التصنيف: no such column: updated_at
2025-06-21 11:30:54 - StoresApp - ERROR - UI Error - خطأ: فشل في تحديث التصنيف: no such column: updated_at
2025-06-21 11:30:54 - StoresApp - ERROR - UI Error - خطأ: فشل في إضافة التصنيف: UNIQUE constraint failed: categories.name
2025-06-21 11:30:54 - StoresApp - ERROR - UI Error - خطأ: فشل في تحديث التصنيف: no such column: updated_at
2025-06-21 11:30:55 - StoresApp - ERROR - UI Error - خطأ: فشل في تحديث التصنيف: no such column: updated_at
2025-06-21 11:31:02 - StoresApp - ERROR - UI Error - خطأ: فشل في تحديث التصنيف: no such column: updated_at
2025-06-21 11:31:02 - StoresApp - ERROR - UI Error - خطأ: فشل في تحديث التصنيف: no such column: updated_at
2025-06-21 11:31:03 - StoresApp - ERROR - UI Error - خطأ: فشل في تحديث التصنيف: no such column: updated_at
2025-06-21 11:31:03 - StoresApp - ERROR - UI Error - خطأ: فشل في تحديث التصنيف: no such column: updated_at
2025-06-21 11:40:48 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-21 11:40:54 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-21 11:41:46 - StoresApp - ERROR - UI Error - خطأ: فشل في تحديث التصنيف: no such column: updated_at
2025-06-21 11:41:47 - StoresApp - ERROR - UI Error - خطأ: فشل في تحديث التصنيف: no such column: updated_at
2025-06-21 11:41:48 - StoresApp - ERROR - UI Error - خطأ: فشل في تحديث التصنيف: no such column: updated_at
2025-06-21 11:41:48 - StoresApp - ERROR - UI Error - خطأ: فشل في تحديث التصنيف: no such column: updated_at
2025-06-21 11:41:48 - StoresApp - ERROR - UI Error - خطأ: فشل في تحديث التصنيف: no such column: updated_at
2025-06-21 11:41:48 - StoresApp - ERROR - UI Error - خطأ: فشل في تحديث التصنيف: no such column: updated_at
2025-06-21 11:42:32 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 11:42:37 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 11:42:40 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 11:42:43 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 11:42:44 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 11:42:53 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 11:42:58 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 11:42:59 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 11:43:04 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 11:43:07 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 11:43:11 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 11:43:12 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 11:43:12 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 11:43:12 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 11:43:15 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 11:43:16 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 11:43:17 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 11:43:17 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 11:43:17 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 11:43:17 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 11:43:18 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 11:52:27 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 11:52:44 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 11:52:45 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 11:52:46 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 11:52:53 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 11:52:54 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 11:52:55 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 11:52:58 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 11:52:58 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 11:52:58 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 11:52:58 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 11:52:59 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 11:52:59 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 11:53:02 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 11:53:03 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 11:53:03 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 11:53:04 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 12:02:47 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-21 12:02:57 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-21 12:03:03 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 12:03:07 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 12:03:09 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 12:03:09 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 12:05:04 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 12:07:12 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 12:12:43 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-21 12:12:49 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-21 12:12:53 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة إدارة الوحدات: UnitsWindow.__init__() takes 2 positional arguments but 3 were given
2025-06-21 12:13:10 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 12:13:13 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-21 12:13:23 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة إدارة الوحدات: UnitsWindow.__init__() takes 2 positional arguments but 3 were given
2025-06-21 12:22:07 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-21 12:22:14 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-21 12:40:25 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-21 12:40:32 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-21 12:40:44 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل البيانات: name 'Unit' is not defined
2025-06-21 12:42:08 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-21 12:42:17 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-21 12:53:34 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-21 12:53:41 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-21 12:53:57 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل البيانات: name 'Unit' is not defined
2025-06-21 12:54:27 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل البيانات: name 'Unit' is not defined
2025-06-21 13:08:10 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-21 13:08:18 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-21 13:18:41 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-21 13:18:47 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-21 13:19:07 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل البيانات: 'Department' object has no attribute 'unit_id'
2025-06-21 13:19:16 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل البيانات: 'Department' object has no attribute 'unit_id'
2025-06-21 13:30:33 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-21 13:30:39 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-21 13:36:33 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-21 13:36:40 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-21 13:42:20 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-21 13:42:26 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-22 05:05:13 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-22 05:05:23 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-22 05:14:39 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-22 05:14:45 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-22 05:17:37 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-22 05:17:40 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-22 05:17:43 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-22 05:19:16 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-22 05:31:38 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-22 05:31:43 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-22 05:32:13 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-22 05:58:07 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-22 05:58:13 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-22 17:38:34 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-22 17:38:39 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-22 17:46:23 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-22 17:46:29 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-22 17:54:09 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-22 17:54:16 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-22 18:00:30 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-22 18:00:36 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-22 18:13:05 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-22 18:13:11 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-22 18:15:49 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-22 18:23:30 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-22 18:23:36 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-22 18:25:48 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-22 18:27:11 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-22 18:27:12 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-22 18:27:16 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-22 18:27:19 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-22 18:28:44 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe6.!treeview"
2025-06-22 18:29:21 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-22 18:29:28 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-22 18:37:57 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-22 18:38:03 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-22 18:38:30 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-22 18:38:33 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-22 18:38:36 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-22 18:38:39 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-22 18:38:42 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-22 18:38:44 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-22 18:38:45 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-22 18:38:53 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-22 18:38:55 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-22 18:49:11 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-22 18:49:17 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-22 18:50:48 - StoresApp - ERROR - UI Error - خطأ في البيانات: يجب أن يكون الرقم العام أكثر من 3 أرقام
2025-06-22 18:50:50 - StoresApp - ERROR - UI Error - خطأ في البيانات: يجب أن يكون الرقم العام أكثر من 3 أرقام
2025-06-22 18:50:50 - StoresApp - ERROR - UI Error - خطأ في البيانات: يجب أن يكون الرقم العام أكثر من 3 أرقام
2025-06-22 18:50:51 - StoresApp - ERROR - UI Error - خطأ في البيانات: يجب أن يكون الرقم العام أكثر من 3 أرقام
2025-06-22 18:50:51 - StoresApp - ERROR - UI Error - خطأ في البيانات: يجب أن يكون الرقم العام أكثر من 3 أرقام
2025-06-22 18:50:54 - StoresApp - ERROR - UI Error - خطأ في البيانات: يجب أن يكون الرقم العام أكثر من 3 أرقام
2025-06-22 18:50:54 - StoresApp - ERROR - UI Error - خطأ في البيانات: يجب أن يكون الرقم العام أكثر من 3 أرقام
2025-06-22 18:50:57 - StoresApp - ERROR - UI Error - خطأ في البيانات: يجب أن يكون الرقم العام أكثر من 3 أرقام
2025-06-22 18:51:02 - StoresApp - ERROR - UI Error - خطأ في البيانات: يجب أن يكون الرقم العام أكثر من 3 أرقام
2025-06-22 18:51:03 - StoresApp - ERROR - UI Error - خطأ في البيانات: يجب أن يكون الرقم العام أكثر من 3 أرقام
2025-06-22 18:51:03 - StoresApp - ERROR - UI Error - خطأ في البيانات: يجب أن يكون الرقم العام أكثر من 3 أرقام
2025-06-22 18:51:03 - StoresApp - ERROR - UI Error - خطأ في البيانات: يجب أن يكون الرقم العام أكثر من 3 أرقام
2025-06-22 18:51:03 - StoresApp - ERROR - UI Error - خطأ في البيانات: يجب أن يكون الرقم العام أكثر من 3 أرقام
2025-06-22 18:51:04 - StoresApp - ERROR - UI Error - خطأ في البيانات: يجب أن يكون الرقم العام أكثر من 3 أرقام
2025-06-22 18:51:04 - StoresApp - ERROR - UI Error - خطأ في البيانات: يجب أن يكون الرقم العام أكثر من 3 أرقام
2025-06-22 18:51:04 - StoresApp - ERROR - UI Error - خطأ في البيانات: يجب أن يكون الرقم العام أكثر من 3 أرقام
2025-06-22 18:51:04 - StoresApp - ERROR - UI Error - خطأ في البيانات: يجب أن يكون الرقم العام أكثر من 3 أرقام
2025-06-22 18:51:04 - StoresApp - ERROR - UI Error - خطأ في البيانات: يجب أن يكون الرقم العام أكثر من 3 أرقام
2025-06-22 18:51:05 - StoresApp - ERROR - UI Error - خطأ في البيانات: يجب أن يكون الرقم العام أكثر من 3 أرقام
2025-06-22 18:51:05 - StoresApp - ERROR - UI Error - خطأ في البيانات: يجب أن يكون الرقم العام أكثر من 3 أرقام
2025-06-22 18:51:05 - StoresApp - ERROR - UI Error - خطأ في البيانات: يجب أن يكون الرقم العام أكثر من 3 أرقام
2025-06-22 18:51:05 - StoresApp - ERROR - UI Error - خطأ في البيانات: يجب أن يكون الرقم العام أكثر من 3 أرقام
2025-06-22 18:51:05 - StoresApp - ERROR - UI Error - خطأ في البيانات: يجب أن يكون الرقم العام أكثر من 3 أرقام
2025-06-22 18:51:05 - StoresApp - ERROR - UI Error - خطأ في البيانات: يجب أن يكون الرقم العام أكثر من 3 أرقام
2025-06-22 18:51:06 - StoresApp - ERROR - UI Error - خطأ في البيانات: يجب أن يكون الرقم العام أكثر من 3 أرقام
2025-06-22 18:51:45 - StoresApp - ERROR - UI Error - خطأ في البيانات: يرجى اختيار الوحدة
2025-06-22 18:51:45 - StoresApp - ERROR - UI Error - خطأ في البيانات: يرجى اختيار الوحدة
2025-06-22 18:51:46 - StoresApp - ERROR - UI Error - خطأ في البيانات: يرجى اختيار الوحدة
2025-06-22 18:51:48 - StoresApp - ERROR - UI Error - خطأ في البيانات: يرجى اختيار الوحدة
2025-06-22 18:51:48 - StoresApp - ERROR - UI Error - خطأ في البيانات: يرجى اختيار الوحدة
2025-06-22 18:51:54 - StoresApp - ERROR - UI Error - خطأ في البيانات: يرجى اختيار الوحدة
2025-06-22 18:51:54 - StoresApp - ERROR - UI Error - خطأ في البيانات: يرجى اختيار الوحدة
2025-06-22 18:51:54 - StoresApp - ERROR - UI Error - خطأ في البيانات: يرجى اختيار الوحدة
2025-06-22 18:51:55 - StoresApp - ERROR - UI Error - خطأ في البيانات: يرجى اختيار الوحدة
2025-06-22 18:51:55 - StoresApp - ERROR - UI Error - خطأ في البيانات: يرجى اختيار الوحدة
2025-06-22 18:51:55 - StoresApp - ERROR - UI Error - خطأ في البيانات: يرجى اختيار الوحدة
2025-06-22 18:51:55 - StoresApp - ERROR - UI Error - خطأ في البيانات: يرجى اختيار الوحدة
2025-06-22 18:51:56 - StoresApp - ERROR - UI Error - خطأ في البيانات: يرجى اختيار الوحدة
2025-06-22 18:51:56 - StoresApp - ERROR - UI Error - خطأ في البيانات: يرجى اختيار الوحدة
2025-06-22 19:08:46 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-22 19:08:52 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-22 19:11:27 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-22 19:11:29 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-22 19:11:30 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-22 19:11:31 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-22 19:11:32 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-22 19:11:32 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-22 19:11:35 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-22 19:11:38 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-22 19:11:38 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-22 19:11:38 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-22 19:15:47 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-22 19:15:53 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-22 19:16:33 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-22 19:16:35 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-22 19:16:36 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-22 19:16:36 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.number
2025-06-22 19:42:45 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-22 19:42:50 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-22 20:56:33 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-22 20:56:40 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-23 05:07:50 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-23 05:08:00 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-23 05:09:39 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة إضافة الهيئة: 'AddOrganizationWindow' object has no attribute 'on_unit_change'
2025-06-23 05:15:44 - StoresApp - ERROR - UI Error - خطأ: فشل في تصدير PDF: bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-23 05:15:46 - StoresApp - ERROR - UI Error - خطأ: فشل في تصدير PDF: bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-23 06:00:15 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-23 06:00:21 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-23 06:23:49 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-23 06:23:55 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-25 20:05:11 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-25 20:05:26 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-25 20:13:55 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-25 20:14:01 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-25 20:14:08 - StoresApp - ERROR - UI Error - خطأ: فشل في إنشاء نموذج البيانات: bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:14:12 - StoresApp - ERROR - UI Error - خطأ: فشل في تصدير البيانات: bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:14:23 - StoresApp - ERROR - UI Error - خطأ: فشل في إنشاء نموذج البيانات: bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:14:24 - StoresApp - ERROR - UI Error - خطأ: فشل في إنشاء نموذج البيانات: bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:14:24 - StoresApp - ERROR - UI Error - خطأ: فشل في تصدير البيانات: bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:14:29 - StoresApp - ERROR - UI Error - خطأ: فشل في إنشاء نموذج البيانات: bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:14:29 - StoresApp - ERROR - UI Error - خطأ: فشل في تصدير البيانات: bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:15:03 - StoresApp - ERROR - UI Error - خطأ: فشل في إنشاء نموذج البيانات: bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:17:56 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-25 20:18:02 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-25 20:18:07 - StoresApp - ERROR - UI Error - خطأ: فشل في إنشاء نموذج البيانات: bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:18:11 - StoresApp - ERROR - UI Error - خطأ: فشل في تصدير البيانات: bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:18:18 - StoresApp - ERROR - UI Error - خطأ: فشل في إنشاء نموذج البيانات: bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:18:19 - StoresApp - ERROR - UI Error - خطأ: فشل في إنشاء نموذج البيانات: bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:18:19 - StoresApp - ERROR - UI Error - خطأ: فشل في إنشاء نموذج البيانات: bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:18:19 - StoresApp - ERROR - UI Error - خطأ: فشل في إنشاء نموذج البيانات: bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:18:24 - StoresApp - ERROR - UI Error - خطأ: فشل في إنشاء التقرير: 'DisplayItem' object has no attribute 'minimum_quantity'
2025-06-25 20:18:40 - StoresApp - ERROR - UI Error - خطأ: فشل في إنشاء نموذج البيانات: bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:18:42 - StoresApp - ERROR - UI Error - خطأ: فشل في إنشاء التقرير: 'DisplayItem' object has no attribute 'minimum_quantity'
2025-06-25 20:21:36 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-25 20:21:42 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-25 20:21:46 - StoresApp - ERROR - UI Error - خطأ: فشل في إنشاء نموذج البيانات: bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:21:51 - StoresApp - ERROR - UI Error - خطأ: فشل في تصدير البيانات: bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:21:53 - StoresApp - ERROR - UI Error - خطأ: فشل في تصدير البيانات: bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:21:54 - StoresApp - ERROR - UI Error - خطأ: فشل في إنشاء نموذج البيانات: bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:21:54 - StoresApp - ERROR - UI Error - خطأ: فشل في تصدير البيانات: bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:21:55 - StoresApp - ERROR - UI Error - خطأ: فشل في إنشاء نموذج البيانات: bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:22:46 - StoresApp - ERROR - UI Error - خطأ: فشل في إنشاء التقرير: 'DisplayItem' object has no attribute 'minimum_quantity'
2025-06-25 20:24:00 - StoresApp - ERROR - UI Error - خطأ: رقم الصنف 101001 موجود بالفعل
2025-06-25 20:24:00 - StoresApp - ERROR - UI Error - خطأ: رقم الصنف 101001 موجود بالفعل
2025-06-25 20:24:01 - StoresApp - ERROR - UI Error - خطأ: رقم الصنف 101001 موجود بالفعل
2025-06-25 20:24:01 - StoresApp - ERROR - UI Error - خطأ: رقم الصنف 101001 موجود بالفعل
2025-06-25 20:29:04 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-25 20:29:10 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-25 20:29:15 - StoresApp - ERROR - UI Error - خطأ في إنشاء النموذج: فشل في إنشاء نموذج البيانات:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:29:17 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير البيانات:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:29:17 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير البيانات:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:29:17 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير البيانات:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:29:18 - StoresApp - ERROR - UI Error - خطأ في إنشاء النموذج: فشل في إنشاء نموذج البيانات:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:29:18 - StoresApp - ERROR - UI Error - خطأ في إنشاء النموذج: فشل في إنشاء نموذج البيانات:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:29:18 - StoresApp - ERROR - UI Error - خطأ في إنشاء النموذج: فشل في إنشاء نموذج البيانات:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:29:21 - StoresApp - ERROR - UI Error - خطأ في إنشاء النموذج: فشل في إنشاء نموذج البيانات:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:29:21 - StoresApp - ERROR - UI Error - خطأ في إنشاء النموذج: فشل في إنشاء نموذج البيانات:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:29:21 - StoresApp - ERROR - UI Error - خطأ في إنشاء النموذج: فشل في إنشاء نموذج البيانات:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:29:22 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير البيانات:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:29:34 - StoresApp - ERROR - UI Error - خطأ: فشل في تصدير التقرير: bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:40:31 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-25 20:40:37 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-25 20:40:42 - StoresApp - ERROR - UI Error - خطأ في إنشاء النموذج: فشل في إنشاء نموذج البيانات:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:40:44 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير البيانات:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:41:02 - StoresApp - ERROR - UI Error - خطأ: فشل في تصدير التقرير: bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:41:04 - StoresApp - ERROR - UI Error - خطأ: فشل في تصدير التقرير: bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:41:04 - StoresApp - ERROR - UI Error - خطأ: فشل في تصدير التقرير: bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:41:05 - StoresApp - ERROR - UI Error - خطأ: فشل في تصدير التقرير: bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:41:07 - StoresApp - ERROR - UI Error - خطأ في إنشاء النموذج: فشل في إنشاء نموذج البيانات:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:41:07 - StoresApp - ERROR - UI Error - خطأ في إنشاء النموذج: فشل في إنشاء نموذج البيانات:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:41:08 - StoresApp - ERROR - UI Error - خطأ في إنشاء النموذج: فشل في إنشاء نموذج البيانات:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:41:08 - StoresApp - ERROR - UI Error - خطأ في إنشاء النموذج: فشل في إنشاء نموذج البيانات:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:41:08 - StoresApp - ERROR - UI Error - خطأ في إنشاء النموذج: فشل في إنشاء نموذج البيانات:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:41:08 - StoresApp - ERROR - UI Error - خطأ في إنشاء النموذج: فشل في إنشاء نموذج البيانات:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:41:09 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير البيانات:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:41:09 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير البيانات:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:41:09 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير البيانات:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:41:20 - StoresApp - ERROR - UI Error - خطأ في إنشاء النموذج: فشل في إنشاء نموذج البيانات:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:41:20 - StoresApp - ERROR - UI Error - خطأ في إنشاء النموذج: فشل في إنشاء نموذج البيانات:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:41:20 - StoresApp - ERROR - UI Error - خطأ في إنشاء النموذج: فشل في إنشاء نموذج البيانات:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:41:21 - StoresApp - ERROR - UI Error - خطأ في إنشاء النموذج: فشل في إنشاء نموذج البيانات:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:41:21 - StoresApp - ERROR - UI Error - خطأ في إنشاء النموذج: فشل في إنشاء نموذج البيانات:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:41:22 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير البيانات:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:41:22 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير البيانات:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:41:22 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير البيانات:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:41:22 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير البيانات:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:41:23 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير البيانات:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:41:23 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير البيانات:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-06-25 20:46:19 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel11.!frame.!frame5.!treeview"
2025-06-25 20:46:20 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel11.!frame.!frame5.!treeview"
2025-06-25 21:01:40 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-25 21:01:46 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-25 23:11:22 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-25 23:11:28 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-25 23:11:41 - StoresApp - ERROR - UI Error - خطأ في إنشاء النموذج: فشل في إنشاء نموذج البيانات:
[Errno 13] Permission denied: 'C:/Users/<USER>/Desktop/نموذج_الأصناف.xlsx'
2025-06-25 23:12:48 - StoresApp - ERROR - UI Error - خطأ في إنشاء النموذج: فشل في إنشاء نموذج البيانات:
[Errno 13] Permission denied: 'C:/Users/<USER>/Desktop/نموذج_الأصناف.xlsx'
2025-06-25 23:24:16 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-25 23:24:22 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-25 23:27:29 - StoresApp - ERROR - UI Error - خطأ في إنشاء النموذج: فشل في إنشاء نموذج البيانات:
[Errno 13] Permission denied: 'C:/Users/<USER>/Desktop/نموذج_الأصناف.xlsx'
2025-06-25 23:30:22 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-25 23:30:29 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-25 23:32:05 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-25 23:32:12 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-25 23:33:48 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-25 23:33:54 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-25 23:46:55 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-25 23:47:01 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-26 00:02:50 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-26 00:02:57 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-26 00:06:46 - StoresApp - ERROR - UI Error - خطأ: الأعمدة المطلوبة مفقودة: اسم الصنف
2025-06-26 00:10:35 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-26 00:10:41 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-26 00:22:26 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 00:22:29 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 00:22:31 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 00:22:31 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 00:22:32 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 00:22:33 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 00:22:34 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 00:22:36 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 00:22:37 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 00:22:37 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 00:22:39 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 00:22:40 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 00:22:40 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 00:26:44 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-26 00:26:50 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-26 00:29:51 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe6.!treeview"
2025-06-26 00:31:07 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-26 00:31:15 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-26 00:34:16 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-26 00:34:25 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-26 00:39:29 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ غير متوقع:
invalid command name ".!frame3.!frame13.!treeview"
2025-06-26 00:44:50 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-26 00:44:56 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-26 00:51:04 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe6.!treeview"
2025-06-26 00:51:06 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 00:51:14 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 00:51:15 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 00:51:16 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 00:51:17 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 00:51:18 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 00:51:19 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 00:51:23 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe8.!treeview"
2025-06-26 00:51:26 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 00:51:27 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe12.!treeview"
2025-06-26 00:51:31 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 00:51:33 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 00:51:34 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 00:51:39 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 00:51:41 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 00:51:41 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 00:51:41 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 00:51:42 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 00:51:42 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 00:51:42 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 00:51:45 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 00:51:45 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 00:51:46 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 00:53:48 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe22.!treeview"
2025-06-26 00:56:04 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe6.!treeview"
2025-06-26 00:56:23 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe8.!treeview"
2025-06-26 00:56:27 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe12.!treeview"
2025-06-26 00:58:48 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe22.!treeview"
2025-06-26 01:00:14 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات الأصناف الأكثر استخداماً: no such column: i.item_number
2025-06-26 01:00:35 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe30.!treeview"
2025-06-26 01:00:48 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe34.!treeview"
2025-06-26 01:01:04 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe6.!treeview"
2025-06-26 01:01:23 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe8.!treeview"
2025-06-26 01:01:27 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe12.!treeview"
2025-06-26 01:01:43 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 01:08:07 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-26 01:08:13 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-26 01:08:27 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 01:08:36 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 01:08:40 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 01:08:43 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 01:08:45 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 01:08:47 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 01:08:51 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 01:08:53 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 01:08:54 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 01:08:54 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 01:08:56 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 01:08:59 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 01:09:01 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 01:09:02 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 01:13:45 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-26 01:13:51 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-26 01:17:28 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-26 01:17:35 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-26 01:17:46 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 01:17:49 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 01:17:52 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 01:17:53 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 01:17:54 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 01:17:54 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 01:18:17 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-26 01:18:26 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-26 01:20:44 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-26 01:20:53 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-26 01:25:28 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-26 01:25:37 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-26 01:28:51 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-26 01:28:59 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-26 01:29:13 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: u.symbol
2025-06-26 01:29:51 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-26 01:30:03 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-26 01:31:10 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-26 01:31:17 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-26 01:32:20 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.unit_id
2025-06-26 01:32:49 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.unit_id
2025-06-26 01:32:54 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.unit_id
2025-06-26 01:32:56 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.unit_id
2025-06-26 01:32:57 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.unit_id
2025-06-26 01:32:58 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.unit_id
2025-06-26 01:33:00 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.unit_id
2025-06-26 01:33:01 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.unit_id
2025-06-26 01:33:03 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.unit_id
2025-06-26 01:33:04 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.unit_id
2025-06-26 01:33:05 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.unit_id
2025-06-26 01:33:06 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.unit_id
2025-06-26 01:33:07 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.unit_id
2025-06-26 01:33:09 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.unit_id
2025-06-26 01:33:11 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.unit_id
2025-06-26 01:33:13 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.unit_id
2025-06-26 01:33:14 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.unit_id
2025-06-26 01:37:25 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-26 01:37:33 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-26 01:40:04 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-26 01:40:10 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-26 01:41:42 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-26 01:41:49 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-26 01:45:47 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-26 01:45:55 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-26 01:46:37 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-26 01:46:49 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-26 01:50:07 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-26 01:50:14 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-26 01:55:09 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-26 01:55:18 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-26 01:55:55 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-26 01:56:03 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-26 01:56:41 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-26 01:56:47 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-26 01:56:54 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.unit_id
2025-06-26 01:56:57 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.unit_id
2025-06-26 01:56:59 - StoresApp - ERROR - UI Error - خطأ: فشل في البحث: no such column: i.unit_id
2025-06-27 11:40:01 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-27 11:40:09 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-27 11:44:37 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-27 11:44:43 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-27 11:45:14 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-27 11:45:19 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-27 11:46:20 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-27 11:46:26 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-27 11:50:50 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-27 11:51:01 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-27 11:51:35 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-27 11:51:43 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-27 11:54:41 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-27 11:54:49 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-27 11:55:58 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-27 11:56:06 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-27 11:59:55 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-27 12:00:01 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-27 12:02:47 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-27 12:02:52 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-27 12:06:23 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-27 12:06:30 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-06-27 12:09:48 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-06-27 12:09:54 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
