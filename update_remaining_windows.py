#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحديث النوافذ المتبقية لإضافة مفاتيح الاختصار
Update Remaining Windows for Shortcuts
"""

import os
import re
from pathlib import Path

def update_window_file(file_path, class_name, window_attr='window'):
    """تحديث ملف نافذة لإضافة مفاتيح الاختصار"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"📁 معالجة {file_path}...")
        
        # 1. إضافة الاستيراد إذا لم يكن موجوداً
        if 'from ui.global_shortcuts import' not in content:
            # البحث عن آخر استيراد
            import_pattern = r'(from\s+[\w.]+\s+import\s+[^\n]+\n)'
            imports = re.findall(import_pattern, content)
            
            if imports:
                last_import = imports[-1]
                new_import = last_import + "from ui.global_shortcuts import GlobalShortcuts, ContextHandler\n"
                content = content.replace(last_import, new_import)
                print(f"  ✅ تم إضافة الاستيراد")
            else:
                print(f"  ❌ لم يتم العثور على استيرادات")
                return False
        else:
            print(f"  ✅ الاستيراد موجود بالفعل")
        
        # 2. إضافة دوال مفاتيح الاختصار إذا لم تكن موجودة
        if 'def setup_shortcuts(' not in content:
            # البحث عن نهاية الكلاس
            class_pattern = rf'class\s+{class_name}.*?(?=\n\nclass|\n\n#|\n\nif\s+__name__|\Z)'
            class_match = re.search(class_pattern, content, re.DOTALL)
            
            if class_match:
                class_content = class_match.group(0)
                
                # إضافة دوال مفاتيح الاختصار
                shortcuts_methods = f'''
    def setup_shortcuts(self):
        """إعداد مفاتيح الاختصار"""
        try:
            # إنشاء معالج السياق
            self.context_handler = ContextHandler()
            
            # تعيين دوال العمليات
            self.context_handler.set_save_callback(self.shortcut_save)
            self.context_handler.set_delete_callback(self.shortcut_delete)
            self.context_handler.set_copy_callback(self.shortcut_copy)
            self.context_handler.set_paste_callback(self.shortcut_paste)
            
            # تفعيل مفاتيح الاختصار
            target_window = getattr(self, '{window_attr}', None)
            if target_window:
                self.global_shortcuts = GlobalShortcuts(target_window, self.context_handler)
        except Exception as e:
            print(f"خطأ في إعداد مفاتيح الاختصار: {{e}}")
    
    def shortcut_save(self):
        """عملية الحفظ (F1)"""
        try:
            # البحث عن دوال الحفظ المتاحة
            save_methods = ['save_data', 'save_changes', 'save_item', 'save', 'add_item']
            for method_name in save_methods:
                if hasattr(self, method_name):
                    getattr(self, method_name)()
                    print(f"تم تنفيذ {{method_name}} بمفتاح F1")
                    return
            print("عملية الحفظ غير متاحة")
        except Exception as e:
            print(f"خطأ في عملية الحفظ: {{e}}")
    
    def shortcut_delete(self):
        """عملية الحذف (F2)"""
        try:
            # البحث عن دوال الحذف المتاحة
            delete_methods = ['delete_selected', 'delete_item', 'delete_data', 'delete']
            for method_name in delete_methods:
                if hasattr(self, method_name):
                    getattr(self, method_name)()
                    print(f"تم تنفيذ {{method_name}} بمفتاح F2")
                    return
            print("عملية الحذف غير متاحة")
        except Exception as e:
            print(f"خطأ في عملية الحذف: {{e}}")
    
    def shortcut_copy(self):
        """عملية النسخ (F3)"""
        try:
            # البحث عن دوال النسخ المتاحة
            copy_methods = ['copy_data', 'copy_selected', 'copy_item']
            for method_name in copy_methods:
                if hasattr(self, method_name):
                    getattr(self, method_name)()
                    print(f"تم تنفيذ {{method_name}} بمفتاح F3")
                    return
            
            # نسخ عامة
            import pyperclip
            pyperclip.copy("تم النسخ من النافذة")
            print("تم نسخ البيانات العامة")
        except Exception as e:
            print(f"خطأ في عملية النسخ: {{e}}")
    
    def shortcut_paste(self):
        """عملية اللصق (F4)"""
        try:
            # البحث عن دوال اللصق المتاحة
            paste_methods = ['paste_data', 'paste_item']
            for method_name in paste_methods:
                if hasattr(self, method_name):
                    getattr(self, method_name)()
                    print(f"تم تنفيذ {{method_name}} بمفتاح F4")
                    return
            
            # لصق عام
            import pyperclip
            clipboard_text = pyperclip.paste()
            if clipboard_text:
                print(f"تم لصق: {{clipboard_text[:50]}}")
            else:
                print("لا توجد بيانات في الحافظة")
        except Exception as e:
            print(f"خطأ في عملية اللصق: {{e}}")'''
                
                # إضافة الدوال في نهاية الكلاس
                new_class_content = class_content + shortcuts_methods
                content = content.replace(class_content, new_class_content)
                print(f"  ✅ تم إضافة دوال مفاتيح الاختصار")
            else:
                print(f"  ❌ لم يتم العثور على الكلاس {class_name}")
                return False
        else:
            print(f"  ✅ دوال مفاتيح الاختصار موجودة بالفعل")
        
        # 3. إضافة استدعاء setup_shortcuts في __init__
        if 'self.setup_shortcuts()' not in content:
            # البحث عن دالة __init__
            init_pattern = r'def __init__\(self.*?\n(.*?)(?=\n    def|\n\nclass|\Z)'
            init_match = re.search(init_pattern, content, re.DOTALL)
            
            if init_match:
                init_content = init_match.group(0)
                
                # إضافة الاستدعاء في نهاية __init__
                lines = init_content.split('\n')
                
                # البحث عن آخر سطر غير فارغ
                last_line_index = len(lines) - 1
                while last_line_index > 0 and not lines[last_line_index].strip():
                    last_line_index -= 1
                
                # إضافة الاستدعاء
                indent = '        '  # 8 مسافات للتبويب
                shortcuts_call = f"\n{indent}# تفعيل مفاتيح الاختصار\n{indent}self.setup_shortcuts()"
                
                lines.insert(last_line_index + 1, shortcuts_call)
                new_init_content = '\n'.join(lines)
                
                content = content.replace(init_content, new_init_content)
                print(f"  ✅ تم إضافة استدعاء setup_shortcuts")
            else:
                print(f"  ❌ لم يتم العثور على دالة __init__")
                return False
        else:
            print(f"  ✅ استدعاء setup_shortcuts موجود بالفعل")
        
        # حفظ الملف
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"  ✅ تم تحديث {file_path} بنجاح")
        return True
        
    except Exception as e:
        print(f"  ❌ خطأ في معالجة {file_path}: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 تحديث النوافذ المتبقية لإضافة مفاتيح الاختصار")
    print("=" * 60)
    
    # قائمة النوافذ المتبقية
    remaining_windows = [
        {
            'file': 'ui/add_item_window.py',
            'class': 'AddItemWindow',
            'window_attr': 'add_window'
        },
        {
            'file': 'ui/edit_item_window.py',
            'class': 'EditItemWindow', 
            'window_attr': 'edit_window'
        },
        {
            'file': 'ui/add_beneficiary_window.py',
            'class': 'AddBeneficiaryWindow',
            'window_attr': 'add_window'
        },
        {
            'file': 'ui/edit_beneficiary_window.py',
            'class': 'EditBeneficiaryWindow',
            'window_attr': 'edit_window'
        },
        {
            'file': 'ui/reports_window.py',
            'class': 'ReportsWindow',
            'window_attr': 'reports_window'
        },
        {
            'file': 'ui/login_window.py',
            'class': 'LoginWindow',
            'window_attr': 'login_window'
        }
    ]
    
    success_count = 0
    total_count = len(remaining_windows)
    
    for window_info in remaining_windows:
        file_path = window_info['file']
        class_name = window_info['class']
        window_attr = window_info['window_attr']
        
        if not os.path.exists(file_path):
            print(f"❌ الملف غير موجود: {file_path}")
            continue
        
        if update_window_file(file_path, class_name, window_attr):
            success_count += 1
    
    print(f"\n📊 النتائج:")
    print(f"✅ تم تحديث {success_count} من {total_count} ملف بنجاح")
    print(f"📋 مفاتيح الاختصار المفعلة:")
    print(f"   F1 - الحفظ")
    print(f"   F2 - الحذف")
    print(f"   F3 - النسخ") 
    print(f"   F4 - اللصق")

if __name__ == "__main__":
    main()
