#!/usr/bin/env python3
"""
شاشة إدارة الأصناف المستقلة
Standalone Inventory Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *

from config import APP_CONFIG, UI_CONFIG, get_message
from models import Item, Category, OrganizationalChart, AddedItem
from database import db_manager

class InventoryStandaloneWindow:
    """شاشة إدارة الأصناف المستقلة (نافذة منفصلة)"""

    def __init__(self, parent, main_window):
        self.parent = parent
        self.main_window = main_window
        self.inventory_window = None
        self.items_data = []
        self.filtered_data = []
        self.current_item = None

        # متغيرات البحث والفلترة
        self.search_var = tk.StringVar()
        self.category_filter_var = tk.StringVar()
        self.status_filter_var = tk.StringVar()
        self.stock_filter_var = tk.StringVar()

        # ربط أحداث البحث
        self.search_var.trace('w', self.on_search_change)
        self.category_filter_var.trace('w', self.on_filter_change)
        self.status_filter_var.trace('w', self.on_filter_change)
        self.stock_filter_var.trace('w', self.on_filter_change)

        self.setup_window()
        self.load_items_data()

    def setup_window(self):
        """إعداد النافذة المستقلة"""
        self.inventory_window = tk.Toplevel(self.parent)
        self.inventory_window.title("📦 إدارة الأصناف")
        self.inventory_window.geometry("1200x700")
        self.inventory_window.resizable(True, True)
        
        # توسيط النافذة
        self.center_window()
        
        # إعداد المحتوى
        self.setup_content()
        
        # جعل النافذة في المقدمة
        self.inventory_window.lift()
        self.inventory_window.focus_force()

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.inventory_window.update_idletasks()

        screen_width = self.inventory_window.winfo_screenwidth()
        screen_height = self.inventory_window.winfo_screenheight()

        window_width = 1200
        window_height = 700

        x = max(0, (screen_width - window_width) // 2)
        y = max(0, (screen_height - window_height) // 2)

        self.inventory_window.geometry(f"{window_width}x{window_height}+{x}+{y}")

    def setup_content(self):
        """إعداد محتوى النافذة"""
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.inventory_window)
        main_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)
        
        # العنوان والأزرار
        self.create_header(main_frame)
        
        # شريط البحث والفلترة
        self.create_search_filter_bar(main_frame)
        
        # جدول الأصناف
        self.create_items_table(main_frame)
        
        # شريط المعلومات
        self.create_info_bar(main_frame)

    def create_header(self, parent):
        """إنشاء العنوان والأزرار"""
        header_frame = ttk_bs.Frame(parent)
        header_frame.pack(fill=X, pady=(0, 20))

        # العنوان
        ttk_bs.Label(
            header_frame,
            text="📦 إدارة الأصناف",
            bootstyle="primary"
        ).pack(side=LEFT)

        # أزرار العمليات
        buttons_frame = ttk_bs.Frame(header_frame)
        buttons_frame.pack(side=RIGHT)

        ttk_bs.Button(
            buttons_frame,
            text="➕ إضافة صنف",
            command=self.add_item,
            bootstyle="success",
            width=18
        ).pack(side=LEFT, padx=2)

        ttk_bs.Button(
            buttons_frame,
            text="✏️ تعديل",
            command=self.edit_item,
            bootstyle="warning",
            width=15
        ).pack(side=LEFT, padx=2)

        ttk_bs.Button(
            buttons_frame,
            text="🗑️ حذف",
            command=self.delete_item,
            bootstyle="danger",
            width=18
        ).pack(side=LEFT, padx=2)

        ttk_bs.Button(
            buttons_frame,
            text="🔄 تحديث",
            command=self.refresh_data,
            bootstyle="secondary",
            width=15
        ).pack(side=LEFT, padx=2)

    def create_search_filter_bar(self, parent):
        """إنشاء شريط البحث والفلترة"""
        search_frame = ttk_bs.Frame(parent)
        search_frame.pack(fill=X, pady=(0, 20))

        # البحث
        ttk_bs.Label(search_frame, text="🔍 البحث:").pack(side=LEFT, padx=5)
        search_entry = ttk_bs.Entry(
            search_frame,
            textvariable=self.search_var,
            width=25
        )
        search_entry.pack(side=LEFT, padx=5)

        # فلتر الحالة
        ttk_bs.Label(search_frame, text="الحالة:").pack(side=LEFT, padx=(20, 5))
        status_combo = ttk_bs.Combobox(
            search_frame,
            textvariable=self.status_filter_var,
            values=["الكل", "نشط", "غير نشط"],
            width=15,
            state="readonly"
        )
        status_combo.set("الكل")
        status_combo.pack(side=LEFT, padx=5)

        # فلتر المخزون
        ttk_bs.Label(search_frame, text="المخزون:").pack(side=LEFT, padx=(20, 5))
        stock_combo = ttk_bs.Combobox(
            search_frame,
            textvariable=self.stock_filter_var,
            values=["الكل", "منخفض", "متوفر", "نفد"],
            width=15,
            state="readonly"
        )
        stock_combo.set("الكل")
        stock_combo.pack(side=LEFT, padx=5)

    def create_items_table(self, parent):
        """إنشاء جدول الأصناف"""
        table_frame = ttk_bs.Frame(parent)
        table_frame.pack(fill=BOTH, expand=True, pady=(0, 20))

        # أعمدة الجدول
        columns = ("id", "item_number", "item_name", "custody_type", "classification", "current_quantity")
        column_names = ("الرقم", "رقم الصنف", "اسم الصنف", "نوع العهدة", "التصنيف", "الكمية الحالية")

        self.tree = ttk_bs.Treeview(
            table_frame,
            columns=columns,
            show="headings",
            height=15
        )

        # تعيين عناوين الأعمدة
        for col, name in zip(columns, column_names):
            self.tree.heading(col, text=name)
            if col == "id":
                self.tree.column(col, width=80, anchor=CENTER)
            elif col == "item_number":
                self.tree.column(col, width=150, anchor=CENTER)
            elif col == "item_name":
                self.tree.column(col, width=250, anchor=E)
            elif col == "custody_type":
                self.tree.column(col, width=150, anchor=E)
            elif col == "classification":
                self.tree.column(col, width=200, anchor=E)
            elif col == "current_quantity":
                self.tree.column(col, width=120, anchor=CENTER)

        # شريط التمرير
        scrollbar = ttk_bs.Scrollbar(table_frame, orient=VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)

        # تخطيط الجدول
        self.tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)

        # ربط أحداث الجدول
        self.tree.bind("<Double-1>", self.on_item_double_click)
        self.tree.bind("<<TreeviewSelect>>", self.on_item_select)
        self.tree.bind("<Button-3>", self.show_context_menu)  # Right click

        # إنشاء القائمة المنبثقة
        self.create_context_menu()

    def create_info_bar(self, parent):
        """إنشاء شريط المعلومات"""
        info_frame = ttk_bs.Frame(parent)
        info_frame.pack(fill=X)

        self.info_label = ttk_bs.Label(
            info_frame,
            text="جاري تحميل البيانات...",
            bootstyle="secondary"
        )
        self.info_label.pack(side=LEFT)

        # معلومات الإحصائيات
        self.stats_label = ttk_bs.Label(
            info_frame,
            text="",
            bootstyle="info"
        )
        self.stats_label.pack(side=RIGHT)

    def create_context_menu(self):
        """إنشاء القائمة المنبثقة"""
        self.context_menu = tk.Menu(self.tree, tearoff=0)

        # إضافة عناصر القائمة
        self.context_menu.add_command(
            label="✏️ تعديل الصنف",
            command=self.edit_item_context
        )

        self.context_menu.add_separator()

        self.context_menu.add_command(
            label="🗑️ حذف الصنف",
            command=self.delete_item_context
        )

    def show_context_menu(self, event):
        """عرض القائمة المنبثقة"""
        # تحديد العنصر المحدد
        item = self.tree.identify_row(event.y)
        if item:
            self.tree.selection_set(item)
            self.tree.focus(item)
            # تحديث العنصر الحالي
            self.on_item_select(None)
            # عرض القائمة
            try:
                self.context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                self.context_menu.grab_release()

    def edit_item_context(self):
        """تعديل الصنف من القائمة المنبثقة"""
        self.edit_item()

    def delete_item_context(self):
        """حذف الصنف من القائمة المنبثقة"""
        self.delete_item()

    def load_items_data(self):
        """تحميل بيانات الأصناف المضافة فقط"""
        try:
            print("📊 بدء تحميل بيانات الأصناف...")
            
            # تحميل البيانات من AddedItem
            added_items = AddedItem.get_all()
            print(f"📊 تم تحميل {len(added_items)} صنف من قاعدة البيانات")

            # تحويل بيانات AddedItem إلى تنسيق للعرض
            self.items_data = []
            for added_item in added_items:
                # إنشاء كائن للعرض
                display_item = type('DisplayItem', (), {})()
                display_item.id = added_item.id
                display_item.item_number = added_item.item_number
                display_item.name = added_item.item_name
                display_item.item_name = added_item.item_name  # للتوافق
                display_item.custody_type = added_item.custody_type
                display_item.classification = added_item.classification
                display_item.current_quantity = added_item.current_quantity
                display_item.unit = added_item.unit
                display_item.data_entry_user = added_item.data_entry_user
                display_item.entry_date = added_item.entry_date
                display_item.is_active = added_item.is_active
                display_item.created_at = added_item.created_at
                display_item.updated_at = added_item.updated_at

                self.items_data.append(display_item)

            # تحديث البيانات المفلترة
            self.filtered_data = self.items_data.copy()
            
            # تحديث الجدول وشريط المعلومات
            self.update_table()
            self.update_info_bar()
            
            print(f"✅ تم تحميل {len(self.items_data)} صنف في النافذة المستقلة")
            
            # تحديث العنصر المحدد حالياً إذا كان موجوداً
            if self.current_item:
                # البحث عن العنصر المحدث في البيانات الجديدة
                updated_item = next(
                    (item for item in self.items_data if item.id == self.current_item.id), 
                    None
                )
                if updated_item:
                    self.current_item = updated_item
                    print(f"🔄 تم تحديث العنصر المحدد: {updated_item.name}")
                    
        except Exception as e:
            print(f"❌ خطأ في تحميل بيانات الأصناف: {e}")
            import traceback
            print(f"تفاصيل الخطأ: {traceback.format_exc()}")
            self.items_data = []
            self.filtered_data = []
            self.update_table()
            self.update_info_bar()

    def update_table(self):
        """تحديث جدول الأصناف"""
        try:
            print("🔄 بدء تحديث جدول الأصناف...")
            
            # حفظ العنصر المحدد حالياً
            selected_item_id = None
            selection = self.tree.selection()
            if selection:
                try:
                    selected_values = self.tree.item(selection[0])['values']
                    if selected_values:
                        selected_item_id = selected_values[0]
                except:
                    pass

            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)

            print(f"📊 إضافة {len(self.filtered_data)} عنصر للجدول...")

            # إضافة البيانات المفلترة
            for item in self.filtered_data:
                # تحديد لون الصف حسب مستوى المخزون
                tags = []
                current_qty = getattr(item, 'current_quantity', 0)
                if current_qty == 0:
                    tags.append("out_of_stock")
                elif current_qty <= 5:
                    tags.append("low_stock")

                # إدراج العنصر في الجدول
                tree_item = self.tree.insert("", "end", values=(
                    getattr(item, 'id', ''),
                    getattr(item, 'item_number', ''),
                    getattr(item, 'name', ''),
                    getattr(item, 'custody_type', ''),
                    getattr(item, 'classification', ''),
                    int(current_qty)  # تحويل إلى عدد صحيح
                ), tags=tags)

                # إعادة تحديد العنصر المحدد إذا كان موجوداً
                if selected_item_id and str(getattr(item, 'id', '')) == str(selected_item_id):
                    self.tree.selection_set(tree_item)
                    self.tree.focus(tree_item)

            # تعيين ألوان الصفوف
            self.tree.tag_configure("low_stock", background="#fff3cd")
            self.tree.tag_configure("out_of_stock", background="#f8d7da")

            print("✅ تم تحديث جدول الأصناف بنجاح")

        except Exception as e:
            print(f"❌ خطأ في تحديث جدول الأصناف: {e}")
            import traceback
            print(f"تفاصيل الخطأ: {traceback.format_exc()}")

    def update_info_bar(self):
        """تحديث شريط المعلومات"""
        total = len(self.items_data)
        filtered = len(self.filtered_data)
        active = len([i for i in self.items_data if getattr(i, 'is_active', True)])
        low_stock = len([i for i in self.items_data if getattr(i, 'current_quantity', 0) <= 5])
        out_of_stock = len([i for i in self.items_data if getattr(i, 'current_quantity', 0) == 0])

        self.info_label.config(text=f"عرض {filtered} من {total} صنف")
        self.stats_label.config(text=f"النشطة: {active} | منخفضة: {low_stock} | نفدت: {out_of_stock}")

    def on_search_change(self, *args):
        """معالج تغيير البحث"""
        self.apply_filters()

    def on_filter_change(self, *args):
        """معالج تغيير الفلاتر"""
        self.apply_filters()

    def apply_filters(self):
        """تطبيق الفلاتر والبحث"""
        search_text = self.search_var.get().lower()
        status_filter = self.status_filter_var.get()
        stock_filter = self.stock_filter_var.get()

        self.filtered_data = []

        for item in self.items_data:
            # فلتر البحث
            if search_text:
                if not any(search_text in str(getattr(item, field, "")).lower()
                          for field in ['name', 'item_number', 'custody_type', 'classification']):
                    continue

            # فلتر الحالة
            if status_filter and status_filter != "الكل":
                if status_filter == "نشط" and not item.is_active:
                    continue
                elif status_filter == "غير نشط" and item.is_active:
                    continue

            # فلتر المخزون
            if stock_filter and stock_filter != "الكل":
                current_qty = getattr(item, 'current_quantity', 0)
                if stock_filter == "منخفض" and current_qty > 5:
                    continue
                elif stock_filter == "متوفر" and current_qty <= 5:
                    continue
                elif stock_filter == "نفد" and current_qty > 0:
                    continue

            self.filtered_data.append(item)

        self.update_table()
        self.update_info_bar()

    def on_item_select(self, event):
        """معالج اختيار عنصر من الجدول"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            item_id = item['values'][0]
            self.current_item = next(
                (i for i in self.items_data if i.id == item_id), None
            )

    def on_item_double_click(self, event):
        """معالج النقر المزدوج على عنصر"""
        self.edit_item()

    def add_item(self):
        """إضافة صنف جديد"""
        try:
            from ui.add_item_window import AddItemWindow
            add_window = AddItemWindow(self.inventory_window, self)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح شاشة إضافة الصنف: {e}")

    def edit_item(self):
        """تعديل الصنف المحدد"""
        if not self.current_item:
            messagebox.showwarning("تحذير", "يرجى اختيار صنف للتعديل")
            return

        try:
            print(f"✏️ فتح نافذة تعديل الصنف: {self.current_item.name}")
            from ui.edit_item_window import EditItemWindow
            edit_window = EditItemWindow(self.inventory_window, self, self.current_item)
        except Exception as e:
            print(f"❌ خطأ في فتح نافذة التعديل: {e}")
            messagebox.showerror("خطأ", f"فشل في فتح شاشة تعديل الصنف: {e}")

    def check_item_can_be_deleted(self):
        """التحقق من إمكانية حذف الصنف"""
        try:
            # التحقق من وجود عمليات صرف مرتبطة بالصنف
            # البحث في جدول transaction_items مباشرة باستخدام item_id
            transactions_count = db_manager.fetch_one("""
                SELECT COUNT(DISTINCT transaction_id) as count
                FROM transaction_items
                WHERE item_id = ?
            """, (self.current_item.id,))

            if transactions_count and transactions_count['count'] > 0:
                # عرض رسالة خطأ باللون الأحمر
                self.show_error_message("الصنف مُسلم في عمليات صرف لمستفيدين ولا يمكن حذفه نهائياً")
                return False

            return True

        except Exception as e:
            print(f"خطأ في التحقق من إمكانية الحذف: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ أثناء التحقق من إمكانية الحذف: {e}")
            return False

    def show_error_message(self, message):
        """عرض رسالة خطأ باللون الأحمر تختفي بعد 3 ثواني"""
        # إنشاء نافذة رسالة مؤقتة
        error_window = tk.Toplevel(self.inventory_window)
        error_window.title("تحذير")
        error_window.configure(bg='white')
        error_window.resizable(False, False)

        # تحديد موقع النافذة في وسط الشاشة
        error_window.geometry("400x100")
        error_window.transient(self.inventory_window)
        error_window.grab_set()

        # إنشاء إطار للرسالة
        message_frame = tk.Frame(error_window, bg='white', padx=20, pady=20)
        message_frame.pack(fill=tk.BOTH, expand=True)

        # عرض الرسالة باللون الأحمر
        message_label = tk.Label(
            message_frame,
            text=message,
            font=("Arial", 12, "bold"),
            fg="red",
            bg="white",
            wraplength=350,
            justify="center"
        )
        message_label.pack(expand=True)

        # إغلاق النافذة بعد 3 ثواني
        error_window.after(3000, error_window.destroy)

        # إغلاق النافذة عند النقر في أي مكان
        def close_on_click(event):
            error_window.destroy()

        error_window.bind("<Button-1>", close_on_click)
        message_label.bind("<Button-1>", close_on_click)

    def delete_item(self):
        """حذف الصنف المحدد"""
        if not self.current_item:
            messagebox.showwarning("تحذير", "يرجى اختيار صنف للحذف")
            return

        # التحقق من إمكانية حذف الصنف
        if not self.check_item_can_be_deleted():
            return

        if messagebox.askyesno(
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف الصنف '{self.current_item.name}'؟\n\nهذا الإجراء لا يمكن التراجع عنه."
        ):
            try:
                # حذف من جدول الأصناف المضافة
                db_manager.execute_query("DELETE FROM added_items WHERE id = ?", (self.current_item.id,))
                messagebox.showinfo("نجح", "تم حذف الصنف بنجاح")
                self.refresh_data()
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ: {e}")

    def refresh_data(self):
        """تحديث البيانات"""
        print("🔄 بدء تحديث بيانات شاشة إدارة الأصناف...")
        
        # إعادة تحميل البيانات من قاعدة البيانات
        self.load_items_data()
        
        # تطبيق الفلاتر الحالية
        self.apply_filters()
        
        # تحديث شريط المعلومات
        self.update_info_bar()
        
        print("✅ تم تحديث بيانات شاشة إدارة الأصناف بنجاح")

# اختبار النافذة
if __name__ == "__main__":
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    # إنشاء نافذة الاختبار
    window = InventoryStandaloneWindow(root, None)
    root.mainloop()
