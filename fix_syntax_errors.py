#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح أخطاء بناء الجملة في ملفات الواجهة
Fix Syntax Errors in UI Files
"""

import re
import os

def fix_file_syntax_errors(file_path):
    """إصلاح أخطاء بناء الجملة في ملف"""
    print(f"🔧 إصلاح الأخطاء في {file_path}...")
    
    try:
        # قراءة الملف
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن السطور المشكلة وإصلاحها
        # البحث عن النمط: "from datetime import datetime\nfrom ui.global_shortcuts import..."
        pattern = r'(from datetime import datetime)\nfrom ui\.global_shortcuts import GlobalShortcuts, ContextHandler'
        replacement = r'\1'
        
        # تطبيق الإصلاح
        fixed_content = re.sub(pattern, replacement, content)
        
        # البحث عن النمط الآخر: "import datetime\nfrom ui.global_shortcuts import..."
        pattern2 = r'(import datetime)\nfrom ui\.global_shortcuts import GlobalShortcuts, ContextHandler'
        replacement2 = r'\1'
        
        fixed_content = re.sub(pattern2, replacement2, fixed_content)
        
        # البحث عن النمط الثالث: سطر منفصل
        pattern3 = r'^from ui\.global_shortcuts import GlobalShortcuts, ContextHandler\n'
        replacement3 = r''
        
        fixed_content = re.sub(pattern3, replacement3, fixed_content, flags=re.MULTILINE)
        
        # حفظ الملف المُصلح
        if fixed_content != content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(fixed_content)
            print(f"✅ تم إصلاح {file_path}")
            return True
        else:
            print(f"ℹ️ لا توجد أخطاء في {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في إصلاح {file_path}: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء إصلاح أخطاء بناء الجملة...")
    print("=" * 50)
    
    # قائمة الملفات للإصلاح
    files_to_fix = [
        "ui/transactions_window.py",
        "ui/reports_window.py", 
        "ui/inventory_window.py"
    ]
    
    fixed_count = 0
    
    for file_path in files_to_fix:
        if os.path.exists(file_path):
            if fix_file_syntax_errors(file_path):
                fixed_count += 1
        else:
            print(f"⚠️ الملف غير موجود: {file_path}")
    
    print("\n" + "=" * 50)
    print(f"📊 النتيجة النهائية: تم إصلاح {fixed_count} ملف")
    
    if fixed_count > 0:
        print("🎉 تم إصلاح جميع الأخطاء!")
        print("💡 يمكنك الآن تشغيل الاختبار مرة أخرى")
    else:
        print("ℹ️ لم يتم العثور على أخطاء للإصلاح")

if __name__ == "__main__":
    main()
