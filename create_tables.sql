-- إنشاء جدول التصنيفات
-- Create Categories Table

-- جدول التصنيفات
CREATE TABLE IF NOT EXISTS categories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- إضافة عمود التصنيف لجدول الأصناف (إذا لم يكن موجوداً)
ALTER TABLE items ADD COLUMN category_id INTEGER REFERENCES categories(id);

-- إدراج بيانات أولية للتصنيفات
INSERT OR IGNORE INTO categories (name, description) VALUES
('أدوات مكتبية', 'أقلام، أوراق، مجلدات'),
('أجهزة إلكترونية', 'حاسوب، طابعات، شاشات'),
('مواد استهلاكية', 'مواد تنظيف، قرطاسية'),
('أثاث مكتبي', 'مكاتب، كراسي، خزائن'),
('معدات أمنية', 'كاميرات، أجهزة إنذار');



-- إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_categories_name ON categories(name);
CREATE INDEX IF NOT EXISTS idx_categories_active ON categories(is_active);
CREATE INDEX IF NOT EXISTS idx_items_category ON items(category_id);

-- إنشاء جدول الوحدات
-- Create Units Table
CREATE TABLE IF NOT EXISTS units (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- لا يتم إدراج بيانات أولية للوحدات - يتم إضافة الوحدات يدوياً من خلال الواجهة

-- إنشاء فهارس للوحدات
CREATE INDEX IF NOT EXISTS idx_units_name ON units(name);
CREATE INDEX IF NOT EXISTS idx_units_active ON units(is_active);